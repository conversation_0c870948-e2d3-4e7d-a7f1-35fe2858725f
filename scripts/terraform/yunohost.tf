resource "digitalocean_droplet" "yunohost" {
  count    = var.create_yunohost ? 1 : 0
  image    = "debian-12-x64"
  name     = var.yunohost_hostname
  region   = var.region
  size     = var.yunohost_droplet_size
  ssh_keys = [digitalocean_ssh_key.community_box.fingerprint]
  tags     = [digitalocean_tag.community_box.id, "yunohost"]

  user_data = <<-EOF
    #!/bin/bash

    # Update system
    apt-get update
    DEBIAN_FRONTEND=noninteractive apt-get upgrade -y

    # Install required packages
    DEBIAN_FRONTEND=noninteractive apt-get install -y curl wget git

    # Set hostname
    hostnamectl set-hostname ${var.yunohost_hostname}

    # Install Yunohost
    echo "Starting YunoHost installation..."
    curl https://install.yunohost.org > /root/install_yunohost.sh
    chmod +x /root/install_yunohost.sh
    /root/install_yunohost.sh -a

    # Add a message to indicate installation is complete
    echo "Yunohost installation completed. Access the web admin at https://SERVER_IP/yunohost/admin" > /root/INSTALLATION_COMPLETE.txt
  EOF

  # Ensure the volume is properly formatted and mounted
  # Wait for SSH to become available using the key that works manually
  provisioner "local-exec" {
    command = <<-EOT
      echo "Testing SSH key permissions..."
      ls -la /home/<USER>/.ssh/id_rsa
      echo "Waiting for SSH to become available..."
      until ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i /home/<USER>/.ssh/id_rsa -o ConnectTimeout=10 root@${self.ipv4_address} 'echo SSH ready'; do
        echo 'Waiting for SSH...'
        sleep 10
      done
    EOT
  }

  # Check if cloud-init has completed
  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for cloud-init to complete..."
      # Wait for cloud-init to complete with a timeout of 10 minutes
      for i in {1..60}; do
        status=$(ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i /home/<USER>/.ssh/id_rsa root@${self.ipv4_address} 'cloud-init status')
        echo "Cloud-init status: $status"
        if [[ "$status" == *"done"* ]]; then
          echo "Cloud-init completed successfully."
          break
        fi
        if [[ "$status" == *"error"* ]]; then
          echo "Cloud-init failed with an error."
          break
        fi
        echo "Waiting for cloud-init to complete... ($i/60)"
        sleep 10
      done
    EOT
  }

  # Check if YunoHost is installed
  provisioner "local-exec" {
    command = <<-EOT
      echo "Checking if YunoHost is installed..."
      # Wait for YunoHost installation to complete with a timeout of 10 minutes
      for i in {1..60}; do
        if ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -i /home/<USER>/.ssh/id_rsa root@${self.ipv4_address} 'test -f /root/INSTALLATION_COMPLETE.txt && echo "YunoHost installation completed."'; then
          echo "YunoHost installation completed successfully."
          break
        fi
        echo "Waiting for YunoHost installation to complete... ($i/60)"
        sleep 10
      done
    EOT
  }

  # Verify YunoHost installation
  provisioner "remote-exec" {
    inline = [
      "echo 'Verifying YunoHost installation...'",
      "if [ -f /usr/bin/yunohost ]; then",
      "  echo 'YunoHost is installed!'",
      "  yunohost --version",
      "else",
      "  echo 'YunoHost is not installed yet. Installing now...'",
      "  if [ -f /root/install_yunohost.sh ]; then",
      "    echo 'Running existing installation script...'",
      "    chmod +x /root/install_yunohost.sh",
      "    DEBIAN_FRONTEND=noninteractive /root/install_yunohost.sh -a",
      "  else",
      "    echo 'Downloading and running installation script...'",
      "    curl https://install.yunohost.org > /root/install_yunohost.sh",
      "    chmod +x /root/install_yunohost.sh",
      "    DEBIAN_FRONTEND=noninteractive /root/install_yunohost.sh -a",
      "  fi",
      "  echo 'YunoHost installation completed. Access the web admin at https://$(hostname -I | awk \"{print \\$1}\")/yunohost/admin' > /root/INSTALLATION_COMPLETE.txt",
      "fi"
    ]

    connection {
      type        = "ssh"
      user        = "root"
      host        = self.ipv4_address
      private_key = file("/home/<USER>/.ssh/id_rsa")
      agent       = false
      timeout     = "10m"
    }
  }


}
