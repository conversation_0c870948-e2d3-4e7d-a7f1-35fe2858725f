name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main  # Set this to your default branch
  workflow_dispatch:  # Allow manual triggering

permissions:
  contents: write  # Changed from read to write to allow committing data changes
  pages: write
  id-token: write

# Allow only one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js for data fetching
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: './scripts/fetch-data/package-lock.json'

      - name: Install data fetching dependencies
        run: npm install --frozen-lockfile
        working-directory: ./scripts/fetch-data

      - name: Run data fetching scripts
        run: npm run fetch-all
        working-directory: ./scripts/fetch-data

      - name: Commit and push data changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add research/fetched-data/
          if git diff --staged --quiet; then
            echo "No data changes to commit"
          else
            git commit -m "chore: update fetched data [skip ci]"
            git push
          fi

      - name: Setup Bun for app build
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install app dependencies
        run: bun install --frozen-lockfile
        working-directory: ./app

      - name: Build app
        run: bun run build
        working-directory: ./app
        env:
          NODE_ENV: production

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: './app/dist'

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
