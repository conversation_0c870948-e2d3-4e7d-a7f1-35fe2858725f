{"timestamp": "2025-06-11T21:33:18.461Z", "script": "scripts.test.js", "source": "YunoHost-Apps GitHub Organization", "url": "https://github.com/YunoHost-Apps", "api_url": "https://api.github.com/orgs/YunoHost-Apps/repos", "total_apps": 860, "total_repos_checked": 878, "languages": ["CSS", "HTML", "JavaScript", "<PERSON><PERSON>", "PHP", "<PERSON><PERSON>", "Python", "<PERSON>", "Shell", "TSQL"], "archived_count": 129, "data": [{"id": "13ft_ynh", "name": "13ft", "description": "13ft package for YunoHost", "url": "https://github.com/YunoHost-Apps/13ft_ynh", "stars": 8, "forks": 3, "updated_at": "2024-12-27T14:02:13Z", "language": "Shell", "archived": false}, {"id": "20euros_ynh", "name": "20euros", "description": "2048 game clone for YunoHost", "url": "https://github.com/YunoHost-Apps/20euros_ynh", "stars": 1, "forks": 1, "updated_at": "2024-09-01T10:34:05Z", "language": "CSS", "archived": false}, {"id": "243-game_ynh", "name": "243-game", "description": "2048 clone package for YunoHost", "url": "https://github.com/YunoHost-Apps/243-game_ynh", "stars": 0, "forks": 2, "updated_at": "2025-02-15T08:44:31Z", "language": "Shell", "archived": false}, {"id": "299ko_ynh", "name": "299ko", "description": "299Ko package for YunoHost", "url": "https://github.com/YunoHost-Apps/299ko_ynh", "stars": 1, "forks": 3, "updated_at": "2025-03-10T12:22:06Z", "language": "Shell", "archived": false}, {"id": "2FAuth_ynh", "name": "2<PERSON>uth", "description": "2FAuth package for YunoHost", "url": "https://github.com/YunoHost-Apps/2FAuth_ynh", "stars": 3, "forks": 5, "updated_at": "2025-04-12T14:31:11Z", "language": "Shell", "archived": false}, {"id": "abantecart_ynh", "name": "abantecart", "description": "Créateur de site Ecommerce", "url": "https://github.com/YunoHost-Apps/abantecart_ynh", "stars": 4, "forks": 5, "updated_at": "2025-05-02T20:26:16Z", "language": "Shell", "archived": false}, {"id": "acropolis_ynh", "name": "acropolis", "description": "Acropolis package for YunoHost", "url": "https://github.com/YunoHost-Apps/acropolis_ynh", "stars": 3, "forks": 6, "updated_at": "2024-06-23T11:28:28Z", "language": "Shell", "archived": false}, {"id": "actual_ynh", "name": "actual", "description": "Actual package for YunoHost", "url": "https://github.com/YunoHost-Apps/actual_ynh", "stars": 5, "forks": 5, "updated_at": "2025-05-14T16:37:32Z", "language": "Shell", "archived": false}, {"id": "adguardhome_ynh", "name": "adguardhome", "description": "AdGuard Home package for YunoHost: Network-wide ads & trackers blocking DNS server", "url": "https://github.com/YunoHost-Apps/adguardhome_ynh", "stars": 16, "forks": 16, "updated_at": "2025-05-30T23:00:25Z", "language": "Shell", "archived": false}, {"id": "adminer_ynh", "name": "adminer", "description": "Adminer package for YunoHost", "url": "https://github.com/YunoHost-Apps/adminer_ynh", "stars": 4, "forks": 5, "updated_at": "2025-03-01T13:59:58Z", "language": "Shell", "archived": false}, {"id": "adminerevo_ynh", "name": "adminerevo", "description": "AdminerEvo package for YunoHost", "url": "https://github.com/YunoHost-Apps/adminerevo_ynh", "stars": 1, "forks": 0, "updated_at": "2024-11-19T10:05:58Z", "language": "Shell", "archived": false}, {"id": "aeneria_ynh", "name": "aeneria", "description": "æneria package for YunoHost", "url": "https://github.com/YunoHost-Apps/aeneria_ynh", "stars": 11, "forks": 2, "updated_at": "2024-08-16T12:09:49Z", "language": "Shell", "archived": false}, {"id": "agendav_ynh", "name": "agendav", "description": "AgenDAV package for YunoHost", "url": "https://github.com/YunoHost-Apps/agendav_ynh", "stars": 3, "forks": 13, "updated_at": "2025-05-18T19:43:30Z", "language": "Shell", "archived": false}, {"id": "agewasm_ynh", "name": "agewasm", "description": "AgeWasm package for YunoHost", "url": "https://github.com/YunoHost-Apps/agewasm_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-22T07:03:00Z", "language": "Shell", "archived": false}, {"id": "agora_ynh", "name": "agora", "description": "Create your private space for your team and share files", "url": "https://github.com/YunoHost-Apps/agora_ynh", "stars": 2, "forks": 2, "updated_at": "2025-04-20T07:34:47Z", "language": "Shell", "archived": false}, {"id": "agorakit_ynh", "name": "agorakit", "description": "Agorakit package for YunoHost", "url": "https://github.com/YunoHost-Apps/agorakit_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-03T16:33:52Z", "language": "Shell", "archived": false}, {"id": "airsonic_ynh", "name": "airsonic", "description": "Web-based media streamer", "url": "https://github.com/YunoHost-Apps/airsonic_ynh", "stars": 10, "forks": 8, "updated_at": "2025-01-13T17:59:18Z", "language": "Shell", "archived": false}, {"id": "akaunting_ynh", "name": "akaunting", "description": "Akaunting package for YunoHost", "url": "https://github.com/YunoHost-Apps/akaunting_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-08T18:22:51Z", "language": "Shell", "archived": false}, {"id": "akkoma_ynh", "name": "akkoma", "description": "Social media based on ActivityPub, fork of Pleroma", "url": "https://github.com/YunoHost-Apps/akkoma_ynh", "stars": 15, "forks": 7, "updated_at": "2025-04-09T10:23:27Z", "language": "Shell", "archived": false}, {"id": "alltube_ynh", "name": "alltube", "description": "Youtube downloader front end package for YunoHost", "url": "https://github.com/YunoHost-Apps/alltube_ynh", "stars": 16, "forks": 2, "updated_at": "2024-05-15T17:56:39Z", "language": "Shell", "archived": true}, {"id": "ampache_ynh", "name": "ampache", "description": "Ampache package for YunoHost", "url": "https://github.com/YunoHost-Apps/ampache_ynh", "stars": 17, "forks": 16, "updated_at": "2025-06-06T06:27:34Z", "language": "PHP", "archived": false}, {"id": "anarchism_ynh", "name": "anarchism", "description": "This is an anarchist FAQ", "url": "https://github.com/YunoHost-Apps/anarchism_ynh", "stars": 3, "forks": 2, "updated_at": "2025-05-09T19:00:14Z", "language": "Shell", "archived": false}, {"id": "anfora_ynh", "name": "an<PERSON>ra", "description": "Self-hosted photo gallery federated social network for YunoHost", "url": "https://github.com/YunoHost-Apps/anfora_ynh", "stars": 2, "forks": 1, "updated_at": "2024-06-19T09:18:03Z", "language": "Shell", "archived": true}, {"id": "answer_ynh", "name": "answer", "description": "Answer package fot YunoHost", "url": "https://github.com/YunoHost-Apps/answer_ynh", "stars": 1, "forks": 0, "updated_at": "2025-03-20T08:34:10Z", "language": "Shell", "archived": false}, {"id": "antigen_ynh", "name": "antigen", "description": "Antigen package for YunoHost", "url": "https://github.com/YunoHost-Apps/antigen_ynh", "stars": 1, "forks": 0, "updated_at": "2025-05-14T20:00:20Z", "language": "Shell", "archived": false}, {"id": "anubis_ynh", "name": "anubis", "description": "Anubis package for YunoHost", "url": "https://github.com/YunoHost-Apps/anubis_ynh", "stars": 4, "forks": 0, "updated_at": "2025-05-10T17:54:12Z", "language": "Shell", "archived": false}, {"id": "appflowy_ynh", "name": "appflowy", "description": "", "url": "https://github.com/YunoHost-Apps/appflowy_ynh", "stars": 3, "forks": 2, "updated_at": "2025-06-10T07:47:02Z", "language": "Shell", "archived": false}, {"id": "archivebox_ynh", "name": "archivebox", "description": "Self-hosted internet archiving solution to collect, save, and view sites you want to preserve offline, for YunoHost.", "url": "https://github.com/YunoHost-Apps/archivebox_ynh", "stars": 18, "forks": 5, "updated_at": "2025-05-08T02:24:54Z", "language": "Shell", "archived": false}, {"id": "archivist_ynh", "name": "archivist", "description": "", "url": "https://github.com/YunoHost-Apps/archivist_ynh", "stars": 15, "forks": 4, "updated_at": "2025-04-19T06:19:19Z", "language": "Shell", "archived": false}, {"id": "argos_ynh", "name": "argos", "description": "Argos package for YunoHost", "url": "https://github.com/YunoHost-Apps/argos_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-29T17:32:53Z", "language": "Shell", "archived": false}, {"id": "armadietto_ynh", "name": "armadietto", "description": "Armadietto package for YunoHost, a remoteStorage server", "url": "https://github.com/YunoHost-Apps/armadietto_ynh", "stars": 2, "forks": 2, "updated_at": "2025-03-24T20:28:58Z", "language": "Shell", "archived": false}, {"id": "arn_messager_ynh", "name": "arn_messager", "description": "ARN Messager package for Yunohost", "url": "https://github.com/YunoHost-Apps/arn_messager_ynh", "stars": 0, "forks": 1, "updated_at": "2024-09-04T09:58:37Z", "language": "Shell", "archived": false}, {"id": "askbot_ynh", "name": "askbot", "description": "Askbot Yunohost package", "url": "https://github.com/YunoHost-Apps/askbot_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:50:39Z", "language": "Shell", "archived": true}, {"id": "audiobookshelf_ynh", "name": "audiobookshelf", "description": "Audiobookshelf package for YunoHost", "url": "https://github.com/YunoHost-Apps/audiobookshelf_ynh", "stars": 5, "forks": 6, "updated_at": "2025-06-02T07:31:40Z", "language": "Shell", "archived": false}, {"id": "autobd_ynh", "name": "autobd", "description": "Autobd package for YunoHost", "url": "https://github.com/YunoHost-Apps/autobd_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-19T23:22:49Z", "language": "Shell", "archived": false}, {"id": "autobrr_ynh", "name": "autobrr", "description": "Autobrr package for YunoHost", "url": "https://github.com/YunoHost-Apps/autobrr_ynh", "stars": 2, "forks": 1, "updated_at": "2025-05-19T20:10:30Z", "language": "Shell", "archived": false}, {"id": "automad_ynh", "name": "automad", "description": "Automad package for YunoHost", "url": "https://github.com/YunoHost-Apps/automad_ynh", "stars": 4, "forks": 2, "updated_at": "2024-10-04T06:37:01Z", "language": "Shell", "archived": false}, {"id": "awx_ynh", "name": "awx", "description": "", "url": "https://github.com/YunoHost-Apps/awx_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:57:19Z", "language": "Shell", "archived": true}, {"id": "backdrop_ynh", "name": "backdrop", "description": "Backdrop package for YunoHost", "url": "https://github.com/YunoHost-Apps/backdrop_ynh", "stars": 1, "forks": 2, "updated_at": "2025-05-19T20:00:34Z", "language": "PHP", "archived": false}, {"id": "backrest_ynh", "name": "backrest", "description": "Backrest package for YunoHost", "url": "https://github.com/YunoHost-Apps/backrest_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-06T21:57:30Z", "language": "Shell", "archived": false}, {"id": "baikal_ynh", "name": "baikal", "description": "Baïkal package for YunoHost", "url": "https://github.com/YunoHost-Apps/baikal_ynh", "stars": 9, "forks": 16, "updated_at": "2025-05-18T19:43:31Z", "language": "Shell", "archived": false}, {"id": "bazarr_ynh", "name": "bazarr", "description": "Companion for Sonarr and Radarr that manages and downloads subtitles, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/bazarr_ynh", "stars": 1, "forks": 2, "updated_at": "2025-05-12T06:41:13Z", "language": "Shell", "archived": false}, {"id": "beatbump_ynh", "name": "beatbump", "description": "Beatbump package for YunoHost", "url": "https://github.com/YunoHost-Apps/beatbump_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T18:04:50Z", "language": "Shell", "archived": false}, {"id": "beehive_ynh", "name": "beehive", "description": "A flexible event/agent & automation system", "url": "https://github.com/YunoHost-Apps/beehive_ynh", "stars": 5, "forks": 2, "updated_at": "2024-07-27T15:00:55Z", "language": "Shell", "archived": false}, {"id": "beneval<PERSON>re_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bénévalibe package for YunoHost", "url": "https://github.com/YunoHost-Apps/benevalibre_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-26T11:21:47Z", "language": "Shell", "archived": false}, {"id": "bibliogram_ynh", "name": "bibliogram", "description": "Bibliogram package for YunoHost", "url": "https://github.com/YunoHost-Apps/bibliogram_ynh", "stars": 5, "forks": 1, "updated_at": "2024-06-19T09:16:23Z", "language": "Shell", "archived": true}, {"id": "biboumi_ynh", "name": "biboumi", "description": "XMPP gateway for the IRC network, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/biboumi_ynh", "stars": 6, "forks": 2, "updated_at": "2025-05-14T11:44:28Z", "language": "Shell", "archived": false}, {"id": "BicBucStriim_ynh", "name": "BicBucStriim", "description": "BicBucStriim package for YunoHost", "url": "https://github.com/YunoHost-Apps/BicBucStriim_ynh", "stars": 4, "forks": 2, "updated_at": "2025-05-01T13:30:26Z", "language": "Shell", "archived": false}, {"id": "bileto_ynh", "name": "bileto", "description": "Bileto package for YunoHost", "url": "https://github.com/YunoHost-Apps/bileto_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-07T08:43:36Z", "language": "Shell", "archived": false}, {"id": "bloat_ynh", "name": "bloat", "description": "", "url": "https://github.com/YunoHost-Apps/bloat_ynh", "stars": 0, "forks": 1, "updated_at": "2024-12-02T15:04:28Z", "language": "Shell", "archived": false}, {"id": "blocky_ynh", "name": "blocky", "description": "Blocky package for YunoHost", "url": "https://github.com/YunoHost-Apps/blocky_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-03T17:38:23Z", "language": "Shell", "archived": false}, {"id": "blogotext_ynh", "name": "blogotext", "description": "Blogotext package for YunoHost", "url": "https://github.com/YunoHost-Apps/blogotext_ynh", "stars": 7, "forks": 2, "updated_at": "2025-03-26T18:30:24Z", "language": "Shell", "archived": false}, {"id": "bludit_ynh", "name": "bludit", "description": "Bludit package for YunoHost", "url": "https://github.com/YunoHost-Apps/bludit_ynh", "stars": 3, "forks": 1, "updated_at": "2024-10-01T19:33:13Z", "language": "Shell", "archived": false}, {"id": "bonfire_ynh", "name": "bonfire", "description": "Bonfire federated social network platform", "url": "https://github.com/YunoHost-Apps/bonfire_ynh", "stars": 1, "forks": 2, "updated_at": "2024-09-03T21:35:35Z", "language": "Shell", "archived": false}, {"id": "bookstack_ynh", "name": "bookstack", "description": "BookStack package for YunoHost", "url": "https://github.com/YunoHost-Apps/bookstack_ynh", "stars": 12, "forks": 6, "updated_at": "2025-05-31T17:17:05Z", "language": "Shell", "archived": false}, {"id": "bookwyrm_ynh", "name": "bookwyrm", "description": "Bookwyrm package for YunoHost", "url": "https://github.com/YunoHost-Apps/bookwyrm_ynh", "stars": 9, "forks": 6, "updated_at": "2025-04-29T19:14:24Z", "language": "Shell", "archived": false}, {"id": "borg_ynh", "name": "borg", "description": "An experimental Borg implementation for YunoHost", "url": "https://github.com/YunoHost-Apps/borg_ynh", "stars": 20, "forks": 26, "updated_at": "2025-04-03T16:25:22Z", "language": "Shell", "archived": false}, {"id": "borgserver_ynh", "name": "borgserver", "description": "Borg server package for YunoHost", "url": "https://github.com/YunoHost-Apps/borgserver_ynh", "stars": 9, "forks": 7, "updated_at": "2025-05-06T06:55:08Z", "language": "Shell", "archived": false}, {"id": "borgwarehouse_ynh", "name": "borgwarehouse", "description": "BorgWarHouse package for YunoHost", "url": "https://github.com/YunoHost-Apps/borgwarehouse_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-02T07:44:12Z", "language": "Shell", "archived": false}, {"id": "bozon_ynh", "name": "bozon", "description": "BoZon package for YunoHost", "url": "https://github.com/YunoHost-Apps/bozon_ynh", "stars": 7, "forks": 5, "updated_at": "2024-06-23T11:28:59Z", "language": "Shell", "archived": false}, {"id": "breezewiki_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "BreezeWiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/breezewiki_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-03T14:35:26Z", "language": "Shell", "archived": false}, {"id": "buckets_ynh", "name": "buckets", "description": "Budget with Buckets Relay Server", "url": "https://github.com/YunoHost-Apps/buckets_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T18:04:22Z", "language": "Shell", "archived": false}, {"id": "cac-proxy_ynh", "name": "cac-proxy", "description": "Enable deployment of Cookie Aware Cors Proxy to YunoHost", "url": "https://github.com/YunoHost-Apps/cac-proxy_ynh", "stars": 3, "forks": 1, "updated_at": "2024-09-16T16:33:20Z", "language": "Shell", "archived": false}, {"id": "cachet_ynh", "name": "cachet", "description": "Cachet package for YunoHost", "url": "https://github.com/YunoHost-Apps/cachet_ynh", "stars": 8, "forks": 2, "updated_at": "2025-01-13T17:57:22Z", "language": "Shell", "archived": false}, {"id": "caerp_ynh", "name": "caerp", "description": "enDI package for YunoHost", "url": "https://github.com/YunoHost-Apps/caerp_ynh", "stars": 0, "forks": 0, "updated_at": "2024-07-15T07:49:12Z", "language": "Shell", "archived": false}, {"id": "calckey_ynh", "name": "calckey", "description": "Calcket package for YunoHost", "url": "https://github.com/YunoHost-Apps/calckey_ynh", "stars": 9, "forks": 7, "updated_at": "2024-08-06T08:20:04Z", "language": "Shell", "archived": false}, {"id": "calcom_ynh", "name": "calcom", "description": "cal.com package for YunoHost", "url": "https://github.com/YunoHost-Apps/calcom_ynh", "stars": 0, "forks": 2, "updated_at": "2025-01-31T00:34:50Z", "language": "Shell", "archived": false}, {"id": "calibreweb_ynh", "name": "calibreweb", "description": "Calibre-web package for YunoHost", "url": "https://github.com/YunoHost-Apps/calibreweb_ynh", "stars": 19, "forks": 7, "updated_at": "2025-05-15T19:48:48Z", "language": "Shell", "archived": false}, {"id": "carreau_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Carreau package for YunoHost", "url": "https://github.com/YunoHost-Apps/carreau_ynh", "stars": 0, "forks": 0, "updated_at": "2024-09-25T22:11:45Z", "language": "Shell", "archived": false}, {"id": "castopod_ynh", "name": "castopod", "description": "Castopod package for YunoHost", "url": "https://github.com/YunoHost-Apps/castopod_ynh", "stars": 24, "forks": 7, "updated_at": "2025-05-22T22:34:22Z", "language": "Shell", "archived": false}, {"id": "cattr_ynh", "name": "cattr", "description": "", "url": "https://github.com/YunoHost-Apps/cattr_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:07:07Z", "language": "Shell", "archived": false}, {"id": "centreon_ynh", "name": "centreon", "description": "Centreon app for YunoHost", "url": "https://github.com/YunoHost-Apps/centreon_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:55:38Z", "language": "Shell", "archived": true}, {"id": "cesium_ynh", "name": "cesium", "description": "Cesium package for YunoHost", "url": "https://github.com/YunoHost-Apps/cesium_ynh", "stars": 2, "forks": 9, "updated_at": "2024-06-22T22:47:22Z", "language": "Shell", "archived": false}, {"id": "changedetection_ynh", "name": "changedetection", "description": "ChangeDetection Package for YunoHost", "url": "https://github.com/YunoHost-Apps/changedetection_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-08T12:57:13Z", "language": "Shell", "archived": false}, {"id": "chatgpt-web_ynh", "name": "chatgpt-web", "description": "ChatGPT-web package for YunoHost", "url": "https://github.com/YunoHost-Apps/chatgpt-web_ynh", "stars": 3, "forks": 4, "updated_at": "2025-05-19T20:32:43Z", "language": "Shell", "archived": false}, {"id": "chatonsinfos_ynh", "name": "chatonsinfos", "description": "", "url": "https://github.com/YunoHost-Apps/chatonsinfos_ynh", "stars": 1, "forks": 2, "updated_at": "2025-02-13T05:17:04Z", "language": "Shell", "archived": false}, {"id": "cheky_ynh", "name": "cheky", "description": "cheky package for YunoHost", "url": "https://github.com/YunoHost-Apps/cheky_ynh", "stars": 2, "forks": 3, "updated_at": "2024-06-23T11:29:11Z", "language": "Shell", "archived": false}, {"id": "chitchatter_ynh", "name": "chitchatter", "description": "Chitchatter package for YunoHost", "url": "https://github.com/YunoHost-Apps/chitchatter_ynh", "stars": 5, "forks": 3, "updated_at": "2024-06-16T10:13:44Z", "language": "Shell", "archived": false}, {"id": "chtickynotes_ynh", "name": "ch<PERSON>yn<PERSON>", "description": "Chtickynotes package for YunoHost", "url": "https://github.com/YunoHost-Apps/chtickynotes_ynh", "stars": 6, "forks": 4, "updated_at": "2024-06-23T11:29:14Z", "language": "JavaScript", "archived": false}, {"id": "chuwiki_ynh", "name": "chu<PERSON><PERSON>", "description": "ChuWiki package for YunoHost.", "url": "https://github.com/YunoHost-Apps/chuwiki_ynh", "stars": 4, "forks": 2, "updated_at": "2024-06-23T11:29:16Z", "language": "Shell", "archived": false}, {"id": "chyrplite_ynh", "name": "chyrplite", "description": "Chyrp Lite package for YunoHost ", "url": "https://github.com/YunoHost-Apps/chyrplite_ynh", "stars": 0, "forks": 0, "updated_at": "2024-06-23T11:29:18Z", "language": "Shell", "archived": false}, {"id": "ci_ynh", "name": "ci", "description": "Experimental YunoHost package check as a YunoHost app using Django...", "url": "https://github.com/YunoHost-Apps/ci_ynh", "stars": 0, "forks": 1, "updated_at": "2024-06-19T09:18:36Z", "language": "Shell", "archived": true}, {"id": "cinny_ynh", "name": "cinny", "description": "Cinny package for Yunohost (Matrix client)", "url": "https://github.com/YunoHost-Apps/cinny_ynh", "stars": 3, "forks": 2, "updated_at": "2025-05-25T07:50:41Z", "language": "Shell", "archived": false}, {"id": "civicrm_drupal_ynh", "name": "civicrm_drupal", "description": "onstituent relationship management for non-profits, NGOs and advocacy organizations for YunoHost", "url": "https://github.com/YunoHost-Apps/civicrm_drupal_ynh", "stars": 0, "forks": 0, "updated_at": "2024-12-27T02:35:01Z", "language": "Shell", "archived": false}, {"id": "civicrm_drupal7_ynh", "name": "civicrm_drupal7", "description": "Constituent relationship management for non-profits, NGOs and advocacy organizations for YunoHost", "url": "https://github.com/YunoHost-Apps/civicrm_drupal7_ynh", "stars": 2, "forks": 3, "updated_at": "2024-12-27T00:35:12Z", "language": "Shell", "archived": false}, {"id": "cjdns_ynh", "name": "cjdns", "description": "cjdns package for YunoHost", "url": "https://github.com/YunoHost-Apps/cjdns_ynh", "stars": 1, "forks": 3, "updated_at": "2025-02-04T16:51:47Z", "language": "Shell", "archived": false}, {"id": "cloudlog_ynh", "name": "cloudlog", "description": "CloudLog package for YunoHost", "url": "https://github.com/YunoHost-Apps/cloudlog_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-07T02:01:22Z", "language": "Shell", "archived": false}, {"id": "cockpit_ynh", "name": "cockpit", "description": "Cockpit package for YunoHost ", "url": "https://github.com/YunoHost-Apps/cockpit_ynh", "stars": 8, "forks": 8, "updated_at": "2025-01-06T20:28:37Z", "language": "Shell", "archived": false}, {"id": "code-server_ynh", "name": "code-server", "description": "Code-server packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/code-server_ynh", "stars": 6, "forks": 2, "updated_at": "2025-06-07T08:56:02Z", "language": "Shell", "archived": false}, {"id": "codepad_ynh", "name": "codepad", "description": "Codepad package for YunoHost.", "url": "https://github.com/YunoHost-Apps/codepad_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:59:38Z", "language": "Shell", "archived": true}, {"id": "codimd_ynh", "name": "codimd", "description": "CodiMD package for YunoHost", "url": "https://github.com/YunoHost-Apps/codimd_ynh", "stars": 11, "forks": 12, "updated_at": "2025-06-11T10:36:44Z", "language": "Shell", "archived": false}, {"id": "coin_ynh", "name": "coin", "description": "Member dashboard for non profit isp", "url": "https://github.com/YunoHost-Apps/coin_ynh", "stars": 2, "forks": 6, "updated_at": "2025-01-21T21:30:26Z", "language": "Shell", "archived": false}, {"id": "collabora_ynh", "name": "collabora", "description": "Collabora package for YunoHost", "url": "https://github.com/YunoHost-Apps/collabora_ynh", "stars": 16, "forks": 13, "updated_at": "2025-04-29T14:51:53Z", "language": "Shell", "archived": false}, {"id": "collaboradocker_ynh", "name": "collaboradocker", "description": "", "url": "https://github.com/YunoHost-Apps/collaboradocker_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:56:07Z", "language": "Shell", "archived": true}, {"id": "commafeed_ynh", "name": "commafeed", "description": "CommaFeed package for YunoHost", "url": "https://github.com/YunoHost-Apps/commafeed_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-29T06:46:03Z", "language": "Shell", "archived": false}, {"id": "commento_ynh", "name": "commento", "description": "Commento package for YunoHost", "url": "https://github.com/YunoHost-Apps/commento_ynh", "stars": 6, "forks": 2, "updated_at": "2024-06-23T11:29:30Z", "language": "Shell", "archived": false}, {"id": "commet_ynh", "name": "commet", "description": "Commet package for YunoHost", "url": "https://github.com/YunoHost-Apps/commet_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-15T08:14:25Z", "language": "Shell", "archived": false}, {"id": "compteur_du_gase_ynh", "name": "compteur_du_gase", "description": "", "url": "https://github.com/YunoHost-Apps/compteur_du_gase_ynh", "stars": 0, "forks": 3, "updated_at": "2025-03-04T21:19:29Z", "language": "Shell", "archived": false}, {"id": "concrete5_ynh", "name": "concrete5", "description": "modern CMS", "url": "https://github.com/YunoHost-Apps/concrete5_ynh", "stars": 3, "forks": 2, "updated_at": "2025-02-25T16:17:15Z", "language": "Shell", "archived": false}, {"id": "conduit_ynh", "name": "conduit", "description": "Conduit packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/conduit_ynh", "stars": 3, "forks": 7, "updated_at": "2025-06-08T12:45:17Z", "language": "Shell", "archived": false}, {"id": "conduwuit_ynh", "name": "conduwuit", "description": "Conduwuit package for YunoHost", "url": "https://github.com/YunoHost-Apps/conduwuit_ynh", "stars": 4, "forks": 1, "updated_at": "2025-04-26T03:44:34Z", "language": "Shell", "archived": true}, {"id": "converse_ynh", "name": "converse", "description": "Converse package for YunoHost", "url": "https://github.com/YunoHost-Apps/converse_ynh", "stars": 5, "forks": 2, "updated_at": "2025-05-21T15:32:56Z", "language": "Shell", "archived": false}, {"id": "convos_ynh", "name": "convos", "description": "", "url": "https://github.com/YunoHost-Apps/convos_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-04T19:55:11Z", "language": "Shell", "archived": false}, {"id": "cops_ynh", "name": "cops", "description": "Calibre OPDS (et HTML) PHP Serveur", "url": "https://github.com/YunoHost-Apps/cops_ynh", "stars": 1, "forks": 5, "updated_at": "2025-05-18T20:08:46Z", "language": "Shell", "archived": false}, {"id": "coquelicot_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "[Not working] Coquelicot package for YunoHost. Coquelicot is a one-click file sharing web application with a focus on protecting users privacy.", "url": "https://github.com/YunoHost-Apps/coquelicot_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T17:50:33Z", "language": "Shell", "archived": true}, {"id": "coturn_ynh", "name": "coturn", "description": "coturn TURN server package for YunoHost", "url": "https://github.com/YunoHost-Apps/coturn_ynh", "stars": 6, "forks": 7, "updated_at": "2025-01-24T19:50:22Z", "language": "Shell", "archived": false}, {"id": "couchdb_ynh", "name": "couchdb", "description": "Couchdb package for YunoHost", "url": "https://github.com/YunoHost-Apps/couchdb_ynh", "stars": 4, "forks": 2, "updated_at": "2024-06-08T19:59:42Z", "language": "Shell", "archived": false}, {"id": "couchpotato_ynh", "name": "couchpotato", "description": "CouchPotato package for YunoHost (install from fork)", "url": "https://github.com/YunoHost-Apps/couchpotato_ynh", "stars": 3, "forks": 2, "updated_at": "2024-07-26T20:16:02Z", "language": "Shell", "archived": true}, {"id": "cowyo_ynh", "name": "cowyo", "description": "Cowyo package for YunoHost", "url": "https://github.com/YunoHost-Apps/cowyo_ynh", "stars": 3, "forks": 2, "updated_at": "2024-06-27T19:17:36Z", "language": "Shell", "archived": false}, {"id": "crabfit_ynh", "name": "crabfit", "description": "crab.fit package for YunoHost", "url": "https://github.com/YunoHost-Apps/crabfit_ynh", "stars": 1, "forks": 1, "updated_at": "2025-01-19T17:14:55Z", "language": "Shell", "archived": false}, {"id": "cryptpad_ynh", "name": "cryptpad", "description": "CryptPad package for YunoHost", "url": "https://github.com/YunoHost-Apps/cryptpad_ynh", "stars": 30, "forks": 23, "updated_at": "2025-06-07T05:47:19Z", "language": "JavaScript", "archived": false}, {"id": "cubby_ynh", "name": "cubby", "description": "Cubby package for YunoHost", "url": "https://github.com/YunoHost-Apps/cubby_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-05T18:40:22Z", "language": "Shell", "archived": false}, {"id": "Cubiks-2048_ynh", "name": "Cubiks-2048", "description": "2048 game Clone in 3D for YunoHost", "url": "https://github.com/YunoHost-Apps/Cubiks-2048_ynh", "stars": 3, "forks": 2, "updated_at": "2025-05-08T15:08:34Z", "language": "JavaScript", "archived": false}, {"id": "cultivons_ynh", "name": "cultivons", "description": "Cultivons package for YunoHost", "url": "https://github.com/YunoHost-Apps/cultivons_ynh", "stars": 1, "forks": 1, "updated_at": "2024-09-08T10:44:31Z", "language": "Shell", "archived": false}, {"id": "cusdis_ynh", "name": "cusdis", "description": "Cusdis package for YunoHost", "url": "https://github.com/YunoHost-Apps/cusdis_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-18T19:40:57Z", "language": "Shell", "archived": false}, {"id": "custom_backup_ynh", "name": "custom_backup", "description": "An app to backup and restore extra paths", "url": "https://github.com/YunoHost-Apps/custom_backup_ynh", "stars": 2, "forks": 1, "updated_at": "2024-08-21T06:50:18Z", "language": "Shell", "archived": false}, {"id": "cyberchef_ynh", "name": "cyberchef", "description": "The Cyber Swiss Army Knife packaged for Yunohost - a web app for encryption, encoding, compression and data analysis.", "url": "https://github.com/YunoHost-Apps/cyberchef_ynh", "stars": 0, "forks": 2, "updated_at": "2024-10-24T23:35:17Z", "language": "Shell", "archived": false}, {"id": "cypht_ynh", "name": "cypht", "description": "Cypht package for YunoHost", "url": "https://github.com/YunoHost-Apps/cypht_ynh", "stars": 3, "forks": 6, "updated_at": "2025-05-19T17:42:08Z", "language": "Shell", "archived": false}, {"id": "cytube_ynh", "name": "cytube", "description": "CyTube package for YunoHost", "url": "https://github.com/YunoHost-Apps/cytube_ynh", "stars": 0, "forks": 0, "updated_at": "2025-02-11T12:18:19Z", "language": "Shell", "archived": false}, {"id": "dagu_ynh", "name": "dagu", "description": "Dagu package for YunoHost", "url": "https://github.com/YunoHost-Apps/dagu_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-19T20:12:26Z", "language": "Shell", "archived": false}, {"id": "dato_ynh", "name": "dato", "description": "Dato package for YunoHost", "url": "https://github.com/YunoHost-Apps/dato_ynh", "stars": 6, "forks": 1, "updated_at": "2024-07-01T21:00:56Z", "language": "Shell", "archived": false}, {"id": "davis_ynh", "name": "davis", "description": "Davis package for YunoHost", "url": "https://github.com/YunoHost-Apps/davis_ynh", "stars": 0, "forks": 1, "updated_at": "2025-06-01T16:29:44Z", "language": "Shell", "archived": false}, {"id": "decidim_ynh", "name": "decidim", "description": "Decidim package for YunoHost", "url": "https://github.com/YunoHost-Apps/decidim_ynh", "stars": 2, "forks": 1, "updated_at": "2024-09-03T21:43:04Z", "language": "Shell", "archived": false}, {"id": "deluge_ynh", "name": "deluge", "description": "Deluge package for YunoHost", "url": "https://github.com/YunoHost-Apps/deluge_ynh", "stars": 0, "forks": 1, "updated_at": "2025-06-06T06:48:01Z", "language": "Shell", "archived": false}, {"id": "democracyos_ynh", "name": "democracyos", "description": "", "url": "https://github.com/YunoHost-Apps/democracyos_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:54:06Z", "language": "Shell", "archived": true}, {"id": "dendrite_ynh", "name": "dendrite", "description": "Matrix homeserver of second-generation, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/dendrite_ynh", "stars": 16, "forks": 8, "updated_at": "2025-04-29T14:36:47Z", "language": "Shell", "archived": false}, {"id": "dex_ynh", "name": "dex", "description": "Federated OpenID Connect Provider package for YunoHost", "url": "https://github.com/YunoHost-Apps/dex_ynh", "stars": 8, "forks": 6, "updated_at": "2025-06-07T17:30:31Z", "language": "Shell", "archived": false}, {"id": "diacamma_ynh", "name": "diacamma", "description": "Diacamma package for YunoHost", "url": "https://github.com/YunoHost-Apps/diacamma_ynh", "stars": 0, "forks": 1, "updated_at": "2024-07-26T19:09:44Z", "language": "Shell", "archived": false}, {"id": "diagnostickoeur_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Imprimer des stickers!!", "url": "https://github.com/YunoHost-Apps/diagnostickoeur_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-07T08:42:58Z", "language": "Shell", "archived": false}, {"id": "diagramsnet_ynh", "name": "diagramsnet", "description": "Diagramsnet package for YunoHost", "url": "https://github.com/YunoHost-Apps/diagramsnet_ynh", "stars": 6, "forks": 4, "updated_at": "2025-06-07T08:38:15Z", "language": "Shell", "archived": false}, {"id": "diaspora_ynh", "name": "diaspora", "description": "Diaspora package for YunoHost", "url": "https://github.com/YunoHost-Apps/diaspora_ynh", "stars": 4, "forks": 5, "updated_at": "2025-03-26T18:31:06Z", "language": "Shell", "archived": false}, {"id": "digipad_ynh", "name": "digipad", "description": "Digipad, create multimedia collaborative walls", "url": "https://github.com/YunoHost-Apps/digipad_ynh", "stars": 1, "forks": 0, "updated_at": "2024-10-19T08:51:52Z", "language": "Shell", "archived": false}, {"id": "digiscreen_ynh", "name": "digiscreen", "description": "Digiscreen package for YunoHost", "url": "https://github.com/YunoHost-Apps/digiscreen_ynh", "stars": 1, "forks": 2, "updated_at": "2025-03-15T14:58:05Z", "language": "Shell", "archived": false}, {"id": "digisteps_ynh", "name": "digisteps", "description": "Digisteps package for YunoHost", "url": "https://github.com/YunoHost-Apps/digisteps_ynh", "stars": 2, "forks": 1, "updated_at": "2025-03-12T01:03:54Z", "language": "Shell", "archived": false}, {"id": "digitools_ynh", "name": "digitools", "description": "DigiTools package for YunoHost", "url": "https://github.com/YunoHost-Apps/digitools_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-11T07:13:06Z", "language": "Shell", "archived": false}, {"id": "digitranscode_ynh", "name": "digitranscode", "description": "DigiTranscode package for YunoHost", "url": "https://github.com/YunoHost-Apps/digitranscode_ynh", "stars": 5, "forks": 3, "updated_at": "2025-05-26T05:55:13Z", "language": "Shell", "archived": false}, {"id": "digiwords_ynh", "name": "digiwords", "description": "DigiWords package for YunoHost", "url": "https://github.com/YunoHost-Apps/digiwords_ynh", "stars": 3, "forks": 1, "updated_at": "2025-03-11T07:17:35Z", "language": "Shell", "archived": false}, {"id": "directorylister_ynh", "name": "directorylister", "description": "Directory Lister package for YunoHost", "url": "https://github.com/YunoHost-Apps/directorylister_ynh", "stars": 2, "forks": 2, "updated_at": "2025-05-19T20:21:03Z", "language": "Shell", "archived": false}, {"id": "discourse_ynh", "name": "discourse", "description": "Discourse package for YunoHost", "url": "https://github.com/YunoHost-Apps/discourse_ynh", "stars": 25, "forks": 12, "updated_at": "2025-05-27T21:14:21Z", "language": "Shell", "archived": false}, {"id": "dispatch_ynh", "name": "dispatch", "description": "Dispatch package for YunoHost", "url": "https://github.com/YunoHost-Apps/dispatch_ynh", "stars": 1, "forks": 1, "updated_at": "2024-09-03T21:23:13Z", "language": "Shell", "archived": false}, {"id": "distbin_ynh", "name": "distbin", "description": "Distributed pastebin with ActivityPub for YunoHost", "url": "https://github.com/YunoHost-Apps/distbin_ynh", "stars": 4, "forks": 2, "updated_at": "2024-12-11T00:37:25Z", "language": "Shell", "archived": false}, {"id": "django_ynh", "name": "django", "description": "Glue code to package django projects as YunoHost apps.", "url": "https://github.com/YunoHost-Apps/django_ynh", "stars": 4, "forks": 1, "updated_at": "2024-05-15T17:58:19Z", "language": "Shell", "archived": true}, {"id": "django_example_ynh", "name": "django_example", "description": "Demo YunoHost Application to demonstrate the integration of a Django project under YunoHost.", "url": "https://github.com/YunoHost-Apps/django_example_ynh", "stars": 7, "forks": 6, "updated_at": "2025-03-12T17:22:38Z", "language": "Python", "archived": false}, {"id": "django-fmd_ynh", "name": "django-fmd", "description": "Find My Device Server -> https://gitlab.com/jedie/django-find-my-device", "url": "https://github.com/YunoHost-Apps/django-fmd_ynh", "stars": 1, "forks": 0, "updated_at": "2024-09-07T11:14:31Z", "language": "Python", "archived": false}, {"id": "django-for-runners_ynh", "name": "django-for-runners", "description": "YunoHost app package for https://github.com/jedie/django-for-runners", "url": "https://github.com/YunoHost-Apps/django-for-runners_ynh", "stars": 3, "forks": 0, "updated_at": "2025-03-23T14:14:27Z", "language": "Python", "archived": false}, {"id": "django-fritzconnection_ynh", "name": "django-fritzconnection", "description": "Web based FritzBox management using Python/Django.", "url": "https://github.com/YunoHost-Apps/django-fritzconnection_ynh", "stars": 4, "forks": 1, "updated_at": "2025-03-23T19:13:47Z", "language": "Python", "archived": false}, {"id": "docker_container_ynh", "name": "docker_container", "description": "Docker containers for yunohost", "url": "https://github.com/YunoHost-Apps/docker_container_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:53:19Z", "language": "Shell", "archived": true}, {"id": "docker_django_example_ynh", "name": "docker_django_example", "description": "Demo YunoHost Application to demonstrate the integration of a Django project via Docker under YunoHost. ", "url": "https://github.com/YunoHost-Apps/docker_django_example_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:34Z", "language": "Shell", "archived": false}, {"id": "dockerui_ynh", "name": "docker<PERSON>", "description": "Docker and DockerUI for YunoHost", "url": "https://github.com/YunoHost-Apps/dockerui_ynh", "stars": 1, "forks": 1, "updated_at": "2024-06-19T09:11:29Z", "language": "Shell", "archived": true}, {"id": "docmost_ynh", "name": "docmost", "description": "Docmost package for YunoHost", "url": "https://github.com/YunoHost-Apps/docmost_ynh", "stars": 1, "forks": 0, "updated_at": "2025-03-28T11:30:51Z", "language": "Shell", "archived": false}, {"id": "docs_ynh", "name": "docs", "description": "Collaborate and write in real time, with no layout constraints", "url": "https://github.com/YunoHost-Apps/docs_ynh", "stars": 1, "forks": 1, "updated_at": "2025-06-08T13:32:26Z", "language": "Shell", "archived": false}, {"id": "docsify_ynh", "name": "docsify", "description": "Docsify package for YunoHost", "url": "https://github.com/YunoHost-Apps/docsify_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-08T18:18:29Z", "language": "Shell", "archived": false}, {"id": "documize_ynh", "name": "documize", "description": "Documize package for YunoHost", "url": "https://github.com/YunoHost-Apps/documize_ynh", "stars": 3, "forks": 2, "updated_at": "2025-03-25T06:17:10Z", "language": "Shell", "archived": false}, {"id": "docusaurus_ynh", "name": "<PERSON>cusaurus", "description": "Docusaurus package for YunoHost", "url": "https://github.com/YunoHost-Apps/docusaurus_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-08T18:26:01Z", "language": "Shell", "archived": false}, {"id": "dodoc_ynh", "name": "dodoc", "description": "do•doc package for YunoHost", "url": "https://github.com/YunoHost-Apps/dodoc_ynh", "stars": 4, "forks": 1, "updated_at": "2025-03-13T08:47:01Z", "language": "Shell", "archived": false}, {"id": "Dokos_ynh", "name": "<PERSON><PERSON>", "description": "Dokos is a 100% open-source management software that is based on ERPNext", "url": "https://github.com/YunoHost-Apps/Dokos_ynh", "stars": 1, "forks": 1, "updated_at": "2025-05-14T14:44:18Z", "language": "Shell", "archived": false}, {"id": "dokuwiki_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Dokuwiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/dokuwiki_ynh", "stars": 11, "forks": 16, "updated_at": "2025-05-14T17:18:23Z", "language": "Shell", "archived": false}, {"id": "dolibarr_ynh", "name": "doli<PERSON><PERSON>", "description": "Dolibarr ERP & CRM is a modern software to manage your organization's activity. This is an integration of Dolibarr in YunoHost", "url": "https://github.com/YunoHost-Apps/dolibarr_ynh", "stars": 14, "forks": 21, "updated_at": "2025-04-07T15:33:31Z", "language": "Shell", "archived": false}, {"id": "domoticz_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Domoticz package for YunoHost", "url": "https://github.com/YunoHost-Apps/domoticz_ynh", "stars": 3, "forks": 3, "updated_at": "2025-05-13T21:47:14Z", "language": "Shell", "archived": false}, {"id": "dont-code_ynh", "name": "dont-code", "description": "Dont-Code package for YunoHost", "url": "https://github.com/YunoHost-Apps/dont-code_ynh", "stars": 2, "forks": 3, "updated_at": "2025-03-10T14:10:06Z", "language": "Shell", "archived": false}, {"id": "dotclear2_ynh", "name": "dotclear2", "description": "DotClear2 package for YunoHost", "url": "https://github.com/YunoHost-Apps/dotclear2_ynh", "stars": 3, "forks": 5, "updated_at": "2024-05-22T09:54:43Z", "language": "Shell", "archived": false}, {"id": "droppy_ynh", "name": "droppy", "description": "Droppy package for YunoHost", "url": "https://github.com/YunoHost-Apps/droppy_ynh", "stars": 4, "forks": 2, "updated_at": "2024-05-15T17:57:49Z", "language": "Shell", "archived": true}, {"id": "drupal_ynh", "name": "drupal", "description": "Drupal package for YunoHost", "url": "https://github.com/YunoHost-Apps/drupal_ynh", "stars": 5, "forks": 12, "updated_at": "2025-06-04T16:14:56Z", "language": "Shell", "archived": false}, {"id": "drupal7_ynh", "name": "drupal7", "description": "Free and open-source content management framework for YunoHost", "url": "https://github.com/YunoHost-Apps/drupal7_ynh", "stars": 1, "forks": 2, "updated_at": "2024-12-27T00:34:59Z", "language": "Shell", "archived": false}, {"id": "dumbbudget_ynh", "name": "dumbbudget", "description": "DumbBudget package for YunoHost", "url": "https://github.com/YunoHost-Apps/dumbbudget_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-19T20:06:56Z", "language": "Shell", "archived": false}, {"id": "dumbdo_ynh", "name": "dumbdo", "description": "DumbDo package for YunoHost", "url": "https://github.com/YunoHost-Apps/dumbdo_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-09T08:47:41Z", "language": "Shell", "archived": false}, {"id": "dumbdrop_ynh", "name": "dumbdrop", "description": "Dumdrop package for YunoHost", "url": "https://github.com/YunoHost-Apps/dumbdrop_ynh", "stars": 1, "forks": 1, "updated_at": "2025-06-09T08:48:21Z", "language": "Shell", "archived": false}, {"id": "dumbkan_ynh", "name": "dumbkan", "description": "DumbKan package for YunoHost", "url": "https://github.com/YunoHost-Apps/dumbkan_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-09T08:49:33Z", "language": "Shell", "archived": false}, {"id": "dumbpad_ynh", "name": "dumbpad", "description": "DumbPad package for YunoHost", "url": "https://github.com/YunoHost-Apps/dumbpad_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-09T10:05:28Z", "language": "Shell", "archived": false}, {"id": "dumbwhois_ynh", "name": "dumbw<PERSON><PERSON>", "description": "DumbWhoIs package for YunoHost", "url": "https://github.com/YunoHost-Apps/dumbwhois_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-09T08:51:12Z", "language": "Shell", "archived": false}, {"id": "duniter_ynh", "name": "duniter", "description": "Duniter package for YunoHost", "url": "https://github.com/YunoHost-Apps/duniter_ynh", "stars": 17, "forks": 13, "updated_at": "2025-01-11T05:45:53Z", "language": "Shell", "archived": false}, {"id": "dynamicqrcode_ynh", "name": "dynamicqrcode", "description": "Dynamic Qr code package for YunoHost", "url": "https://github.com/YunoHost-Apps/dynamicqrcode_ynh", "stars": 1, "forks": 1, "updated_at": "2025-06-08T21:34:05Z", "language": "Shell", "archived": false}, {"id": "easyappointments_ynh", "name": "easyappointments", "description": "Easy!Appointments package for YunoHost", "url": "https://github.com/YunoHost-Apps/easyappointments_ynh", "stars": 5, "forks": 2, "updated_at": "2025-01-20T15:38:04Z", "language": "Shell", "archived": false}, {"id": "ecko_ynh", "name": "ecko", "description": "Ecko package for YunoHost", "url": "https://github.com/YunoHost-Apps/ecko_ynh", "stars": 6, "forks": 2, "updated_at": "2024-06-19T09:14:04Z", "language": "Shell", "archived": true}, {"id": "elabftw_ynh", "name": "elabftw", "description": "eLabFTW package for YunoHost", "url": "https://github.com/YunoHost-Apps/elabftw_ynh", "stars": 1, "forks": 2, "updated_at": "2025-01-27T20:43:43Z", "language": "Shell", "archived": false}, {"id": "elasticsearch7_ynh", "name": "elasticsearch7", "description": ":mag_right: Distributed and RESTful search engine for YunoHost.", "url": "https://github.com/YunoHost-Apps/elasticsearch7_ynh", "stars": 0, "forks": 1, "updated_at": "2024-06-22T22:47:44Z", "language": "Shell", "archived": false}, {"id": "elasticsearch8_ynh", "name": "elasticsearch8", "description": ":mag_right: Distributed and RESTful search engine for YunoHost. ", "url": "https://github.com/YunoHost-Apps/elasticsearch8_ynh", "stars": 0, "forks": 1, "updated_at": "2024-06-23T17:21:06Z", "language": "Shell", "archived": false}, {"id": "element_ynh", "name": "element", "description": "Element package for YunoHost", "url": "https://github.com/YunoHost-Apps/element_ynh", "stars": 23, "forks": 8, "updated_at": "2025-06-11T09:58:40Z", "language": "Shell", "archived": false}, {"id": "element-call_ynh", "name": "element-call", "description": "Element-Call package for YunoHost", "url": "https://github.com/YunoHost-Apps/element-call_ynh", "stars": 0, "forks": 3, "updated_at": "2025-03-29T09:54:34Z", "language": "Shell", "archived": false}, {"id": "eleventy_ynh", "name": "eleventy", "description": "Eleventy package for YunoHost", "url": "https://github.com/YunoHost-Apps/eleventy_ynh", "stars": 3, "forks": 3, "updated_at": "2024-11-27T16:04:23Z", "language": "Shell", "archived": false}, {"id": "emailpoubelle_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Générer des emails jetables dans YunoHost", "url": "https://github.com/YunoHost-Apps/emailpoubelle_ynh", "stars": 7, "forks": 4, "updated_at": "2025-01-29T21:57:10Z", "language": "Shell", "archived": false}, {"id": "emby_ynh", "name": "emby", "description": "Emby Server package for YunoHost", "url": "https://github.com/YunoHost-Apps/emby_ynh", "stars": 3, "forks": 10, "updated_at": "2024-05-15T17:55:28Z", "language": "Shell", "archived": true}, {"id": "emoncms_ynh", "name": "emoncms", "description": "Emoncms package for YunoHost", "url": "https://github.com/YunoHost-Apps/emoncms_ynh", "stars": 0, "forks": 1, "updated_at": "2025-02-09T21:51:34Z", "language": "Shell", "archived": false}, {"id": "encryptic_ynh", "name": "encryptic", "description": "Encryptic package for YunoHost", "url": "https://github.com/YunoHost-Apps/encryptic_ynh", "stars": 5, "forks": 2, "updated_at": "2024-05-15T17:58:04Z", "language": "Shell", "archived": true}, {"id": "encryptor-decryptor_ynh", "name": "encryptor-decryptor", "description": "Easy-File-Encryptor-Decryptor package for YunoHost", "url": "https://github.com/YunoHost-Apps/encryptor-decryptor_ynh", "stars": 0, "forks": 1, "updated_at": "2024-09-11T14:32:42Z", "language": "Shell", "archived": false}, {"id": "epicyon_ynh", "name": "epicyon", "description": "ActivityPub compliant server ", "url": "https://github.com/YunoHost-Apps/epicyon_ynh", "stars": 6, "forks": 3, "updated_at": "2024-06-15T04:01:10Z", "language": "Shell", "archived": false}, {"id": "ergo_ynh", "name": "ergo", "description": "Ergo (IRC server) package for YunoHost", "url": "https://github.com/YunoHost-Apps/ergo_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-19T00:43:06Z", "language": "Shell", "archived": false}, {"id": "esphome_ynh", "name": "esphome", "description": "Unofficial ESPHome package for YunoHost", "url": "https://github.com/YunoHost-Apps/esphome_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-10T16:04:59Z", "language": "Shell", "archived": false}, {"id": "espocrm_ynh", "name": "espocrm", "description": "EspoCRM package for YunoHost", "url": "https://github.com/YunoHost-Apps/espocrm_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-04T14:20:17Z", "language": "Shell", "archived": false}, {"id": "ethercalc_ynh", "name": "ethercalc", "description": "EtherCalc package for YunoHost ", "url": "https://github.com/YunoHost-Apps/ethercalc_ynh", "stars": 9, "forks": 6, "updated_at": "2024-10-05T17:55:07Z", "language": "Shell", "archived": false}, {"id": "etherpad_ynh", "name": "etherpad", "description": "Etherpad-Lite package for YunoHost", "url": "https://github.com/YunoHost-Apps/etherpad_ynh", "stars": 2, "forks": 2, "updated_at": "2025-04-06T15:43:30Z", "language": "Shell", "archived": false}, {"id": "etherpad_mypads_ynh", "name": "etherpad_mypads", "description": "Etherpad MyPads package for YunoHost", "url": "https://github.com/YunoHost-Apps/etherpad_mypads_ynh", "stars": 16, "forks": 14, "updated_at": "2024-06-23T11:30:42Z", "language": "Shell", "archived": false}, {"id": "excalidraw_ynh", "name": "excalidraw", "description": "Excalidraw package for YunoHost", "url": "https://github.com/YunoHost-Apps/excalidraw_ynh", "stars": 4, "forks": 4, "updated_at": "2025-03-15T14:55:37Z", "language": "Shell", "archived": false}, {"id": "fab-manager_ynh", "name": "fab-manager", "description": "Fab-manager package for YunoHost", "url": "https://github.com/YunoHost-Apps/fab-manager_ynh", "stars": 3, "forks": 6, "updated_at": "2025-04-02T19:47:54Z", "language": "Shell", "archived": false}, {"id": "faceprivacy_ynh", "name": "faceprivacy", "description": "FacePrivacy package for YunoHost", "url": "https://github.com/YunoHost-Apps/faceprivacy_ynh", "stars": 1, "forks": 0, "updated_at": "2025-03-15T18:08:53Z", "language": "Shell", "archived": false}, {"id": "facette_ynh", "name": "facette", "description": "Facette packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/facette_ynh", "stars": 1, "forks": 1, "updated_at": "2024-06-23T11:30:47Z", "language": "Shell", "archived": false}, {"id": "facilmap_ynh", "name": "facilmap", "description": "Facilmap package for YunoHost", "url": "https://github.com/YunoHost-Apps/facilmap_ynh", "stars": 6, "forks": 4, "updated_at": "2024-07-05T15:16:38Z", "language": "Shell", "archived": false}, {"id": "fail2ban-web_ynh", "name": "fail2ban-web", "description": "Monitoring fail2ban and manually ban / release IP's", "url": "https://github.com/YunoHost-Apps/fail2ban-web_ynh", "stars": 1, "forks": 0, "updated_at": "2025-01-26T08:07:51Z", "language": "Shell", "archived": false}, {"id": "faircamp_ynh", "name": "faircamp", "description": "Faircamp package for YunoHost", "url": "https://github.com/YunoHost-Apps/faircamp_ynh", "stars": 1, "forks": 1, "updated_at": "2025-05-25T00:35:26Z", "language": "Shell", "archived": false}, {"id": "fallback_ynh", "name": "fallback", "description": "Fallback package for YunoHost", "url": "https://github.com/YunoHost-Apps/fallback_ynh", "stars": 14, "forks": 1, "updated_at": "2024-07-26T20:15:58Z", "language": "Shell", "archived": true}, {"id": "FastAPI_ynh", "name": "FastAPI", "description": "a YunoHost app to run a custom FastAPI code", "url": "https://github.com/YunoHost-Apps/FastAPI_ynh", "stars": 2, "forks": 3, "updated_at": "2025-04-29T14:22:33Z", "language": "Shell", "archived": false}, {"id": "fathom_ynh", "name": "fathom", "description": "Fathom package for YunoHost", "url": "https://github.com/YunoHost-Apps/fathom_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-01T20:10:46Z", "language": "Shell", "archived": false}, {"id": "feber_ynh", "name": "feber", "description": "Feber package for YunoHost", "url": "https://github.com/YunoHost-Apps/feber_ynh", "stars": 0, "forks": 2, "updated_at": "2025-01-25T00:33:34Z", "language": "Shell", "archived": false}, {"id": "ffsync_ynh", "name": "ffsync", "description": "Mozilla’s Sync Server package for YunoHost", "url": "https://github.com/YunoHost-Apps/ffsync_ynh", "stars": 15, "forks": 8, "updated_at": "2024-05-15T17:52:09Z", "language": "Python", "archived": true}, {"id": "fider_ynh", "name": "fider", "description": "Open platform to collect and prioritize feedback", "url": "https://github.com/YunoHost-Apps/fider_ynh", "stars": 1, "forks": 1, "updated_at": "2025-06-07T08:49:55Z", "language": "Shell", "archived": false}, {"id": "filebin_ynh", "name": "filebin", "description": "FileBin package for YunoHost", "url": "https://github.com/YunoHost-Apps/filebin_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-20T16:31:06Z", "language": "Shell", "archived": false}, {"id": "filebrowser_ynh", "name": "filebrowser", "description": "FileBrowser package for YunoHost", "url": "https://github.com/YunoHost-Apps/filebrowser_ynh", "stars": 5, "forks": 1, "updated_at": "2025-04-15T13:52:25Z", "language": "Shell", "archived": false}, {"id": "filepizza_ynh", "name": "filepizza", "description": "FilePizza package for YunoHost", "url": "https://github.com/YunoHost-Apps/filepizza_ynh", "stars": 4, "forks": 2, "updated_at": "2025-04-08T12:20:15Z", "language": "Shell", "archived": false}, {"id": "findmydevice_ynh", "name": "findmydevice", "description": "Find My Device Server package for YunoHost", "url": "https://github.com/YunoHost-Apps/findmydevice_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-15T07:10:56Z", "language": "Shell", "archived": false}, {"id": "fipamo_ynh", "name": "fipamo", "description": "Fipamo package for YunoHost", "url": "https://github.com/YunoHost-Apps/fipamo_ynh", "stars": 0, "forks": 0, "updated_at": "2024-06-19T09:17:18Z", "language": "Shell", "archived": true}, {"id": "firefish_ynh", "name": "firefish", "description": "Forked from Misskey and made by a passionate team of developers, Firefish is all about listening to its community and making people happy with great software.", "url": "https://github.com/YunoHost-Apps/firefish_ynh", "stars": 1, "forks": 6, "updated_at": "2025-03-25T20:50:32Z", "language": "Shell", "archived": true}, {"id": "firefly-iii_ynh", "name": "firefly-iii", "description": " Firefly III package for YunoHost", "url": "https://github.com/YunoHost-Apps/firefly-iii_ynh", "stars": 22, "forks": 12, "updated_at": "2025-06-07T08:54:13Z", "language": "Shell", "archived": false}, {"id": "firefly-iii-di_ynh", "name": "firefly-iii-di", "description": "Firefly-III Data Importer packager for YunoHost", "url": "https://github.com/YunoHost-Apps/firefly-iii-di_ynh", "stars": 3, "forks": 9, "updated_at": "2025-06-09T08:51:42Z", "language": "Shell", "archived": false}, {"id": "fittrackee_ynh", "name": "fittrack<PERSON>", "description": "FitTrackee package for YunoHost 🚴", "url": "https://github.com/YunoHost-Apps/fittrackee_ynh", "stars": 6, "forks": 1, "updated_at": "2025-05-31T16:32:53Z", "language": "Shell", "archived": false}, {"id": "flarum_ynh", "name": "flarum", "description": "Flarum, an open-source forum software, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/flarum_ynh", "stars": 21, "forks": 6, "updated_at": "2025-05-29T08:07:21Z", "language": "Shell", "archived": false}, {"id": "flask_ynh", "name": "flask", "description": "Flask template for YunoHost", "url": "https://github.com/YunoHost-Apps/flask_ynh", "stars": 6, "forks": 9, "updated_at": "2024-06-19T09:20:20Z", "language": "Shell", "archived": true}, {"id": "flohmarkt_ynh", "name": "floh<PERSON>t", "description": "integration of flohmarkt - a federated decentral classified ad software using activitypub - into YunoHost", "url": "https://github.com/YunoHost-Apps/flohmarkt_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-11T09:08:40Z", "language": "Shell", "archived": false}, {"id": "flood_ynh", "name": "flood", "description": "Flood package for YunoHost", "url": "https://github.com/YunoHost-Apps/flood_ynh", "stars": 2, "forks": 2, "updated_at": "2025-06-06T06:29:29Z", "language": "Shell", "archived": false}, {"id": "flow_ynh", "name": "flow", "description": "Flow package for YunoHost", "url": "https://github.com/YunoHost-Apps/flow_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T18:04:25Z", "language": "Shell", "archived": false}, {"id": "fluffychat_ynh", "name": "fluffychat", "description": " Fluffychat package for YunoHost: The cutest instant messenger in the [matrix]", "url": "https://github.com/YunoHost-Apps/fluffychat_ynh", "stars": 1, "forks": 1, "updated_at": "2025-06-07T08:52:41Z", "language": "Shell", "archived": false}, {"id": "flus_ynh", "name": "flus", "description": "Flus package for YunoHost", "url": "https://github.com/YunoHost-Apps/flus_ynh", "stars": 1, "forks": 0, "updated_at": "2025-02-27T01:00:16Z", "language": "Shell", "archived": false}, {"id": "fluxbb_ynh", "name": "fluxbb", "description": "FluxBB package for YunoHost", "url": "https://github.com/YunoHost-Apps/fluxbb_ynh", "stars": 1, "forks": 2, "updated_at": "2025-03-11T13:23:36Z", "language": "Shell", "archived": false}, {"id": "focalboard_ynh", "name": "focalboard", "description": "Focalboard package for YunoHost", "url": "https://github.com/YunoHost-Apps/focalboard_ynh", "stars": 7, "forks": 3, "updated_at": "2025-01-21T16:25:51Z", "language": "Shell", "archived": false}, {"id": "fontcompare_ynh", "name": "fontcompare", "description": "An easy way to dynamically compare locally hosted fonts", "url": "https://github.com/YunoHost-Apps/fontcompare_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-07T01:17:52Z", "language": "Shell", "archived": false}, {"id": "foodsoft_ynh", "name": "foodsoft", "description": "Web-based software to manage a non-profit food coop package for YunoHost", "url": "https://github.com/YunoHost-Apps/foodsoft_ynh", "stars": 2, "forks": 2, "updated_at": "2024-05-15T17:56:53Z", "language": "Shell", "archived": true}, {"id": "forgejo_ynh", "name": "<PERSON><PERSON>", "description": "Forgejo package for YunoHost", "url": "https://github.com/YunoHost-Apps/forgejo_ynh", "stars": 18, "forks": 5, "updated_at": "2025-06-07T08:36:05Z", "language": "Shell", "archived": false}, {"id": "forgejo_runner_ynh", "name": "forgejo_runner", "description": "", "url": "https://github.com/YunoHost-Apps/forgejo_runner_ynh", "stars": 1, "forks": 1, "updated_at": "2025-05-13T22:23:11Z", "language": "Shell", "archived": false}, {"id": "forgejo-runner_ynh", "name": "forgejo-runner", "description": "Forgejo Runner package for YunoHost", "url": "https://github.com/YunoHost-Apps/forgejo-runner_ynh", "stars": 0, "forks": 3, "updated_at": "2025-01-24T09:04:52Z", "language": "Shell", "archived": false}, {"id": "formbricks_ynh", "name": "formbricks", "description": "Formbricks package for YunoHost", "url": "https://github.com/YunoHost-Apps/formbricks_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-06T08:21:28Z", "language": "Shell", "archived": false}, {"id": "forte_ynh", "name": "forte", "description": "Forte package for YunoHost", "url": "https://github.com/YunoHost-Apps/forte_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-08T21:45:32Z", "language": "Shell", "archived": false}, {"id": "framaestro_ynh", "name": "frama<PERSON><PERSON>", "description": "", "url": "https://github.com/YunoHost-Apps/framaestro_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:09Z", "language": "Shell", "archived": true}, {"id": "framaestro_hub_ynh", "name": "framaestro_hub", "description": "", "url": "https://github.com/YunoHost-Apps/framaestro_hub_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:11Z", "language": "Shell", "archived": true}, {"id": "framaforms_ynh", "name": "framaforms", "description": "Create online webforms and surveys with Framaforms on YunoHost", "url": "https://github.com/YunoHost-Apps/framaforms_ynh", "stars": 9, "forks": 6, "updated_at": "2024-09-17T14:46:16Z", "language": "PHP", "archived": false}, {"id": "framagames_ynh", "name": "framagames", "description": "Set of games from Framasoft", "url": "https://github.com/YunoHost-Apps/framagames_ynh", "stars": 0, "forks": 3, "updated_at": "2025-05-06T06:49:05Z", "language": "Shell", "archived": false}, {"id": "freeboard_ynh", "name": "freeboard", "description": "A damn-sexy, open source real-time dashboard builder for IOT and other web mashups. A free open-source alternative to Geckoboard.", "url": "https://github.com/YunoHost-Apps/freeboard_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:51:42Z", "language": "Shell", "archived": true}, {"id": "freepbx_ynh", "name": "freepbx", "description": "", "url": "https://github.com/YunoHost-Apps/freepbx_ynh", "stars": 2, "forks": 3, "updated_at": "2025-04-21T03:16:32Z", "language": "Shell", "archived": true}, {"id": "freescout_ynh", "name": "freescout", "description": "Freescout package for YunoHost", "url": "https://github.com/YunoHost-Apps/freescout_ynh", "stars": 0, "forks": 2, "updated_at": "2025-06-04T06:32:11Z", "language": "Shell", "archived": false}, {"id": "freqtrade_ynh", "name": "freqtrade", "description": "Trading bot", "url": "https://github.com/YunoHost-Apps/freqtrade_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:00:22Z", "language": null, "archived": false}, {"id": "freshrss_ynh", "name": "freshrss", "description": "FreshRSS package for YunoHost", "url": "https://github.com/YunoHost-Apps/freshrss_ynh", "stars": 31, "forks": 21, "updated_at": "2025-06-07T09:01:31Z", "language": "Shell", "archived": false}, {"id": "friendica_ynh", "name": "friendica", "description": "Friendica package for YunoHost", "url": "https://github.com/YunoHost-Apps/friendica_ynh", "stars": 12, "forks": 14, "updated_at": "2025-05-18T20:09:04Z", "language": "Shell", "archived": false}, {"id": "ftp_support_webapp_ynh", "name": "ftp_support_webapp", "description": "Support FTP pour webapp yunohost", "url": "https://github.com/YunoHost-Apps/ftp_support_webapp_ynh", "stars": 0, "forks": 4, "updated_at": "2024-05-15T17:49:50Z", "language": "PHP", "archived": true}, {"id": "ftpgrab_ynh", "name": "ftpgrab", "description": "FTPGrab package for YunoHost", "url": "https://github.com/YunoHost-Apps/ftpgrab_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-24T11:08:13Z", "language": "Shell", "archived": false}, {"id": "ftssolr_ynh", "name": "ftssolr", "description": "Full Text Search for IMAP via Solr", "url": "https://github.com/YunoHost-Apps/ftssolr_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:51:57Z", "language": "Shell", "archived": true}, {"id": "funkwhale_ynh", "name": "funkwhale", "description": "A modern, convivial and free music server on YunoHost", "url": "https://github.com/YunoHost-Apps/funkwhale_ynh", "stars": 80, "forks": 24, "updated_at": "2025-05-31T18:19:23Z", "language": "Shell", "archived": false}, {"id": "galene_ynh", "name": "galene", "description": "Galène package for YunoHost", "url": "https://github.com/YunoHost-Apps/galene_ynh", "stars": 11, "forks": 4, "updated_at": "2025-03-08T18:51:39Z", "language": "Shell", "archived": false}, {"id": "galette_ynh", "name": "galette", "description": "Galette package for YunoHost", "url": "https://github.com/YunoHost-Apps/galette_ynh", "stars": 7, "forks": 7, "updated_at": "2025-05-18T20:09:13Z", "language": "Shell", "archived": false}, {"id": "gamja_ynh", "name": "gamja", "description": "Gamja package for YunoHost", "url": "https://github.com/YunoHost-Apps/gamja_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-03T08:00:49Z", "language": "Shell", "archived": false}, {"id": "gancio_ynh", "name": "gancio", "description": "A shared agenda for local communities, federated with the fediverse.", "url": "https://github.com/YunoHost-Apps/gancio_ynh", "stars": 1, "forks": 5, "updated_at": "2025-04-29T16:49:24Z", "language": "Shell", "archived": false}, {"id": "garage_ynh", "name": "garage", "description": "Garage package for YunoHost", "url": "https://github.com/YunoHost-Apps/garage_ynh", "stars": 1, "forks": 5, "updated_at": "2025-06-02T14:01:18Z", "language": "Shell", "archived": false}, {"id": "garmin-to-fit<PERSON><PERSON>_ynh", "name": "garmin-to-fit<PERSON>ee", "description": "", "url": "https://github.com/YunoHost-Apps/garmin-to-fittrackee_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-01T18:19:31Z", "language": "Shell", "archived": false}, {"id": "garradin_ynh", "name": "garradin", "description": "Logiciel libre de gestion associative pour YunoHost", "url": "https://github.com/YunoHost-Apps/garradin_ynh", "stars": 7, "forks": 9, "updated_at": "2024-05-15T17:52:19Z", "language": "Shell", "archived": true}, {"id": "gekko_ynh", "name": "gekko", "description": "Gekko is a free and open source Bitcoin TA trading and backtesting platform https://gekko.wizb.it/", "url": "https://github.com/YunoHost-Apps/gekko_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T17:54:25Z", "language": "Shell", "archived": true}, {"id": "gemserv_ynh", "name": "gemserv", "description": "A gemini server written in rust.", "url": "https://github.com/YunoHost-Apps/gemserv_ynh", "stars": 10, "forks": 2, "updated_at": "2024-12-13T18:35:02Z", "language": "Shell", "archived": false}, {"id": "geoquest_ynh", "name": "geoquest", "description": "GeoQuest package for YunoHost", "url": "https://github.com/YunoHost-Apps/geoquest_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-05T08:42:14Z", "language": "Shell", "archived": false}, {"id": "getsimple_ynh", "name": "getsimple", "description": "GetSimple package for YunoHost", "url": "https://github.com/YunoHost-Apps/getsimple_ynh", "stars": 3, "forks": 2, "updated_at": "2025-05-18T20:09:16Z", "language": "PHP", "archived": false}, {"id": "ghost_ynh", "name": "ghost", "description": "Ghost package for YunoHost", "url": "https://github.com/YunoHost-Apps/ghost_ynh", "stars": 20, "forks": 30, "updated_at": "2025-06-11T09:59:16Z", "language": "Shell", "archived": false}, {"id": "gitea_ynh", "name": "g<PERSON>a", "description": "Gitea is a fork of Gogs. A git platform, Mirror of https://framagit.org/YunoHost-Apps/gitea_ynh", "url": "https://github.com/YunoHost-Apps/gitea_ynh", "stars": 25, "forks": 18, "updated_at": "2025-05-26T20:52:54Z", "language": "Shell", "archived": false}, {"id": "gitea-runner_ynh", "name": "gitea-runner", "description": "Gitea Runner package for YunoHost", "url": "https://github.com/YunoHost-Apps/gitea-runner_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:08Z", "language": "Shell", "archived": false}, {"id": "gitlab_ynh", "name": "gitlab", "description": "GitLab package for YunoHost", "url": "https://github.com/YunoHost-Apps/gitlab_ynh", "stars": 20, "forks": 9, "updated_at": "2025-05-23T23:57:45Z", "language": "<PERSON>", "archived": false}, {"id": "gitlab-runner_ynh", "name": "gitlab-runner", "description": "GitLab runner package for YunoHost", "url": "https://github.com/YunoHost-Apps/gitlab-runner_ynh", "stars": 5, "forks": 6, "updated_at": "2025-03-23T21:08:29Z", "language": "Shell", "archived": false}, {"id": "gitlist_ynh", "name": "gitlist", "description": "GitList package for YunoHost", "url": "https://github.com/YunoHost-Apps/gitlist_ynh", "stars": 3, "forks": 2, "updated_at": "2024-09-08T19:13:29Z", "language": "Shell", "archived": false}, {"id": "gitrepositories_ynh", "name": "gitrepositories", "description": "Attempt to provide private / shared / public git repositories via https in Yunohost", "url": "https://github.com/YunoHost-Apps/gitrepositories_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:57Z", "language": "Shell", "archived": true}, {"id": "glance_ynh", "name": "glance", "description": "Glance package for YunoHost", "url": "https://github.com/YunoHost-Apps/glance_ynh", "stars": 3, "forks": 1, "updated_at": "2025-06-11T10:00:31Z", "language": "Shell", "archived": false}, {"id": "glances_ynh", "name": "glances", "description": "Glances package for YunoHost", "url": "https://github.com/YunoHost-Apps/glances_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-20T17:14:14Z", "language": "Shell", "archived": false}, {"id": "glitchsoc_ynh", "name": "glitchsoc", "description": "Microblogging social network package for YunoHost", "url": "https://github.com/YunoHost-Apps/glitchsoc_ynh", "stars": 17, "forks": 10, "updated_at": "2025-04-30T15:04:28Z", "language": "Shell", "archived": false}, {"id": "glowingbear_ynh", "name": "glowingbear", "description": "Glowing Bear package for YunoHost", "url": "https://github.com/YunoHost-Apps/glowingbear_ynh", "stars": 3, "forks": 4, "updated_at": "2024-06-22T22:47:58Z", "language": "Shell", "archived": false}, {"id": "glpi_ynh", "name": "glpi", "description": "GLPI package for YunoHost ", "url": "https://github.com/YunoHost-Apps/glpi_ynh", "stars": 0, "forks": 4, "updated_at": "2025-02-15T16:51:27Z", "language": "Shell", "archived": false}, {"id": "gnusocial_ynh", "name": "gnusocial", "description": "GNU Social app with <PERSON><PERSON><PERSON> for Yunohost ", "url": "https://github.com/YunoHost-Apps/gnusocial_ynh", "stars": 6, "forks": 3, "updated_at": "2024-05-15T17:51:40Z", "language": "Shell", "archived": true}, {"id": "gogs_ynh", "name": "gogs", "description": "Gogs package for YunoHost", "url": "https://github.com/YunoHost-Apps/gogs_ynh", "stars": 18, "forks": 9, "updated_at": "2025-06-10T07:45:04Z", "language": "Shell", "archived": false}, {"id": "gogs_webhost_ynh", "name": "gogs_webhost", "description": "This Yunohost App take an existing gogs repository and serve it with nginx", "url": "https://github.com/YunoHost-Apps/gogs_webhost_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:52:03Z", "language": "Shell", "archived": true}, {"id": "gokapi_ynh", "name": "go<PERSON><PERSON>", "description": "Gokapi package for YunoHost", "url": "https://github.com/YunoHost-Apps/gokapi_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-19T20:41:53Z", "language": "Shell", "archived": false}, {"id": "gollum_ynh", "name": "gollum", "description": "A simple, Git-powered wiki with a sweet API and local frontend, packaged with YunoHost and Docker.", "url": "https://github.com/YunoHost-Apps/gollum_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:58:53Z", "language": "Shell", "archived": false}, {"id": "gomft_ynh", "name": "gomft", "description": "GoMFT package for YunoHost", "url": "https://github.com/YunoHost-Apps/gomft_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-15T15:29:39Z", "language": "Shell", "archived": false}, {"id": "google-webfonts-helper_ynh", "name": "google-webfonts-helper", "description": " Google Webfonts helper package for YunoHost", "url": "https://github.com/YunoHost-Apps/google-webfonts-helper_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-12T17:10:46Z", "language": "Shell", "archived": false}, {"id": "gossa_ynh", "name": "gossa", "description": "Gossa package for YunoHost", "url": "https://github.com/YunoHost-Apps/gossa_ynh", "stars": 4, "forks": 1, "updated_at": "2025-01-21T21:37:39Z", "language": "Shell", "archived": false}, {"id": "gotify_ynh", "name": "gotify", "description": "Gotify package for YunoHost", "url": "https://github.com/YunoHost-Apps/gotify_ynh", "stars": 6, "forks": 4, "updated_at": "2025-05-29T18:46:32Z", "language": "Shell", "archived": false}, {"id": "gotosocial_ynh", "name": "gotosocial", "description": "GoToSocial package for YunoHost: an ActivityPub social network server, written in Golang.", "url": "https://github.com/YunoHost-Apps/gotosocial_ynh", "stars": 16, "forks": 8, "updated_at": "2025-05-29T21:07:09Z", "language": "Shell", "archived": false}, {"id": "gplayweb_ynh", "name": "gplayweb", "description": "", "url": "https://github.com/YunoHost-Apps/gplayweb_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:50:47Z", "language": "Shell", "archived": true}, {"id": "grafana_ynh", "name": "grafana", "description": "Grafana package for YunoHost", "url": "https://github.com/YunoHost-Apps/grafana_ynh", "stars": 17, "forks": 12, "updated_at": "2025-04-24T11:51:22Z", "language": "Shell", "archived": false}, {"id": "grammalecte_ynh", "name": "grammalecte", "description": "Grammalecte server package for YunoHost", "url": "https://github.com/YunoHost-Apps/grammalecte_ynh", "stars": 4, "forks": 2, "updated_at": "2025-03-23T23:31:32Z", "language": "Shell", "archived": false}, {"id": "grav_ynh", "name": "grav", "description": "Grav, a flat-file CMS packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/grav_ynh", "stars": 18, "forks": 13, "updated_at": "2025-05-22T20:05:06Z", "language": "Shell", "archived": false}, {"id": "grist_ynh", "name": "grist", "description": "Grist package for YunoHost", "url": "https://github.com/YunoHost-Apps/grist_ynh", "stars": 11, "forks": 4, "updated_at": "2025-06-05T20:40:43Z", "language": "Shell", "archived": false}, {"id": "grocy_ynh", "name": "grocy", "description": "Grocy package for YunoHost", "url": "https://github.com/YunoHost-Apps/grocy_ynh", "stars": 8, "forks": 5, "updated_at": "2025-04-29T14:36:42Z", "language": "PHP", "archived": false}, {"id": "grr_ynh", "name": "grr", "description": "GRR package for YunoHost", "url": "https://github.com/YunoHost-Apps/grr_ynh", "stars": 3, "forks": 1, "updated_at": "2024-12-28T20:18:04Z", "language": "Shell", "archived": false}, {"id": "guacamole_ynh", "name": "guacamole", "description": "Apache Guacamole package for YunoHost", "url": "https://github.com/YunoHost-Apps/guacamole_ynh", "stars": 11, "forks": 16, "updated_at": "2025-03-03T14:14:04Z", "language": "Shell", "archived": false}, {"id": "gull_ynh", "name": "gull", "description": "Gull package for YunoHost", "url": "https://github.com/YunoHost-Apps/gull_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-29T21:07:15Z", "language": "Shell", "archived": false}, {"id": "h5ai_ynh", "name": "h5ai", "description": "h5ai package for YunoHost", "url": "https://github.com/YunoHost-Apps/h5ai_ynh", "stars": 12, "forks": 3, "updated_at": "2025-03-23T21:09:12Z", "language": "Shell", "archived": false}, {"id": "halcyon_ynh", "name": "halcyon", "description": "Halcyon package for YunoHost", "url": "https://github.com/YunoHost-Apps/halcyon_ynh", "stars": 5, "forks": 3, "updated_at": "2024-08-16T22:32:07Z", "language": "Shell", "archived": false}, {"id": "haste_ynh", "name": "haste", "description": "Haste package for YunoHost", "url": "https://github.com/YunoHost-Apps/haste_ynh", "stars": 3, "forks": 5, "updated_at": "2024-09-08T11:01:00Z", "language": "Shell", "archived": false}, {"id": "hat_ynh", "name": "hat", "description": "Hat.sh package for YunoHost", "url": "https://github.com/YunoHost-Apps/hat_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-11T21:42:12Z", "language": "Shell", "archived": false}, {"id": "headphones_ynh", "name": "headphones", "description": "Headphones package for YunoHost ", "url": "https://github.com/YunoHost-Apps/headphones_ynh", "stars": 6, "forks": 2, "updated_at": "2024-08-15T21:01:57Z", "language": "Shell", "archived": false}, {"id": "headplane_ynh", "name": "headplane", "description": "An advanced UI for headscale", "url": "https://github.com/YunoHost-Apps/headplane_ynh", "stars": 5, "forks": 0, "updated_at": "2025-05-27T22:08:10Z", "language": "Shell", "archived": false}, {"id": "headscale_ynh", "name": "headscale", "description": "FOSS implementation of the Tailscale control server, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/headscale_ynh", "stars": 12, "forks": 6, "updated_at": "2025-06-07T08:58:41Z", "language": "Shell", "archived": false}, {"id": "healthchecks_ynh", "name": "healthchecks", "description": "A cron monitoring tool written in Python & Django ", "url": "https://github.com/YunoHost-Apps/healthchecks_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:04:19Z", "language": "Shell", "archived": false}, {"id": "hedgedoc_ynh", "name": "hedgedoc", "description": "HedgeDoc package for YunoHost", "url": "https://github.com/YunoHost-Apps/hedgedoc_ynh", "stars": 13, "forks": 8, "updated_at": "2025-04-12T13:21:03Z", "language": "Shell", "archived": false}, {"id": "heisenbridge_ynh", "name": "heise<PERSON>", "description": "YunoHost package for Heisenbridge, a bouncer-style Matrix IRC bridge", "url": "https://github.com/YunoHost-Apps/heisenbridge_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:05:50Z", "language": null, "archived": false}, {"id": "helloworld_ynh", "name": "helloworld", "description": "A simple, dummy, basic app to illustrate how YunoHost packaging works", "url": "https://github.com/YunoHost-Apps/helloworld_ynh", "stars": 3, "forks": 10, "updated_at": "2025-01-21T21:13:49Z", "language": "Shell", "archived": false}, {"id": "hextris_ynh", "name": "hex<PERSON>s", "description": "A fast paced puzzle game package for YunoHost.", "url": "https://github.com/YunoHost-Apps/hextris_ynh", "stars": 2, "forks": 4, "updated_at": "2025-04-02T20:10:48Z", "language": "Shell", "archived": false}, {"id": "homarr_ynh", "name": "homarr", "description": "Homarr package for YunoHost", "url": "https://github.com/YunoHost-Apps/homarr_ynh", "stars": 6, "forks": 6, "updated_at": "2025-05-18T20:09:21Z", "language": "Shell", "archived": false}, {"id": "homeassistant_ynh", "name": "homeassistant", "description": "Home automation platform package for YunoHost", "url": "https://github.com/YunoHost-Apps/homeassistant_ynh", "stars": 21, "forks": 16, "updated_at": "2025-05-24T05:50:32Z", "language": "Shell", "archived": false}, {"id": "homebox_ynh", "name": "homebox", "description": "HomeBox package for YunoHost", "url": "https://github.com/YunoHost-Apps/homebox_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-15T19:52:07Z", "language": "Shell", "archived": false}, {"id": "homer_ynh", "name": "homer", "description": "Homer package for YunoHost", "url": "https://github.com/YunoHost-Apps/homer_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T18:00:59Z", "language": "Shell", "archived": false}, {"id": "horde_ynh", "name": "horde", "description": "A Groupware and webmail", "url": "https://github.com/YunoHost-Apps/horde_ynh", "stars": 3, "forks": 2, "updated_at": "2024-06-23T11:31:52Z", "language": "PHP", "archived": false}, {"id": "hotglue_ynh", "name": "hotglue", "description": "Hotglue package for YunoHost", "url": "https://github.com/YunoHost-Apps/hotglue_ynh", "stars": 1, "forks": 0, "updated_at": "2025-05-12T06:40:45Z", "language": "Shell", "archived": false}, {"id": "hotspot_ynh", "name": "hotspot", "description": "Wifi Hotspot app for YunoHost", "url": "https://github.com/YunoHost-Apps/hotspot_ynh", "stars": 42, "forks": 19, "updated_at": "2025-01-11T05:42:10Z", "language": "Shell", "archived": false}, {"id": "htpc-manager_ynh", "name": "htpc-manager", "description": "HTPC-Manager package for Yunohost (install from fork)", "url": "https://github.com/YunoHost-Apps/htpc-manager_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:50:46Z", "language": "Shell", "archived": true}, {"id": "httpsh_ynh", "name": "httpsh", "description": "ttyd package for YunoHost, a web terminal client", "url": "https://github.com/YunoHost-Apps/httpsh_ynh", "stars": 2, "forks": 1, "updated_at": "2025-05-29T21:00:35Z", "language": "Shell", "archived": false}, {"id": "hubzilla_ynh", "name": "hubzilla", "description": "Hubzilla Hub package for YunoHost", "url": "https://github.com/YunoHost-Apps/hubzilla_ynh", "stars": 16, "forks": 9, "updated_at": "2025-04-12T14:31:55Z", "language": "Shell", "archived": false}, {"id": "huginn_ynh", "name": "hug<PERSON>", "description": "", "url": "https://github.com/YunoHost-Apps/huginn_ynh", "stars": 9, "forks": 9, "updated_at": "2025-03-24T22:29:17Z", "language": "Shell", "archived": false}, {"id": "hugo_ynh", "name": "hugo", "description": "Static site generator, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/hugo_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-02T10:11:51Z", "language": "Shell", "archived": false}, {"id": "humhub_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "HumHub package for YunoHost", "url": "https://github.com/YunoHost-Apps/humhub_ynh", "stars": 3, "forks": 8, "updated_at": "2024-09-12T13:26:27Z", "language": "Shell", "archived": false}, {"id": "hydrogen_ynh", "name": "hydrogen", "description": "Hydrogen package for YunoHost", "url": "https://github.com/YunoHost-Apps/hydrogen_ynh", "stars": 1, "forks": 2, "updated_at": "2025-04-06T21:22:18Z", "language": "Shell", "archived": false}, {"id": "icecast2_ynh", "name": "icecast2", "description": "Icecast2 package for YunoHost", "url": "https://github.com/YunoHost-Apps/icecast2_ynh", "stars": 0, "forks": 1, "updated_at": "2025-01-31T12:22:01Z", "language": "Shell", "archived": false}, {"id": "icecoder_ynh", "name": "icecoder", "description": "ICEcoder package for YunoHost.", "url": "https://github.com/YunoHost-Apps/icecoder_ynh", "stars": 3, "forks": 1, "updated_at": "2024-05-15T17:59:09Z", "language": "Shell", "archived": true}, {"id": "iceshrimp_ynh", "name": "iceshrimp", "description": "Iceshrimp is a decentralized and federated social networking service, implementing the ActivityPub standard.", "url": "https://github.com/YunoHost-Apps/iceshrimp_ynh", "stars": 3, "forks": 3, "updated_at": "2024-09-28T12:40:23Z", "language": "Shell", "archived": false}, {"id": "ifconfig-io_ynh", "name": "ifconfig-io", "description": "IP API service", "url": "https://github.com/YunoHost-Apps/ifconfig-io_ynh", "stars": 4, "forks": 2, "updated_at": "2025-04-24T07:57:04Z", "language": "Shell", "archived": false}, {"id": "ifm_ynh", "name": "ifm", "description": "Improved File Manager package for YunoHost", "url": "https://github.com/YunoHost-Apps/ifm_ynh", "stars": 4, "forks": 2, "updated_at": "2025-02-13T22:50:24Z", "language": "Shell", "archived": false}, {"id": "ihatemoney_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ihatemoney package for YunoHost", "url": "https://github.com/YunoHost-Apps/ihatemoney_ynh", "stars": 9, "forks": 6, "updated_at": "2025-03-25T19:37:22Z", "language": "Shell", "archived": false}, {"id": "immich_ynh", "name": "immich", "description": "Self-hosted photo and video backup solution directly from your mobile phone.", "url": "https://github.com/YunoHost-Apps/immich_ynh", "stars": 13, "forks": 8, "updated_at": "2025-05-28T17:04:05Z", "language": "Shell", "archived": false}, {"id": "incus_ynh", "name": "incus", "description": "Powerful system container and virtual machine manager ", "url": "https://github.com/YunoHost-Apps/incus_ynh", "stars": 1, "forks": 1, "updated_at": "2025-01-06T12:52:29Z", "language": "Shell", "archived": false}, {"id": "indexhibit_ynh", "name": "indexhibit", "description": "Indexhibit package for YunoHost", "url": "https://github.com/YunoHost-Apps/indexhibit_ynh", "stars": 5, "forks": 2, "updated_at": "2025-01-01T11:03:06Z", "language": "Shell", "archived": false}, {"id": "indico_ynh", "name": "indico", "description": "Indico package for YunoHost", "url": "https://github.com/YunoHost-Apps/indico_ynh", "stars": 1, "forks": 1, "updated_at": "2025-05-18T20:09:17Z", "language": "Shell", "archived": false}, {"id": "influxdb_v2_ynh", "name": "influxdb_v2", "description": "InflixDB package for YunoHost", "url": "https://github.com/YunoHost-Apps/influxdb_v2_ynh", "stars": 1, "forks": 2, "updated_at": "2025-03-23T23:32:05Z", "language": "Shell", "archived": false}, {"id": "internetarchive_ynh", "name": "internetarchive", "description": "Internet Archive offline for Yunohost", "url": "https://github.com/YunoHost-Apps/internetarchive_ynh", "stars": 3, "forks": 0, "updated_at": "2024-06-19T09:17:52Z", "language": "Shell", "archived": true}, {"id": "invidious_ynh", "name": "invidious", "description": "Invidious package for YunoHost", "url": "https://github.com/YunoHost-Apps/invidious_ynh", "stars": 19, "forks": 8, "updated_at": "2025-05-24T06:24:32Z", "language": "Shell", "archived": false}, {"id": "invoiceninja_ynh", "name": "invoiceninja", "description": "InvoiceNinja package for YunoHost", "url": "https://github.com/YunoHost-Apps/invoiceninja_ynh", "stars": 6, "forks": 3, "updated_at": "2024-06-22T22:48:12Z", "language": "Shell", "archived": false}, {"id": "invoiceninja5_ynh", "name": "invoiceninja5", "description": "self-hosted invoicing platform to create & email invoices, track payments and expenses, and time billable tasks & projects for clients.", "url": "https://github.com/YunoHost-Apps/invoiceninja5_ynh", "stars": 11, "forks": 6, "updated_at": "2025-06-06T05:12:16Z", "language": "Shell", "archived": false}, {"id": "invoiceshelf_ynh", "name": "invoiceshelf", "description": "InvoiceShelf package for YunoHost", "url": "https://github.com/YunoHost-Apps/invoiceshelf_ynh", "stars": 0, "forks": 0, "updated_at": "2025-02-10T19:59:13Z", "language": "Shell", "archived": false}, {"id": "ipfs_ynh", "name": "ipfs", "description": "IPFS package for YunoHost", "url": "https://github.com/YunoHost-Apps/ipfs_ynh", "stars": 6, "forks": 0, "updated_at": "2024-05-30T12:31:46Z", "language": "Shell", "archived": true}, {"id": "isso_ynh", "name": "isso", "description": "a Disqus alternative", "url": "https://github.com/YunoHost-Apps/isso_ynh", "stars": 4, "forks": 1, "updated_at": "2024-10-21T07:03:42Z", "language": "Shell", "archived": false}, {"id": "it-tools_ynh", "name": "it-tools", "description": "Collection of handy online tools for people working in IT.", "url": "https://github.com/YunoHost-Apps/it-tools_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-09T23:49:42Z", "language": "Shell", "archived": false}, {"id": "itflow_ynh", "name": "itflow", "description": "Itflow package for YunoHost", "url": "https://github.com/YunoHost-Apps/itflow_ynh", "stars": 2, "forks": 1, "updated_at": "2025-06-02T07:43:41Z", "language": "Shell", "archived": false}, {"id": "jackett_ynh", "name": "jackett", "description": "Jackett package for YunoHost", "url": "https://github.com/YunoHost-Apps/jackett_ynh", "stars": 0, "forks": 4, "updated_at": "2025-05-31T07:03:08Z", "language": "Shell", "archived": false}, {"id": "jang<PERSON>_ynh", "name": "jangouts", "description": "Jangouts package for YunoHost", "url": "https://github.com/YunoHost-Apps/jangouts_ynh", "stars": 0, "forks": 0, "updated_at": "2025-02-09T11:10:04Z", "language": "Shell", "archived": false}, {"id": "jappix_ynh", "name": "jappix", "description": "Jappix package for YunoHost", "url": "https://github.com/YunoHost-Apps/jappix_ynh", "stars": 5, "forks": 6, "updated_at": "2024-05-21T23:28:40Z", "language": "Shell", "archived": true}, {"id": "jappix_mini_ynh", "name": "jappix_mini", "description": "[Not working and no more maintained] Jappix Mini package for YunoHost", "url": "https://github.com/YunoHost-Apps/jappix_mini_ynh", "stars": 3, "forks": 0, "updated_at": "2024-05-15T17:51:00Z", "language": "JavaScript", "archived": true}, {"id": "jdownloader_ynh", "name": "jdownloader", "description": "JDownloader app for YunoHost", "url": "https://github.com/YunoHost-Apps/jdownloader_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:55:33Z", "language": "Shell", "archived": true}, {"id": "jeedom_ynh", "name": "j<PERSON>om", "description": "Jeedom package for YunoHost", "url": "https://github.com/YunoHost-Apps/jeedom_ynh", "stars": 2, "forks": 1, "updated_at": "2025-02-11T23:20:37Z", "language": "Shell", "archived": false}, {"id": "jellyfin_ynh", "name": "jellyfin", "description": "Jellyfin package for YunoHost", "url": "https://github.com/YunoHost-Apps/jellyfin_ynh", "stars": 30, "forks": 22, "updated_at": "2025-05-09T14:01:52Z", "language": "Shell", "archived": false}, {"id": "jellyfin-vue_ynh", "name": "jellyfin-vue", "description": "YunoHost package for the modern web client for Jellyfin", "url": "https://github.com/YunoHost-Apps/jellyfin-vue_ynh", "stars": 0, "forks": 3, "updated_at": "2025-06-10T09:08:45Z", "language": "Shell", "archived": false}, {"id": "jellyseerr_ynh", "name": "jellyseerr", "description": "Jellyseer package for YunoHost", "url": "https://github.com/YunoHost-Apps/jellyseerr_ynh", "stars": 1, "forks": 4, "updated_at": "2025-06-11T05:41:44Z", "language": "Shell", "archived": false}, {"id": "jenkins_ynh", "name": "jenkins", "description": "Jenkins package for YunoHost", "url": "https://github.com/YunoHost-Apps/jenkins_ynh", "stars": 6, "forks": 10, "updated_at": "2025-06-07T08:50:52Z", "language": "Shell", "archived": false}, {"id": "jira<PERSON><PERSON>_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Jirafeau package for YunoHost", "url": "https://github.com/YunoHost-Apps/jirafeau_ynh", "stars": 13, "forks": 14, "updated_at": "2025-03-05T22:19:49Z", "language": "Shell", "archived": false}, {"id": "jitsi_ynh", "name": "jitsi", "description": "Video conference for YunoHost", "url": "https://github.com/YunoHost-Apps/jitsi_ynh", "stars": 21, "forks": 19, "updated_at": "2025-03-18T19:40:03Z", "language": "JavaScript", "archived": false}, {"id": "joomla_ynh", "name": "j<PERSON><PERSON>", "description": "Joomla! is an award-winning content management system ", "url": "https://github.com/YunoHost-Apps/joomla_ynh", "stars": 2, "forks": 6, "updated_at": "2025-05-29T19:27:50Z", "language": "Shell", "archived": false}, {"id": "joplin_ynh", "name": "j<PERSON>lin", "description": "Joplin server package for YunoHost", "url": "https://github.com/YunoHost-Apps/joplin_ynh", "stars": 7, "forks": 4, "updated_at": "2025-05-21T06:06:26Z", "language": "Shell", "archived": false}, {"id": "jsoncrack_ynh", "name": "jsoncrack", "description": "Visualization application that transforms various data formats, such as JSON, YAML, XML, CSV and more, into interactive graphs.", "url": "https://github.com/YunoHost-Apps/jsoncrack_ynh", "stars": 2, "forks": 0, "updated_at": "2025-06-02T02:40:31Z", "language": "Shell", "archived": false}, {"id": "jump_ynh", "name": "jump", "description": "Jump is a self-hosted startpage and real-time status page for your server designed to be simple, stylish, fast and secure.", "url": "https://github.com/YunoHost-Apps/jump_ynh", "stars": 0, "forks": 0, "updated_at": "2024-06-23T11:32:26Z", "language": "Shell", "archived": false}, {"id": "jupyterlab_ynh", "name": "jupyterlab", "description": "JupyterLab package for YunoHost", "url": "https://github.com/YunoHost-Apps/jupyterlab_ynh", "stars": 14, "forks": 6, "updated_at": "2025-04-10T01:54:29Z", "language": "Python", "archived": false}, {"id": "kanboard_ynh", "name": "kanboard", "description": "Kanboard package for YunoHost", "url": "https://github.com/YunoHost-Apps/kanboard_ynh", "stars": 16, "forks": 17, "updated_at": "2025-05-24T06:23:08Z", "language": "PHP", "archived": false}, {"id": "karadav_ynh", "name": "karadav", "description": "KaraDAV packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/karadav_ynh", "stars": 1, "forks": 0, "updated_at": "2024-02-03T18:00:52Z", "language": "Shell", "archived": false}, {"id": "karak<PERSON>_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Karakeep package for YunoHost", "url": "https://github.com/YunoHost-Apps/karakeep_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-23T15:17:35Z", "language": "Shell", "archived": false}, {"id": "karaokeforever_ynh", "name": "karaokeforever", "description": "karaoke app package for YunoHost", "url": "https://github.com/YunoHost-Apps/karaokeforever_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T18:03:07Z", "language": "Shell", "archived": false}, {"id": "kavita_ynh", "name": "kavita", "description": "Kavita package for YunoHost", "url": "https://github.com/YunoHost-Apps/kavita_ynh", "stars": 3, "forks": 4, "updated_at": "2025-04-24T07:50:47Z", "language": "Shell", "archived": false}, {"id": "kbin_ynh", "name": "kbin", "description": "Kbin is a decentralized content aggregator and microblogging platform running on the Fediverse network.", "url": "https://github.com/YunoHost-Apps/kbin_ynh", "stars": 6, "forks": 1, "updated_at": "2025-04-14T11:21:01Z", "language": "Shell", "archived": false}, {"id": "keeweb_ynh", "name": "keeweb", "description": "Keeweb package for YunoHost", "url": "https://github.com/YunoHost-Apps/keeweb_ynh", "stars": 13, "forks": 7, "updated_at": "2024-06-22T22:48:21Z", "language": "HTML", "archived": false}, {"id": "khatru-pyramid_ynh", "name": "khatru-pyramid", "description": "", "url": "https://github.com/YunoHost-Apps/khatru-pyramid_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-15T16:34:19Z", "language": "Shell", "archived": false}, {"id": "kibana_ynh", "name": "kibana", "description": "", "url": "https://github.com/YunoHost-Apps/kibana_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:56:51Z", "language": "Shell", "archived": true}, {"id": "kimai2_ynh", "name": "kimai2", "description": "Kimai v2 package for YunoHost", "url": "https://github.com/YunoHost-Apps/kimai2_ynh", "stars": 13, "forks": 9, "updated_at": "2025-05-19T20:31:28Z", "language": "Shell", "archived": false}, {"id": "kitchenowl_ynh", "name": "kitchenowl", "description": "KitchenOwl package for YunoHost", "url": "https://github.com/YunoHost-Apps/kitchenowl_ynh", "stars": 1, "forks": 2, "updated_at": "2024-11-07T20:18:46Z", "language": "Shell", "archived": false}, {"id": "kiwiirc_ynh", "name": "kiwiirc", "description": "KiwiIRC package for YunoHost", "url": "https://github.com/YunoHost-Apps/kiwiirc_ynh", "stars": 2, "forks": 1, "updated_at": "2025-03-24T20:28:07Z", "language": "Shell", "archived": false}, {"id": "kiwix_ynh", "name": "kiwix", "description": "Kiwix package for YunoHost", "url": "https://github.com/YunoHost-Apps/kiwix_ynh", "stars": 3, "forks": 6, "updated_at": "2025-05-21T21:59:00Z", "language": "Shell", "archived": false}, {"id": "kodi_ynh", "name": "kodi", "description": "Kodi package for YunoHost", "url": "https://github.com/YunoHost-Apps/kodi_ynh", "stars": 5, "forks": 4, "updated_at": "2025-04-29T19:24:35Z", "language": "Shell", "archived": false}, {"id": "koel_ynh", "name": "koel", "description": "A personal music streaming server, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/koel_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-03T07:52:30Z", "language": "Shell", "archived": false}, {"id": "komga_ynh", "name": "komga", "description": "Komga package for YunoHost", "url": "https://github.com/YunoHost-Apps/komga_ynh", "stars": 5, "forks": 4, "updated_at": "2025-02-24T16:10:46Z", "language": "Shell", "archived": false}, {"id": "kresus_ynh", "name": "kres<PERSON>", "description": "<PERSON><PERSON><PERSON> (personal finance manager) for YunoHost", "url": "https://github.com/YunoHost-Apps/kresus_ynh", "stars": 20, "forks": 21, "updated_at": "2025-06-04T16:40:50Z", "language": "Shell", "archived": false}, {"id": "kutt_ynh", "name": "kutt", "description": "Kutt package for YunoHost", "url": "https://github.com/YunoHost-Apps/kutt_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-06T08:19:30Z", "language": "Shell", "archived": false}, {"id": "ladder_ynh", "name": "ladder", "description": "Alternative to 12ft.io. and 1ft.io to bypass paywalls with a proxy", "url": "https://github.com/YunoHost-Apps/ladder_ynh", "stars": 1, "forks": 1, "updated_at": "2024-11-22T17:41:51Z", "language": "Shell", "archived": false}, {"id": "languagetool_ynh", "name": "languagetool", "description": "LanguageTool server package for YunoHost", "url": "https://github.com/YunoHost-Apps/languagetool_ynh", "stars": 4, "forks": 5, "updated_at": "2025-03-23T13:57:07Z", "language": "Shell", "archived": false}, {"id": "laverna_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Laverna package for YunoHost ", "url": "https://github.com/YunoHost-Apps/laverna_ynh", "stars": 4, "forks": 4, "updated_at": "2024-06-22T22:29:30Z", "language": "Shell", "archived": false}, {"id": "LBCAlerte_ynh", "name": "LBCAlerte", "description": "LBCAlerte package for YunoHost", "url": "https://github.com/YunoHost-Apps/LBCAlerte_ynh", "stars": 3, "forks": 2, "updated_at": "2024-05-15T17:52:00Z", "language": "Shell", "archived": true}, {"id": "leantime_ynh", "name": "leantime", "description": "Leantime package for YunoHost", "url": "https://github.com/YunoHost-Apps/leantime_ynh", "stars": 1, "forks": 5, "updated_at": "2025-06-02T07:37:16Z", "language": "Shell", "archived": false}, {"id": "leed_ynh", "name": "leed", "description": "Leed package for YunoHost", "url": "https://github.com/YunoHost-Apps/leed_ynh", "stars": 4, "forks": 4, "updated_at": "2025-06-05T19:44:24Z", "language": "Shell", "archived": false}, {"id": "lektor_ynh", "name": "lektor", "description": "Lektor package for YunoHost", "url": "https://github.com/YunoHost-Apps/lektor_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:51:06Z", "language": "Shell", "archived": true}, {"id": "lemmy_ynh", "name": "lemmy", "description": "A link aggregator for the fediverse.", "url": "https://github.com/YunoHost-Apps/lemmy_ynh", "stars": 20, "forks": 12, "updated_at": "2025-05-26T05:56:55Z", "language": "Shell", "archived": false}, {"id": "letsencrypt_ynh", "name": "letsencrypt", "description": "[No more maintain, Let's Encrypt is integrated on YunoHost 2.5] LE certificate install on YunoHost", "url": "https://github.com/YunoHost-Apps/letsencrypt_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T17:51:37Z", "language": "Shell", "archived": true}, {"id": "liberaforms_ynh", "name": "liberaforms", "description": "LiberaForms package for YunoHost", "url": "https://github.com/YunoHost-Apps/liberaforms_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-21T13:28:31Z", "language": "Shell", "archived": false}, {"id": "librarian_ynh", "name": "librarian", "description": "Librarian package for YunoHost", "url": "https://github.com/YunoHost-Apps/librarian_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T18:02:32Z", "language": "Shell", "archived": true}, {"id": "libreerp_ynh", "name": "lib<PERSON><PERSON>", "description": "LibreERP package for YunoHost", "url": "https://github.com/YunoHost-Apps/libreerp_ynh", "stars": 16, "forks": 19, "updated_at": "2024-12-15T11:20:27Z", "language": "Shell", "archived": false}, {"id": "libremdb_ynh", "name": "libremdb", "description": "Libremdb package for YunoHost", "url": "https://github.com/YunoHost-Apps/libremdb_ynh", "stars": 2, "forks": 1, "updated_at": "2025-06-07T08:46:56Z", "language": "Shell", "archived": false}, {"id": "librephotos_ynh", "name": "librephotos", "description": "Librephotos package for YunoHost", "url": "https://github.com/YunoHost-Apps/librephotos_ynh", "stars": 4, "forks": 3, "updated_at": "2025-04-29T14:34:39Z", "language": "Shell", "archived": false}, {"id": "libresonic_ynh", "name": "libresonic", "description": "libresonic_ynh", "url": "https://github.com/YunoHost-Apps/libresonic_ynh", "stars": 2, "forks": 1, "updated_at": "2024-05-15T17:51:51Z", "language": "Shell", "archived": true}, {"id": "librespeed_ynh", "name": "librespeed", "description": "librespeed package for YunoHost", "url": "https://github.com/YunoHost-Apps/librespeed_ynh", "stars": 2, "forks": 1, "updated_at": "2025-01-21T21:27:44Z", "language": "HTML", "archived": false}, {"id": "libretime_ynh", "name": "libretime", "description": "LibreTime package for YunoHost", "url": "https://github.com/YunoHost-Apps/libretime_ynh", "stars": 3, "forks": 0, "updated_at": "2025-04-14T12:39:09Z", "language": "Shell", "archived": false}, {"id": "libreto_ynh", "name": "libreto", "description": "LibreTo package for YunoHost", "url": "https://github.com/YunoHost-Apps/libreto_ynh", "stars": 6, "forks": 5, "updated_at": "2025-05-19T20:17:48Z", "language": "Shell", "archived": false}, {"id": "libretranslate_ynh", "name": "libretranslate", "description": "LibreTranslate package for YunHost", "url": "https://github.com/YunoHost-Apps/libretranslate_ynh", "stars": 3, "forks": 5, "updated_at": "2025-03-27T06:33:18Z", "language": "Shell", "archived": false}, {"id": "librex_ynh", "name": "librex", "description": "LibreX package for YunoHost", "url": "https://github.com/YunoHost-Apps/librex_ynh", "stars": 5, "forks": 1, "updated_at": "2025-03-23T23:32:27Z", "language": "Shell", "archived": false}, {"id": "lichenmarkdown_ynh", "name": "lichenmarkdown", "description": "Lichen-Markdown package for YunoHost", "url": "https://github.com/YunoHost-Apps/lichenmarkdown_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-29T15:45:58Z", "language": "Shell", "archived": false}, {"id": "lidarr_ynh", "name": "lidarr", "description": "Music collection manager for Usenet and BitTorrent users, packaged for YunoHost ", "url": "https://github.com/YunoHost-Apps/lidarr_ynh", "stars": 1, "forks": 1, "updated_at": "2025-05-02T20:14:36Z", "language": "Shell", "archived": false}, {"id": "limesurvey_ynh", "name": "limesurvey", "description": "LimeSurvey package for yunoHost", "url": "https://github.com/YunoHost-Apps/limesurvey_ynh", "stars": 5, "forks": 13, "updated_at": "2025-06-11T10:01:52Z", "language": "Shell", "archived": false}, {"id": "lingva_ynh", "name": "lingva", "description": "Lingva Translate package for YunoHost", "url": "https://github.com/YunoHost-Apps/lingva_ynh", "stars": 0, "forks": 2, "updated_at": "2025-03-24T16:27:37Z", "language": "Shell", "archived": false}, {"id": "linkstack_ynh", "name": "linkstack", "description": "LinkStack package for YunoHost", "url": "https://github.com/YunoHost-Apps/linkstack_ynh", "stars": 5, "forks": 1, "updated_at": "2025-06-07T08:57:10Z", "language": "Shell", "archived": false}, {"id": "linkwarden_ynh", "name": "linkwarden", "description": "Collaborative bookmark manager to collect, organize and archive webpages.", "url": "https://github.com/YunoHost-Apps/linkwarden_ynh", "stars": 4, "forks": 1, "updated_at": "2025-06-11T13:41:11Z", "language": "Shell", "archived": false}, {"id": "linuxdash_ynh", "name": "linuxdash", "description": "Linux-Dash package for YunoHost", "url": "https://github.com/YunoHost-Apps/linuxdash_ynh", "stars": 6, "forks": 2, "updated_at": "2024-06-23T11:33:00Z", "language": "Shell", "archived": false}, {"id": "lionwiki-t2t_ynh", "name": "lionwiki-t2t", "description": "Lionwiki-t2t package for YunoHost", "url": "https://github.com/YunoHost-Apps/lionwiki-t2t_ynh", "stars": 1, "forks": 3, "updated_at": "2025-05-02T20:13:15Z", "language": "PHP", "archived": false}, {"id": "listmonk_ynh", "name": "listmonk", "description": "Listmonk package for YunoHost", "url": "https://github.com/YunoHost-Apps/listmonk_ynh", "stars": 5, "forks": 3, "updated_at": "2025-06-08T07:53:42Z", "language": "Shell", "archived": false}, {"id": "litecart_ynh", "name": "litecart", "description": "litecart package for YunoHost", "url": "https://github.com/YunoHost-Apps/litecart_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-12T00:42:08Z", "language": "Shell", "archived": false}, {"id": "lnbits_ynh", "name": "lnbits", "description": "LNbits, free and open-source lightning-network wallet/accounts system.", "url": "https://github.com/YunoHost-Apps/lnbits_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T18:03:12Z", "language": "Shell", "archived": false}, {"id": "localai_ynh", "name": "localai", "description": "Free, Open Source OpenAI alternative", "url": "https://github.com/YunoHost-Apps/localai_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-14T16:51:49Z", "language": "Shell", "archived": false}, {"id": "logdy_ynh", "name": "logdy", "description": "", "url": "https://github.com/YunoHost-Apps/logdy_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-22T17:40:33Z", "language": "Shell", "archived": false}, {"id": "loki_ynh", "name": "loki", "description": "Grafana Loki is a set of components that can be composed into a fully featured logging stack.", "url": "https://github.com/YunoHost-Apps/loki_ynh", "stars": 3, "forks": 1, "updated_at": "2025-06-07T08:46:10Z", "language": "Shell", "archived": false}, {"id": "lstu_ynh", "name": "lstu", "description": "URL Shortener package for YunoHost", "url": "https://github.com/YunoHost-Apps/lstu_ynh", "stars": 9, "forks": 11, "updated_at": "2025-01-21T16:23:49Z", "language": "<PERSON><PERSON>", "archived": false}, {"id": "luckysheet_ynh", "name": "luckysheet", "description": "Luckysheet package for YunoHost", "url": "https://github.com/YunoHost-Apps/luckysheet_ynh", "stars": 2, "forks": 1, "updated_at": "2024-09-03T21:23:33Z", "language": "Shell", "archived": false}, {"id": "lufi_ynh", "name": "lufi", "description": "Lufi package for YunoHost", "url": "https://github.com/YunoHost-Apps/lufi_ynh", "stars": 16, "forks": 9, "updated_at": "2025-03-24T01:36:06Z", "language": "<PERSON><PERSON>", "archived": false}, {"id": "lutim_ynh", "name": "lutim", "description": "Lutim package for YunoHost", "url": "https://github.com/YunoHost-Apps/lutim_ynh", "stars": 6, "forks": 9, "updated_at": "2025-04-24T17:28:46Z", "language": "Shell", "archived": false}, {"id": "lxd_ynh", "name": "lxd", "description": "LXD package for YunoHost", "url": "https://github.com/YunoHost-Apps/lxd_ynh", "stars": 10, "forks": 3, "updated_at": "2025-03-23T00:38:03Z", "language": "Shell", "archived": false}, {"id": "lxd-dashboard_ynh", "name": "lxd-dashboard", "description": "LXD dashboard package for YunoHost", "url": "https://github.com/YunoHost-Apps/lxd-dashboard_ynh", "stars": 3, "forks": 2, "updated_at": "2024-12-11T00:38:45Z", "language": "Shell", "archived": false}, {"id": "lychee_ynh", "name": "lychee", "description": "Lychee package for YunoHost", "url": "https://github.com/YunoHost-Apps/lychee_ynh", "stars": 10, "forks": 7, "updated_at": "2025-05-28T08:47:17Z", "language": "Shell", "archived": false}, {"id": "magic-wormhole_ynh", "name": "magic-wormhole", "description": "Magic Wormhole package for YunoHost", "url": "https://github.com/YunoHost-Apps/magic-wormhole_ynh", "stars": 0, "forks": 0, "updated_at": "2024-12-17T21:37:38Z", "language": "Shell", "archived": false}, {"id": "mailbag_ynh", "name": "mailbag", "description": "MailBag package for YunoHost", "url": "https://github.com/YunoHost-Apps/mailbag_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:37Z", "language": "Shell", "archived": false}, {"id": "mailman_ynh", "name": "mailman", "description": "Mailman packaging for YunoHost", "url": "https://github.com/YunoHost-Apps/mailman_ynh", "stars": 10, "forks": 1, "updated_at": "2024-06-19T09:14:14Z", "language": "Shell", "archived": true}, {"id": "mailman3_ynh", "name": "mailman3", "description": "Mailman - The GNU Mailing List Management System packaged for YunoHost.", "url": "https://github.com/YunoHost-Apps/mailman3_ynh", "stars": 7, "forks": 4, "updated_at": "2025-01-02T00:17:52Z", "language": "Shell", "archived": false}, {"id": "mantis_ynh", "name": "mantis", "description": "Mantis package for YunoHost", "url": "https://github.com/YunoHost-Apps/mantis_ynh", "stars": 3, "forks": 1, "updated_at": "2025-04-16T10:21:00Z", "language": "Shell", "archived": false}, {"id": "many-notes_ynh", "name": "many-notes", "description": "Many Notes package for YunoHost", "url": "https://github.com/YunoHost-Apps/many-notes_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-26T05:56:15Z", "language": "Shell", "archived": false}, {"id": "marl_ynh", "name": "marl", "description": "Mastodon Archive Reader Lite - a lightweight single-page app to explore the contents of your Mastodon archive file", "url": "https://github.com/YunoHost-Apps/marl_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-31T01:25:03Z", "language": "Shell", "archived": false}, {"id": "mastodon_ynh", "name": "mastodon", "description": "Free, open-source social network for YunoHost", "url": "https://github.com/YunoHost-Apps/mastodon_ynh", "stars": 86, "forks": 39, "updated_at": "2025-06-03T07:34:53Z", "language": "Shell", "archived": false}, {"id": "matomo_ynh", "name": "matomo", "description": "Matomo package for YunoHost", "url": "https://github.com/YunoHost-Apps/matomo_ynh", "stars": 19, "forks": 7, "updated_at": "2025-05-18T20:09:40Z", "language": "Shell", "archived": false}, {"id": "matrix-appservice-irc_ynh", "name": "matrix-appservice-irc", "description": "Node.js IRC bridge for Matrix - Yunohost package", "url": "https://github.com/YunoHost-Apps/matrix-appservice-irc_ynh", "stars": 1, "forks": 2, "updated_at": "2025-01-11T05:48:54Z", "language": "Shell", "archived": false}, {"id": "matrix-puppet-discord_ynh", "name": "matrix-puppet-discord", "description": "", "url": "https://github.com/YunoHost-Apps/matrix-puppet-discord_ynh", "stars": 4, "forks": 3, "updated_at": "2024-06-04T21:00:18Z", "language": "Shell", "archived": true}, {"id": "matterbridge_ynh", "name": "matterbridge", "description": "Matterbridge package for YunoHost", "url": "https://github.com/YunoHost-Apps/matterbridge_ynh", "stars": 11, "forks": 4, "updated_at": "2025-01-11T05:47:14Z", "language": "Shell", "archived": false}, {"id": "mattermost_ynh", "name": "mattermost", "description": "Mattermost package for YunoHost", "url": "https://github.com/YunoHost-Apps/mattermost_ynh", "stars": 35, "forks": 19, "updated_at": "2025-05-31T12:29:59Z", "language": "Shell", "archived": false}, {"id": "mautic_ynh", "name": "mautic", "description": "Mautic package for YunoHost", "url": "https://github.com/YunoHost-Apps/mautic_ynh", "stars": 0, "forks": 3, "updated_at": "2025-05-29T06:40:46Z", "language": "Shell", "archived": false}, {"id": "mautrix_discord_ynh", "name": "mautrix_discord", "description": "Matrix Discord pupetting bridge for YunoHost ", "url": "https://github.com/YunoHost-Apps/mautrix_discord_ynh", "stars": 0, "forks": 3, "updated_at": "2025-04-17T06:32:45Z", "language": "Shell", "archived": false}, {"id": "mautrix_facebook_ynh", "name": "mautrix_facebook", "description": "A template for packaging mautrix bridges for YunoHost", "url": "https://github.com/YunoHost-Apps/mautrix_facebook_ynh", "stars": 2, "forks": 3, "updated_at": "2024-06-23T11:33:17Z", "language": "Shell", "archived": false}, {"id": "mautrix_googlechat_ynh", "name": "mautrix_googlechat", "description": " Matrix Google Chat pupetting bridge for YunoHost ", "url": "https://github.com/YunoHost-Apps/mautrix_googlechat_ynh", "stars": 1, "forks": 1, "updated_at": "2025-01-11T05:49:16Z", "language": "Shell", "archived": false}, {"id": "mautrix_instagram_ynh", "name": "mautrix_instagram", "description": "", "url": "https://github.com/YunoHost-Apps/mautrix_instagram_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:58:56Z", "language": null, "archived": false}, {"id": "mautrix_instagram2_ynh", "name": "mautrix_instagram2", "description": "", "url": "https://github.com/YunoHost-Apps/mautrix_instagram2_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:03:04Z", "language": "Shell", "archived": false}, {"id": "mautrix_signal_ynh", "name": "mautrix_signal", "description": "Matrix signal package for YunoHost", "url": "https://github.com/YunoHost-Apps/mautrix_signal_ynh", "stars": 5, "forks": 11, "updated_at": "2025-05-18T07:14:21Z", "language": "Shell", "archived": false}, {"id": "mautrix_telegram_ynh", "name": "mautrix_telegram", "description": "Matrix Telegram pupetting bridge for YunoHost", "url": "https://github.com/YunoHost-Apps/mautrix_telegram_ynh", "stars": 16, "forks": 5, "updated_at": "2025-04-09T10:18:00Z", "language": "Shell", "archived": false}, {"id": "mautrix_whatsapp_ynh", "name": "mautrix_whatsapp", "description": "Matrix Whatsapp pupetting bridge for YunoHost", "url": "https://github.com/YunoHost-Apps/mautrix_whatsapp_ynh", "stars": 27, "forks": 14, "updated_at": "2025-06-05T12:21:56Z", "language": "Shell", "archived": false}, {"id": "mediacms_ynh", "name": "mediacms", "description": "", "url": "https://github.com/YunoHost-Apps/mediacms_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-03T14:36:00Z", "language": "Shell", "archived": false}, {"id": "mediadrop_ynh", "name": "mediadrop", "description": "Mediadrop - The Web’s Open Source Video Platform", "url": "https://github.com/YunoHost-Apps/mediadrop_ynh", "stars": 4, "forks": 6, "updated_at": "2024-06-19T09:14:39Z", "language": "Shell", "archived": true}, {"id": "mediagoblin_ynh", "name": "mediagoblin", "description": "GNU MediaGoblin for YunoHost", "url": "https://github.com/YunoHost-Apps/mediagoblin_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:54:03Z", "language": "Shell", "archived": true}, {"id": "mediatracker_ynh", "name": "mediatracker", "description": "MediaTracker package for YunoHost", "url": "https://github.com/YunoHost-Apps/mediatracker_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-18T20:34:46Z", "language": "Shell", "archived": false}, {"id": "mediawiki_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "MediaWiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/mediawiki_ynh", "stars": 8, "forks": 17, "updated_at": "2025-04-12T13:22:49Z", "language": "Shell", "archived": false}, {"id": "medusa_ynh", "name": "medusa", "description": "Medusa package for YunoHost", "url": "https://github.com/YunoHost-Apps/medusa_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:55:05Z", "language": "Shell", "archived": true}, {"id": "meet_ynh", "name": "meet", "description": "Simple and Secure Video Conferencing. Powered by LiveKit.", "url": "https://github.com/YunoHost-Apps/meet_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-06T12:19:17Z", "language": "Shell", "archived": false}, {"id": "meilisearch_ynh", "name": "meilisearch", "description": "Lightning Fast, Ultra Relevant, and Typo-Tolerant Search Engine", "url": "https://github.com/YunoHost-Apps/meilisearch_ynh", "stars": 12, "forks": 3, "updated_at": "2025-06-10T07:47:54Z", "language": "Shell", "archived": false}, {"id": "memos_ynh", "name": "memos", "description": "Memos package for YunoHost", "url": "https://github.com/YunoHost-Apps/memos_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-03T07:50:54Z", "language": "Shell", "archived": false}, {"id": "menu_ynh", "name": "menu", "description": "<PERSON><PERSON> for Yunohost", "url": "https://github.com/YunoHost-Apps/menu_ynh", "stars": 3, "forks": 1, "updated_at": "2024-05-15T17:50:19Z", "language": "JavaScript", "archived": true}, {"id": "metabase_ynh", "name": "metabase", "description": "Metabase package for YunoHost", "url": "https://github.com/YunoHost-Apps/metabase_ynh", "stars": 11, "forks": 5, "updated_at": "2025-05-06T23:55:30Z", "language": "Shell", "archived": false}, {"id": "metronome_ynh", "name": "metronome", "description": "Metronome package for YunoHost", "url": "https://github.com/YunoHost-Apps/metronome_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-26T23:19:47Z", "language": "<PERSON><PERSON>", "archived": false}, {"id": "microbin_ynh", "name": "microbin", "description": "Microbin package for YunoHost", "url": "https://github.com/YunoHost-Apps/microbin_ynh", "stars": 0, "forks": 2, "updated_at": "2025-02-01T06:47:33Z", "language": "Shell", "archived": false}, {"id": "microblogpub_ynh", "name": "microblogpub", "description": "YunoHost integration of a self-hosted, single-user, ActivityPub powered microblog", "url": "https://github.com/YunoHost-Apps/microblogpub_ynh", "stars": 0, "forks": 1, "updated_at": "2025-01-19T19:08:23Z", "language": "Shell", "archived": false}, {"id": "minchat_ynh", "name": "minchat", "description": "Minimalist chat for YunoHost", "url": "https://github.com/YunoHost-Apps/minchat_ynh", "stars": 6, "forks": 2, "updated_at": "2024-06-23T11:33:23Z", "language": "Shell", "archived": false}, {"id": "mindmaps_ynh", "name": "mindmaps", "description": "Mindmaps package for YunoHost", "url": "https://github.com/YunoHost-Apps/mindmaps_ynh", "stars": 3, "forks": 2, "updated_at": "2024-06-23T11:33:25Z", "language": "Shell", "archived": false}, {"id": "minecraft_ynh", "name": "minecraft", "description": "Minecraft Server package for YunoHost", "url": "https://github.com/YunoHost-Apps/minecraft_ynh", "stars": 12, "forks": 15, "updated_at": "2025-03-01T20:28:04Z", "language": "Shell", "archived": false}, {"id": "minetest_ynh", "name": "minetest", "description": "Minetest package for YunoHost", "url": "https://github.com/YunoHost-Apps/minetest_ynh", "stars": 6, "forks": 8, "updated_at": "2025-06-01T17:38:42Z", "language": "Shell", "archived": false}, {"id": "mineweb_ynh", "name": "mineweb", "description": "MineWeb package for YunoHost", "url": "https://github.com/YunoHost-Apps/mineweb_ynh", "stars": 2, "forks": 4, "updated_at": "2024-06-23T11:33:27Z", "language": "Shell", "archived": false}, {"id": "minidlna_ynh", "name": "minidlna", "description": "MiniDLNA (ReadyMedia) package for YunoHost", "url": "https://github.com/YunoHost-Apps/minidlna_ynh", "stars": 2, "forks": 4, "updated_at": "2024-06-23T11:33:29Z", "language": "Shell", "archived": false}, {"id": "miniflux_ynh", "name": "miniflux", "description": "Miniflux package for YunoHost", "url": "https://github.com/YunoHost-Apps/miniflux_ynh", "stars": 8, "forks": 1, "updated_at": "2025-05-28T06:59:38Z", "language": "Shell", "archived": false}, {"id": "minio_ynh", "name": "minio", "description": "High Performance, Kubernetes Native Object Storage, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/minio_ynh", "stars": 8, "forks": 6, "updated_at": "2025-02-28T22:19:38Z", "language": "Shell", "archived": false}, {"id": "mirotalk_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "MiroTalk package for YunoHost", "url": "https://github.com/YunoHost-Apps/mirotalk_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-09T08:46:53Z", "language": "Shell", "archived": false}, {"id": "misskey_ynh", "name": "misskey", "description": "Misskey package for YunoHost", "url": "https://github.com/YunoHost-Apps/misskey_ynh", "stars": 31, "forks": 15, "updated_at": "2025-06-03T07:53:22Z", "language": "Shell", "archived": false}, {"id": "mitra_ynh", "name": "mitra", "description": "Mitra package for YunoHost", "url": "https://github.com/YunoHost-Apps/mitra_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-06T12:00:03Z", "language": "Shell", "archived": false}, {"id": "mlmmj_ynh", "name": "mlmmj", "description": "mlmmj (mailing list manager) packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/mlmmj_ynh", "stars": 0, "forks": 1, "updated_at": "2024-09-30T08:35:54Z", "language": "Shell", "archived": false}, {"id": "mlmmj-web_ynh", "name": "mlmmj-web", "description": "Mlmmj Web interface package for YunoHost", "url": "https://github.com/YunoHost-Apps/mlmmj-web_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-01T13:40:07Z", "language": "Shell", "archived": false}, {"id": "mobilizon_ynh", "name": "mobilizon", "description": "A federated organization and mobilization platform for YunoHost", "url": "https://github.com/YunoHost-Apps/mobilizon_ynh", "stars": 21, "forks": 10, "updated_at": "2025-03-07T23:43:02Z", "language": "Shell", "archived": false}, {"id": "modernpaste_ynh", "name": "modernpaste", "description": "A modern, feature-rich Pastebin alternative package for YunoHost", "url": "https://github.com/YunoHost-Apps/modernpaste_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:59Z", "language": "JavaScript", "archived": true}, {"id": "mollysocket_ynh", "name": "molly<PERSON><PERSON>", "description": "Mollysocket package for YunoHost", "url": "https://github.com/YunoHost-Apps/mollysocket_ynh", "stars": 1, "forks": 1, "updated_at": "2025-01-20T20:11:26Z", "language": "Shell", "archived": false}, {"id": "moncycle_ynh", "name": "moncycle", "description": "Suivi de cycle pour les méthodes naturelles de régulation de naissance.", "url": "https://github.com/YunoHost-Apps/moncycle_ynh", "stars": 3, "forks": 3, "updated_at": "2025-01-11T03:48:15Z", "language": "Shell", "archived": false}, {"id": "mongo-express_ynh", "name": "mongo-express", "description": "YunoHost scripts to manage Mongo-express", "url": "https://github.com/YunoHost-Apps/mongo-express_ynh", "stars": 0, "forks": 3, "updated_at": "2025-05-15T08:24:26Z", "language": "Shell", "archived": false}, {"id": "monica_ynh", "name": "monica", "description": "Monica package for YunoHost", "url": "https://github.com/YunoHost-Apps/monica_ynh", "stars": 18, "forks": 13, "updated_at": "2025-03-24T16:29:19Z", "language": "Shell", "archived": false}, {"id": "monit_ynh", "name": "monit", "description": "Monit for YunoHost", "url": "https://github.com/YunoHost-Apps/monit_ynh", "stars": 4, "forks": 0, "updated_at": "2024-12-04T08:41:09Z", "language": "Shell", "archived": true}, {"id": "monitorix_ynh", "name": "monitorix", "description": "Monitorix package for YunoHost", "url": "https://github.com/YunoHost-Apps/monitorix_ynh", "stars": 3, "forks": 9, "updated_at": "2025-04-26T19:40:36Z", "language": "Shell", "archived": false}, {"id": "moodle_ynh", "name": "moodle", "description": "Moodle package for YunoHost", "url": "https://github.com/YunoHost-Apps/moodle_ynh", "stars": 4, "forks": 7, "updated_at": "2025-05-22T18:38:29Z", "language": "Shell", "archived": false}, {"id": "mopidy_ynh", "name": "mopidy", "description": "Mopidy package for YunoHost", "url": "https://github.com/YunoHost-Apps/mopidy_ynh", "stars": 4, "forks": 8, "updated_at": "2024-05-28T21:35:31Z", "language": "Shell", "archived": false}, {"id": "mosquitto_ynh", "name": "m<PERSON><PERSON><PERSON>", "description": "Mosquitto package for YunoHost ", "url": "https://github.com/YunoHost-Apps/mosquitto_ynh", "stars": 4, "forks": 3, "updated_at": "2025-03-23T15:14:24Z", "language": "Shell", "archived": false}, {"id": "mostlymatter_ynh", "name": "mostlymatter", "description": "Mostlymatter package for YunoHost", "url": "https://github.com/YunoHost-Apps/mostlymatter_ynh", "stars": 1, "forks": 0, "updated_at": "2025-03-01T10:22:58Z", "language": "Shell", "archived": false}, {"id": "motioneye_ynh", "name": "motioneye", "description": "MotionEye package for YunoHost", "url": "https://github.com/YunoHost-Apps/motioneye_ynh", "stars": 0, "forks": 3, "updated_at": "2024-11-22T17:39:08Z", "language": "Shell", "archived": false}, {"id": "movim_ynh", "name": "movim", "description": "Movim package for YunoHost", "url": "https://github.com/YunoHost-Apps/movim_ynh", "stars": 7, "forks": 13, "updated_at": "2025-04-24T07:38:23Z", "language": "Shell", "archived": false}, {"id": "mstream_ynh", "name": "mstream", "description": "mStream package for YunoHost", "url": "https://github.com/YunoHost-Apps/mstream_ynh", "stars": 1, "forks": 1, "updated_at": "2024-12-22T08:59:18Z", "language": "Shell", "archived": false}, {"id": "multi_webapp_ynh", "name": "multi_webapp", "description": "Custom Webapp without FTP access", "url": "https://github.com/YunoHost-Apps/multi_webapp_ynh", "stars": 5, "forks": 8, "updated_at": "2024-05-15T17:49:48Z", "language": "Shell", "archived": true}, {"id": "mumble-web_ynh", "name": "mumble-web", "description": "Mumble web client package for YunoHost", "url": "https://github.com/YunoHost-Apps/mumble-web_ynh", "stars": 7, "forks": 1, "updated_at": "2025-02-13T02:03:27Z", "language": "Shell", "archived": false}, {"id": "mumbleserver_ynh", "name": "mumbleserver", "description": "Mumble server package for YunoHost", "url": "https://github.com/YunoHost-Apps/mumbleserver_ynh", "stars": 11, "forks": 4, "updated_at": "2025-05-06T06:53:05Z", "language": "Shell", "archived": false}, {"id": "munin_ynh", "name": "munin", "description": "<PERSON><PERSON> for YunoHost", "url": "https://github.com/YunoHost-Apps/munin_ynh", "stars": 2, "forks": 1, "updated_at": "2024-05-15T17:50:18Z", "language": "Shell", "archived": true}, {"id": "my_capsule_ynh", "name": "my_capsule", "description": "Custom Gemini package for YunoHost", "url": "https://github.com/YunoHost-Apps/my_capsule_ynh", "stars": 7, "forks": 4, "updated_at": "2024-12-20T17:51:52Z", "language": "Shell", "archived": false}, {"id": "my_flask_webapp_ynh", "name": "my_flask_webapp", "description": "", "url": "https://github.com/YunoHost-Apps/my_flask_webapp_ynh", "stars": 2, "forks": 1, "updated_at": "2024-06-19T09:19:52Z", "language": "Shell", "archived": true}, {"id": "my_idlers_ynh", "name": "my_idlers", "description": "My Idlers package for YunoHost", "url": "https://github.com/YunoHost-Apps/my_idlers_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-05T08:33:31Z", "language": "Shell", "archived": false}, {"id": "my_webapp_ynh", "name": "my_webapp", "description": "Custom Web app with SFTP access", "url": "https://github.com/YunoHost-Apps/my_webapp_ynh", "stars": 49, "forks": 51, "updated_at": "2025-05-02T07:11:50Z", "language": "Shell", "archived": false}, {"id": "my_webdav_ynh", "name": "my_webdav", "description": "Webdav configuration for YunoHost", "url": "https://github.com/YunoHost-Apps/my_webdav_ynh", "stars": 2, "forks": 2, "updated_at": "2025-02-22T18:33:30Z", "language": "Shell", "archived": false}, {"id": "my-mind_ynh", "name": "my-mind", "description": "My Mind package for YunoHost", "url": "https://github.com/YunoHost-Apps/my-mind_ynh", "stars": 3, "forks": 1, "updated_at": "2025-05-21T17:34:46Z", "language": "Shell", "archived": false}, {"id": "mybb_ynh", "name": "mybb", "description": "MyBB package for YunoHost", "url": "https://github.com/YunoHost-Apps/mybb_ynh", "stars": 2, "forks": 1, "updated_at": "2024-06-22T22:27:18Z", "language": "Shell", "archived": false}, {"id": "mycryptochat_ynh", "name": "mycryptochat", "description": "MyCryptoChat for Yunohost", "url": "https://github.com/YunoHost-Apps/mycryptochat_ynh", "stars": 5, "forks": 4, "updated_at": "2024-08-29T00:42:50Z", "language": "JavaScript", "archived": true}, {"id": "mydrive_ynh", "name": "mydrive", "description": "MyDrive package for YunoHost", "url": "https://github.com/YunoHost-Apps/mydrive_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-19T22:14:31Z", "language": "Shell", "archived": false}, {"id": "mygpo_ynh", "name": "mygpo", "description": " mygpo packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/mygpo_ynh", "stars": 6, "forks": 4, "updated_at": "2025-05-02T20:16:00Z", "language": "Shell", "archived": false}, {"id": "myspeed_ynh", "name": "my<PERSON><PERSON>", "description": "MySpeed package for YunoHost", "url": "https://github.com/YunoHost-Apps/myspeed_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-01T22:27:17Z", "language": "Shell", "archived": false}, {"id": "mytinytodo_ynh", "name": "my<PERSON><PERSON><PERSON>o", "description": "myTinyTodo package for YunoHost", "url": "https://github.com/YunoHost-Apps/mytinytodo_ynh", "stars": 1, "forks": 3, "updated_at": "2025-05-29T17:17:03Z", "language": "Shell", "archived": false}, {"id": "n8n_ynh", "name": "n8n", "description": "n8n package for YunoHost", "url": "https://github.com/YunoHost-Apps/n8n_ynh", "stars": 15, "forks": 21, "updated_at": "2025-06-06T06:28:11Z", "language": "Shell", "archived": false}, {"id": "navidrome_ynh", "name": "navidrome", "description": "Navidrome package for YunoHost", "url": "https://github.com/YunoHost-Apps/navidrome_ynh", "stars": 17, "forks": 6, "updated_at": "2025-05-30T06:56:38Z", "language": "Shell", "archived": false}, {"id": "netdata_ynh", "name": "netdata", "description": "NetData package for YunoHost", "url": "https://github.com/YunoHost-Apps/netdata_ynh", "stars": 9, "forks": 5, "updated_at": "2025-06-07T08:58:22Z", "language": "Shell", "archived": false}, {"id": "nextcloud_ynh", "name": "nextcloud", "description": "Nextcloud package for YunoHost", "url": "https://github.com/YunoHost-Apps/nextcloud_ynh", "stars": 161, "forks": 68, "updated_at": "2025-06-05T05:10:31Z", "language": "Shell", "archived": false}, {"id": "nextcloud-signaling_ynh", "name": "nextcloud-signaling", "description": "Standalone signaling server for Nextcloud Talk.", "url": "https://github.com/YunoHost-Apps/nextcloud-signaling_ynh", "stars": 0, "forks": 1, "updated_at": "2024-10-16T20:01:56Z", "language": "Shell", "archived": false}, {"id": "nexusoss_ynh", "name": "nexusoss", "description": "sonatype nexus repository manager OSS for yunohost", "url": "https://github.com/YunoHost-Apps/nexusoss_ynh", "stars": 1, "forks": 2, "updated_at": "2024-05-15T17:53:29Z", "language": "Shell", "archived": true}, {"id": "nitter_ynh", "name": "nitter", "description": "Nitter package for YunoHost", "url": "https://github.com/YunoHost-Apps/nitter_ynh", "stars": 7, "forks": 5, "updated_at": "2025-04-24T07:50:14Z", "language": "Shell", "archived": false}, {"id": "no_stretch_ynh", "name": "no_stretch", "description": "This app prevent to upgrade to stretch by mistake", "url": "https://github.com/YunoHost-Apps/no_stretch_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:17Z", "language": "Shell", "archived": true}, {"id": "noalyss_ynh", "name": "noalyss", "description": "Noalyss package for YunoHost", "url": "https://github.com/YunoHost-Apps/noalyss_ynh", "stars": 3, "forks": 6, "updated_at": "2025-05-18T20:09:38Z", "language": "Shell", "archived": false}, {"id": "nocodb_ynh", "name": "nocodb", "description": "Open Source Airtable alternative, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/nocodb_ynh", "stars": 11, "forks": 5, "updated_at": "2025-05-29T11:39:58Z", "language": "Shell", "archived": false}, {"id": "node_exporter_ynh", "name": "node_exporter", "description": "Node Exporter package for YunoHost", "url": "https://github.com/YunoHost-Apps/node_exporter_ynh", "stars": 2, "forks": 0, "updated_at": "2025-05-30T21:09:36Z", "language": "Shell", "archived": false}, {"id": "nodebb_ynh", "name": "nodebb", "description": "NodeBB package for YunoHost", "url": "https://github.com/YunoHost-Apps/nodebb_ynh", "stars": 7, "forks": 6, "updated_at": "2025-06-10T07:44:12Z", "language": "Shell", "archived": false}, {"id": "nodered_ynh", "name": "nodered", "description": "Flow-based programming for the Internet of Things, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/nodered_ynh", "stars": 5, "forks": 6, "updated_at": "2025-05-29T10:03:52Z", "language": "JavaScript", "archived": false}, {"id": "nomad_ynh", "name": "nomad", "description": "Nomad package for YunoHost", "url": "https://github.com/YunoHost-Apps/nomad_ynh", "stars": 2, "forks": 1, "updated_at": "2024-07-20T20:37:25Z", "language": "Shell", "archived": false}, {"id": "nonbiri_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Nonbiri, is a self-hosted back-end and front-end for MangaDex packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/nonbiri_ynh", "stars": 1, "forks": 0, "updated_at": "2025-02-19T00:33:45Z", "language": "Shell", "archived": true}, {"id": "not_so_yuno_ynh", "name": "not_so_yuno", "description": "", "url": "https://github.com/YunoHost-Apps/not_so_yuno_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:55:12Z", "language": null, "archived": true}, {"id": "ntfy_ynh", "name": "ntfy", "description": "Send push notifications to your phone or desktop using PUT/POST", "url": "https://github.com/YunoHost-Apps/ntfy_ynh", "stars": 12, "forks": 5, "updated_at": "2025-06-07T08:37:09Z", "language": "Shell", "archived": false}, {"id": "ntopng_ynh", "name": "ntopng", "description": "ntopng for YunoHost ", "url": "https://github.com/YunoHost-Apps/ntopng_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:56:08Z", "language": "Shell", "archived": true}, {"id": "nullboard_ynh", "name": "nullboard", "description": "Nullboard package for YunoHost", "url": "https://github.com/YunoHost-Apps/nullboard_ynh", "stars": 3, "forks": 1, "updated_at": "2024-09-08T19:13:59Z", "language": "Shell", "archived": false}, {"id": "octoprint_ynh", "name": "octoprint", "description": "Octoprint package for Yunohost", "url": "https://github.com/YunoHost-Apps/octoprint_ynh", "stars": 3, "forks": 2, "updated_at": "2025-04-12T16:02:25Z", "language": "Shell", "archived": false}, {"id": "odoo_ynh", "name": "odoo", "description": "All-in-one entreprise management system, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/odoo_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:00:54Z", "language": "Shell", "archived": true}, {"id": "ofbiz_ynh", "name": "ofbiz", "description": "OfBiz package for YunoHost", "url": "https://github.com/YunoHost-Apps/ofbiz_ynh", "stars": 0, "forks": 4, "updated_at": "2025-03-28T01:21:26Z", "language": "Shell", "archived": false}, {"id": "ojs_ynh", "name": "ojs", "description": "Open Journal systems package for YunoHost", "url": "https://github.com/YunoHost-Apps/ojs_ynh", "stars": 1, "forks": 1, "updated_at": "2025-05-18T20:30:40Z", "language": "PHP", "archived": false}, {"id": "olivetin_ynh", "name": "olivetin", "description": "OliveTin package for YunoHost", "url": "https://github.com/YunoHost-Apps/olivetin_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-07T06:06:40Z", "language": "Shell", "archived": false}, {"id": "ollama_ynh", "name": "ollama", "description": "", "url": "https://github.com/YunoHost-Apps/ollama_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-11T17:43:05Z", "language": "Shell", "archived": false}, {"id": "ombi_ynh", "name": "ombi", "description": "Ombi package for YunoHost", "url": "https://github.com/YunoHost-Apps/ombi_ynh", "stars": 0, "forks": 2, "updated_at": "2025-02-28T18:04:56Z", "language": "Shell", "archived": false}, {"id": "omeka-s_ynh", "name": "omeka-s", "description": "Omeka S package for YunoHost", "url": "https://github.com/YunoHost-Apps/omeka-s_ynh", "stars": 5, "forks": 2, "updated_at": "2024-08-31T13:03:24Z", "language": "Shell", "archived": false}, {"id": "onlyoffice_ynh", "name": "onlyoffice", "description": "OnlyOffice package for YunoHost", "url": "https://github.com/YunoHost-Apps/onlyoffice_ynh", "stars": 19, "forks": 19, "updated_at": "2025-05-27T10:06:02Z", "language": "Shell", "archived": false}, {"id": "opcache-gui_ynh", "name": "opcache-gui", "description": "OPcache-GUI package for YunoHost", "url": "https://github.com/YunoHost-Apps/opcache-gui_ynh", "stars": 0, "forks": 0, "updated_at": "2025-02-06T09:14:50Z", "language": "Shell", "archived": false}, {"id": "open-web-calendar_ynh", "name": "open-web-calendar", "description": "Open Web Calendar package for YunoHost", "url": "https://github.com/YunoHost-Apps/open-web-calendar_ynh", "stars": 0, "forks": 2, "updated_at": "2025-04-03T20:48:32Z", "language": "Shell", "archived": false}, {"id": "opencast_ynh", "name": "opencast", "description": "OpenCast package for YunoHost", "url": "https://github.com/YunoHost-Apps/opencast_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-31T21:28:58Z", "language": "Shell", "archived": false}, {"id": "opencloud_ynh", "name": "opencloud", "description": "OpenCloud package for YunoHost", "url": "https://github.com/YunoHost-Apps/opencloud_ynh", "stars": 2, "forks": 1, "updated_at": "2025-06-10T16:28:29Z", "language": "Shell", "archived": false}, {"id": "opennote_ynh", "name": "opennote", "description": "OpenNote package for YunoHost", "url": "https://github.com/YunoHost-Apps/opennote_ynh", "stars": 2, "forks": 1, "updated_at": "2024-05-15T17:51:45Z", "language": "Shell", "archived": true}, {"id": "openproject_ynh", "name": "openproject", "description": "OpenProject instanciation for YunoHost", "url": "https://github.com/YunoHost-Apps/openproject_ynh", "stars": 6, "forks": 3, "updated_at": "2025-06-04T16:45:53Z", "language": "Shell", "archived": false}, {"id": "opensearch_ynh", "name": "opensearch", "description": ":mag_right: Open source distributed and RESTful search engine for YunoHost.", "url": "https://github.com/YunoHost-Apps/opensearch_ynh", "stars": 2, "forks": 1, "updated_at": "2025-05-16T15:34:54Z", "language": "Shell", "archived": false}, {"id": "opensondage_ynh", "name": "opensondage", "description": "OpenSondage for YunoHost", "url": "https://github.com/YunoHost-Apps/opensondage_ynh", "stars": 16, "forks": 14, "updated_at": "2025-05-02T20:20:53Z", "language": "Shell", "archived": false}, {"id": "opentracker_ynh", "name": "opentracker", "description": "OpenTracker package for YunoHost", "url": "https://github.com/YunoHost-Apps/opentracker_ynh", "stars": 3, "forks": 0, "updated_at": "2025-01-24T16:34:04Z", "language": "Shell", "archived": false}, {"id": "orangehrm_ynh", "name": "orangehrm", "description": "OrangeHRM package for YunoHost", "url": "https://github.com/YunoHost-Apps/orangehrm_ynh", "stars": 0, "forks": 0, "updated_at": "2025-02-23T13:16:35Z", "language": "Shell", "archived": false}, {"id": "ore_ynh", "name": "ore", "description": "ORE is a script for building a website containing information about buying, renting, selling ", "url": "https://github.com/YunoHost-Apps/ore_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:34Z", "language": "Shell", "archived": true}, {"id": "osada_ynh", "name": "o<PERSON>a", "description": "Osada/Zap package for YunoHost", "url": "https://github.com/YunoHost-Apps/osada_ynh", "stars": 10, "forks": 2, "updated_at": "2024-06-23T12:12:35Z", "language": "Shell", "archived": false}, {"id": "osjs_ynh", "name": "osjs", "description": "Os-js package for YunoHost", "url": "https://github.com/YunoHost-Apps/osjs_ynh", "stars": 8, "forks": 6, "updated_at": "2024-06-23T12:12:37Z", "language": "Shell", "archived": false}, {"id": "osmw_ynh", "name": "osmw", "description": "", "url": "https://github.com/YunoHost-Apps/osmw_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:52:45Z", "language": "Shell", "archived": true}, {"id": "osp_ynh", "name": "osp", "description": "Open Streaming Platform package for YunoHost", "url": "https://github.com/YunoHost-Apps/osp_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T17:57:28Z", "language": "Shell", "archived": false}, {"id": "osticket_ynh", "name": "osticket", "description": "Support Ticketing System package for YunoHost", "url": "https://github.com/YunoHost-Apps/osticket_ynh", "stars": 2, "forks": 7, "updated_at": "2025-06-07T08:49:19Z", "language": "Shell", "archived": false}, {"id": "outline_ynh", "name": "outline", "description": "An open, extensible, wiki for your team built using React and Node.js", "url": "https://github.com/YunoHost-Apps/outline_ynh", "stars": 18, "forks": 11, "updated_at": "2025-05-27T21:29:56Z", "language": "Shell", "archived": false}, {"id": "overleaf_ynh", "name": "overleaf", "description": "Overleaf package for YunoHost", "url": "https://github.com/YunoHost-Apps/overleaf_ynh", "stars": 2, "forks": 5, "updated_at": "2025-06-09T05:35:37Z", "language": "Shell", "archived": false}, {"id": "owncast_ynh", "name": "owncast", "description": "Owncast package for YunoHost", "url": "https://github.com/YunoHost-Apps/owncast_ynh", "stars": 13, "forks": 2, "updated_at": "2025-05-11T21:10:40Z", "language": "Shell", "archived": false}, {"id": "owncast-emojiwall_ynh", "name": "owncast-emojiwall", "description": "Owncast Emojiwall package for YunoHost", "url": "https://github.com/YunoHost-Apps/owncast-emojiwall_ynh", "stars": 2, "forks": 2, "updated_at": "2024-09-08T10:52:51Z", "language": "Shell", "archived": false}, {"id": "owncloud_ynh", "name": "owncloud", "description": "ownCloud package for YunoHost", "url": "https://github.com/YunoHost-Apps/owncloud_ynh", "stars": 4, "forks": 8, "updated_at": "2025-06-03T07:16:43Z", "language": "Shell", "archived": false}, {"id": "owntracks_ynh", "name": "owntracks", "description": "Owntracks package for YunoHost", "url": "https://github.com/YunoHost-Apps/owntracks_ynh", "stars": 13, "forks": 4, "updated_at": "2025-01-17T17:17:26Z", "language": "Shell", "archived": false}, {"id": "pagure_ynh", "name": "pagure", "description": "Pagure, a git centered forge for YunoHost", "url": "https://github.com/YunoHost-Apps/pagure_ynh", "stars": 1, "forks": 1, "updated_at": "2024-06-23T12:12:44Z", "language": "Shell", "archived": false}, {"id": "paheko_ynh", "name": "paheko", "description": "Paheko package for YunoHost", "url": "https://github.com/YunoHost-Apps/paheko_ynh", "stars": 3, "forks": 6, "updated_at": "2025-05-19T18:11:39Z", "language": "PHP", "archived": false}, {"id": "pairdrop_ynh", "name": "pairdrop", "description": "PairDrop package for YunoHost", "url": "https://github.com/YunoHost-Apps/pairdrop_ynh", "stars": 1, "forks": 1, "updated_at": "2025-02-01T19:00:43Z", "language": "Shell", "archived": false}, {"id": "papercut_ynh", "name": "papercut", "description": "", "url": "https://github.com/YunoHost-Apps/papercut_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:55:44Z", "language": "Shell", "archived": true}, {"id": "paperless-ngx_ynh", "name": "paperless-ngx", "description": "Scan, index and archive all your physical documents", "url": "https://github.com/YunoHost-Apps/paperless-ngx_ynh", "stars": 13, "forks": 13, "updated_at": "2025-05-25T07:52:10Z", "language": "Shell", "archived": false}, {"id": "paperless-ngx_docker_ynh", "name": "paperless-ngx_docker", "description": "Dockerized Paperless-ngx (experimental)", "url": "https://github.com/YunoHost-Apps/paperless-ngx_docker_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T18:06:40Z", "language": "Shell", "archived": false}, {"id": "passed_ynh", "name": "passed", "description": "Share a password with someone securely.", "url": "https://github.com/YunoHost-Apps/passed_ynh", "stars": 1, "forks": 0, "updated_at": "2025-05-26T01:55:39Z", "language": "Shell", "archived": false}, {"id": "peachpub_ynh", "name": "peachpub", "description": "Secure Scuttlebutt pub with a web interface for pub management, for YunoHost. ", "url": "https://github.com/YunoHost-Apps/peachpub_ynh", "stars": 5, "forks": 0, "updated_at": "2025-05-12T15:45:12Z", "language": "Shell", "archived": false}, {"id": "peer-calls_ynh", "name": "peer-calls", "description": "Peer-Calls package for YunoHost", "url": "https://github.com/YunoHost-Apps/peer-calls_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-20T20:05:39Z", "language": "Shell", "archived": false}, {"id": "peertube_ynh", "name": "peertube", "description": "Federated (ActivityPub) video streaming platform using P2P for YunoHost", "url": "https://github.com/YunoHost-Apps/peertube_ynh", "stars": 90, "forks": 28, "updated_at": "2025-06-05T07:25:30Z", "language": "Shell", "archived": false}, {"id": "peertube_remote_runner_ynh", "name": "peertube_remote_runner", "description": "Peertube remote runner standalone package for YunoHost", "url": "https://github.com/YunoHost-Apps/peertube_remote_runner_ynh", "stars": 1, "forks": 1, "updated_at": "2024-06-23T16:14:27Z", "language": "Shell", "archived": false}, {"id": "peertube-search-index_ynh", "name": "peertube-search-index", "description": "Moteur de recherche de vidéos et chaînes PeerTube", "url": "https://github.com/YunoHost-Apps/peertube-search-index_ynh", "stars": 8, "forks": 3, "updated_at": "2025-06-07T08:53:05Z", "language": "Shell", "archived": false}, {"id": "pelican_ynh", "name": "pelican", "description": "Pelican package for Yunohost", "url": "https://github.com/YunoHost-Apps/pelican_ynh", "stars": 4, "forks": 3, "updated_at": "2024-06-23T12:12:54Z", "language": "Shell", "archived": false}, {"id": "penpot_ynh", "name": "penpot", "description": "Penpot packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/penpot_ynh", "stars": 6, "forks": 5, "updated_at": "2025-06-08T21:23:08Z", "language": "Shell", "archived": false}, {"id": "pepettes_ynh", "name": "pepettes", "description": "Pepettes is a donation form", "url": "https://github.com/YunoHost-Apps/pepettes_ynh", "stars": 7, "forks": 3, "updated_at": "2025-05-01T11:05:26Z", "language": "Shell", "archived": false}, {"id": "peppermint_ynh", "name": "peppermint", "description": "Peppermint package for YunoHost", "url": "https://github.com/YunoHost-Apps/peppermint_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:05:07Z", "language": "Shell", "archived": false}, {"id": "petitesannonces_ynh", "name": "petitesann<PERSON><PERSON>", "description": "PetitesAnnonces package for YunoHost", "url": "https://github.com/YunoHost-Apps/petitesannonces_ynh", "stars": 6, "forks": 4, "updated_at": "2024-06-23T12:12:56Z", "language": "Shell", "archived": false}, {"id": "petrolette_ynh", "name": "petrolette", "description": "Petrolette package for YunoHost", "url": "https://github.com/YunoHost-Apps/petrolette_ynh", "stars": 2, "forks": 2, "updated_at": "2024-06-23T17:29:54Z", "language": "Shell", "archived": false}, {"id": "pgadmin_ynh", "name": "pgadmin", "description": "PostgreSQL web admin tool package for YunoHost", "url": "https://github.com/YunoHost-Apps/pgadmin_ynh", "stars": 6, "forks": 2, "updated_at": "2025-06-07T08:50:21Z", "language": "Shell", "archived": false}, {"id": "pgweb_ynh", "name": "pgweb", "description": "Pgweb package for YunoHost", "url": "https://github.com/YunoHost-Apps/pgweb_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-09T16:48:17Z", "language": "Shell", "archived": false}, {"id": "phanpy_ynh", "name": "phanpy", "description": "Phanpy package for YunoHost", "url": "https://github.com/YunoHost-Apps/phanpy_ynh", "stars": 2, "forks": 1, "updated_at": "2025-06-11T02:52:38Z", "language": "Shell", "archived": false}, {"id": "photonix_ynh", "name": "photonix", "description": "Photonix package for YunoHost", "url": "https://github.com/YunoHost-Apps/photonix_ynh", "stars": 3, "forks": 1, "updated_at": "2024-08-17T10:51:13Z", "language": "Shell", "archived": false}, {"id": "photoprism_ynh", "name": "photoprism", "description": "AI-Powered Photos App for the Decentralized Web", "url": "https://github.com/YunoHost-Apps/photoprism_ynh", "stars": 7, "forks": 6, "updated_at": "2025-05-10T05:48:11Z", "language": "Shell", "archived": false}, {"id": "photoview_ynh", "name": "photoview", "description": "Photoview package for YunoHost", "url": "https://github.com/YunoHost-Apps/photoview_ynh", "stars": 7, "forks": 12, "updated_at": "2025-05-02T20:16:17Z", "language": "Shell", "archived": false}, {"id": "phpback_ynh", "name": "phpback", "description": "PHPBack package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpback_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-16T12:39:18Z", "language": "Shell", "archived": false}, {"id": "phpbb_ynh", "name": "phpbb", "description": "phpBB package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpbb_ynh", "stars": 2, "forks": 2, "updated_at": "2025-01-27T18:27:05Z", "language": "Shell", "archived": false}, {"id": "phpboost_ynh", "name": "phpboost", "description": "PHPBoost package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpboost_ynh", "stars": 3, "forks": 1, "updated_at": "2025-05-09T10:05:40Z", "language": "Shell", "archived": false}, {"id": "phpinfo_ynh", "name": "phpinfo", "description": "PHPinfo package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpinfo_ynh", "stars": 0, "forks": 2, "updated_at": "2024-08-27T13:08:01Z", "language": "Shell", "archived": false}, {"id": "phpipam_ynh", "name": "phpipam", "description": "phpIPAM package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpipam_ynh", "stars": 2, "forks": 2, "updated_at": "2024-11-09T17:01:06Z", "language": "PHP", "archived": false}, {"id": "phpldapadmin_ynh", "name": "php<PERSON><PERSON><PERSON>", "description": "phpLDAPadmin package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpldapadmin_ynh", "stars": 3, "forks": 7, "updated_at": "2025-04-02T23:45:36Z", "language": "Shell", "archived": false}, {"id": "phplicensewatcher_ynh", "name": "phplicensewatcher", "description": "phpLicenseWatcher package for YunoHost", "url": "https://github.com/YunoHost-Apps/phplicensewatcher_ynh", "stars": 1, "forks": 1, "updated_at": "2025-06-02T07:38:17Z", "language": "Shell", "archived": false}, {"id": "phpmyadmin_ynh", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "phpMyAdmin package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpmyadmin_ynh", "stars": 2, "forks": 13, "updated_at": "2025-04-29T19:23:42Z", "language": "Shell", "archived": false}, {"id": "phpservermon_ynh", "name": "phpservermon", "description": "PHP Server Monitor package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpservermon_ynh", "stars": 1, "forks": 2, "updated_at": "2024-06-23T12:13:15Z", "language": "Shell", "archived": false}, {"id": "phpsysinfo_ynh", "name": "phpsysinfo", "description": "PHPSysInfo package for YunoHost", "url": "https://github.com/YunoHost-Apps/phpsysinfo_ynh", "stars": 2, "forks": 2, "updated_at": "2024-10-10T09:47:52Z", "language": "Shell", "archived": false}, {"id": "pia_ynh", "name": "pia", "description": "PIA for YunoHost", "url": "https://github.com/YunoHost-Apps/pia_ynh", "stars": 3, "forks": 2, "updated_at": "2024-05-15T17:56:23Z", "language": "Shell", "archived": true}, {"id": "pico_ynh", "name": "pico", "description": "Pico package for YunoHost", "url": "https://github.com/YunoHost-Apps/pico_ynh", "stars": 3, "forks": 1, "updated_at": "2025-05-19T20:16:47Z", "language": "CSS", "archived": false}, {"id": "pihole_ynh", "name": "pihole", "description": "Pi-hole package for YunoHost", "url": "https://github.com/YunoHost-Apps/pihole_ynh", "stars": 19, "forks": 18, "updated_at": "2025-03-14T04:56:43Z", "language": "Shell", "archived": false}, {"id": "pilea_ynh", "name": "pilea", "description": "Pilea package for YunoHost", "url": "https://github.com/YunoHost-Apps/pilea_ynh", "stars": 4, "forks": 3, "updated_at": "2024-05-15T17:55:17Z", "language": "Shell", "archived": true}, {"id": "pinkarrows_ynh", "name": "pinkarrows", "description": "Lightweight images/screenshot annotation tool.", "url": "https://github.com/YunoHost-Apps/pinkarrows_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-13T12:21:14Z", "language": "Shell", "archived": false}, {"id": "piped_ynh", "name": "piped", "description": "piped package for YunoHost", "url": "https://github.com/YunoHost-Apps/piped_ynh", "stars": 2, "forks": 2, "updated_at": "2025-05-26T09:37:01Z", "language": "Shell", "archived": false}, {"id": "piwigo_ynh", "name": "pi<PERSON>o", "description": "Piwigo package for YunoHost", "url": "https://github.com/YunoHost-Apps/piwigo_ynh", "stars": 6, "forks": 5, "updated_at": "2025-03-22T22:08:45Z", "language": "Shell", "archived": false}, {"id": "pixelfed_ynh", "name": "pixelfed", "description": "The federated image shareing service Pixelfed for YunoHost", "url": "https://github.com/YunoHost-Apps/pixelfed_ynh", "stars": 48, "forks": 17, "updated_at": "2025-05-26T21:07:33Z", "language": "Shell", "archived": false}, {"id": "pixelfedglitch_ynh", "name": "pixelfedglitch", "description": "Fork of the federated image shareing service Pixelfed, for YunoHost", "url": "https://github.com/YunoHost-Apps/pixelfedglitch_ynh", "stars": 3, "forks": 0, "updated_at": "2025-05-18T20:34:57Z", "language": "Shell", "archived": false}, {"id": "plainpad_ynh", "name": "plainpad", "description": "Plainpad package for YunoHost", "url": "https://github.com/YunoHost-Apps/plainpad_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-24T20:28:34Z", "language": "Shell", "archived": false}, {"id": "planka_ynh", "name": "planka", "description": "Planka package for YunoHost", "url": "https://github.com/YunoHost-Apps/planka_ynh", "stars": 1, "forks": 4, "updated_at": "2025-05-18T20:30:52Z", "language": "Shell", "archived": false}, {"id": "plateau_ynh", "name": "plateau", "description": "Plateau helps designers organize and document participatory workshops.", "url": "https://github.com/YunoHost-Apps/plateau_ynh", "stars": 1, "forks": 4, "updated_at": "2024-06-23T17:14:42Z", "language": "Shell", "archived": false}, {"id": "pleroma_ynh", "name": "pleroma", "description": "Pleroma package for YunoHost: A free, federated social networking server built on open protocols.", "url": "https://github.com/YunoHost-Apps/pleroma_ynh", "stars": 29, "forks": 16, "updated_at": "2025-03-23T16:16:21Z", "language": "Shell", "archived": false}, {"id": "plexmediaserver_ynh", "name": "plexmediaserver", "description": "PlexMediaServer package for YunoHost", "url": "https://github.com/YunoHost-Apps/plexmediaserver_ynh", "stars": 5, "forks": 11, "updated_at": "2024-11-01T02:40:12Z", "language": "Shell", "archived": true}, {"id": "plonecms_ynh", "name": "plonecms", "description": "CMS written in python", "url": "https://github.com/YunoHost-Apps/plonecms_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:20Z", "language": "Shell", "archived": true}, {"id": "plume_ynh", "name": "plume", "description": "Federated blogging application for YunoHost", "url": "https://github.com/YunoHost-Apps/plume_ynh", "stars": 16, "forks": 2, "updated_at": "2025-04-09T10:13:20Z", "language": "Shell", "archived": false}, {"id": "pluxml_ynh", "name": "pluxml", "description": "PluXml package for YunoHost", "url": "https://github.com/YunoHost-Apps/pluxml_ynh", "stars": 9, "forks": 3, "updated_at": "2025-03-07T18:39:15Z", "language": "Shell", "archived": false}, {"id": "pmwiki_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "PmWiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/pmwiki_ynh", "stars": 3, "forks": 2, "updated_at": "2025-05-18T20:31:01Z", "language": "Shell", "archived": false}, {"id": "pocketmine_ynh", "name": "pocketmine", "description": "", "url": "https://github.com/YunoHost-Apps/pocketmine_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:57:37Z", "language": null, "archived": true}, {"id": "polr_ynh", "name": "polr", "description": "Polr package for YunoHost", "url": "https://github.com/YunoHost-Apps/polr_ynh", "stars": 1, "forks": 0, "updated_at": "2024-06-19T09:18:56Z", "language": "Shell", "archived": true}, {"id": "portainer_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Portainer package for YunoHost", "url": "https://github.com/YunoHost-Apps/portainer_ynh", "stars": 6, "forks": 20, "updated_at": "2025-02-13T10:37:04Z", "language": "Shell", "archived": true}, {"id": "portainerx86_ynh", "name": "portainerx86", "description": "", "url": "https://github.com/YunoHost-Apps/portainerx86_ynh", "stars": 1, "forks": 0, "updated_at": "2024-06-19T09:28:29Z", "language": "Shell", "archived": true}, {"id": "prestashop_ynh", "name": "prestashop", "description": "Prestashop package for YunoHost", "url": "https://github.com/YunoHost-Apps/prestashop_ynh", "stars": 10, "forks": 8, "updated_at": "2025-03-08T13:53:15Z", "language": "Shell", "archived": false}, {"id": "pretalx_ynh", "name": "pretalx", "description": "Pretalx package for YunoHost", "url": "https://github.com/YunoHost-Apps/pretalx_ynh", "stars": 2, "forks": 1, "updated_at": "2025-06-01T19:34:18Z", "language": "Shell", "archived": false}, {"id": "pretix_ynh", "name": "pretix", "description": "Pretix packge for YunoHost", "url": "https://github.com/YunoHost-Apps/pretix_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-12T08:45:28Z", "language": "Shell", "archived": false}, {"id": "prettynoemiecms_ynh", "name": "prettynoemiecms", "description": "PrettyNoemieCMS package for YunoHost", "url": "https://github.com/YunoHost-Apps/prettynoemiecms_ynh", "stars": 6, "forks": 4, "updated_at": "2024-06-23T12:13:36Z", "language": "Shell", "archived": false}, {"id": "privatebin_ynh", "name": "privatebin", "description": "PrivateBin package for YunoHost", "url": "https://github.com/YunoHost-Apps/privatebin_ynh", "stars": 1, "forks": 4, "updated_at": "2025-02-01T19:54:44Z", "language": "PHP", "archived": false}, {"id": "privtracker_ynh", "name": "privtracker", "description": "PrivTracker package for YunoHost", "url": "https://github.com/YunoHost-Apps/privtracker_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-10T12:27:43Z", "language": "Shell", "archived": false}, {"id": "processwire_ynh", "name": "processwire", "description": "ProcessWire package for YunoHost", "url": "https://github.com/YunoHost-Apps/processwire_ynh", "stars": 0, "forks": 0, "updated_at": "2024-09-03T21:24:11Z", "language": "PHP", "archived": false}, {"id": "projectsend_ynh", "name": "projectsend", "description": "", "url": "https://github.com/YunoHost-Apps/projectsend_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-24T14:19:12Z", "language": "Shell", "archived": false}, {"id": "prometheus_ynh", "name": "prometheus", "description": "Monitoring system and time series database, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/prometheus_ynh", "stars": 6, "forks": 1, "updated_at": "2025-06-01T20:54:14Z", "language": "Shell", "archived": false}, {"id": "prose_ynh", "name": "prose", "description": "Prose package for YunoHost", "url": "https://github.com/YunoHost-Apps/prose_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-19T20:15:04Z", "language": "Shell", "archived": false}, {"id": "prosody_ynh", "name": "prosody", "description": "Prosody package for YunoHost", "url": "https://github.com/YunoHost-Apps/prosody_ynh", "stars": 5, "forks": 9, "updated_at": "2025-05-24T17:27:14Z", "language": "Shell", "archived": false}, {"id": "protonmail-bridge_ynh", "name": "protonmail-bridge", "description": "ProtonMail bridge package for YunoHost", "url": "https://github.com/YunoHost-Apps/protonmail-bridge_ynh", "stars": 0, "forks": 0, "updated_at": "2024-09-16T20:20:53Z", "language": "Shell", "archived": false}, {"id": "prowlarr_ynh", "name": "prowlarr", "description": "Indexer manager/proxy that integrates seamlessly with *arr app family, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/prowlarr_ynh", "stars": 4, "forks": 3, "updated_at": "2025-06-05T08:09:35Z", "language": "Shell", "archived": false}, {"id": "proxigram_ynh", "name": "proxigram", "description": "Proxigram package for YunoHost", "url": "https://github.com/YunoHost-Apps/proxigram_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:03Z", "language": "Shell", "archived": false}, {"id": "proxitok_ynh", "name": "proxitok", "description": "ProxiTok package for YunoHost", "url": "https://github.com/YunoHost-Apps/proxitok_ynh", "stars": 4, "forks": 0, "updated_at": "2024-08-20T21:42:33Z", "language": "Shell", "archived": false}, {"id": "psitransfer_ynh", "name": "psitransfer", "description": "Share files you want in a protected environnement.", "url": "https://github.com/YunoHost-Apps/psitransfer_ynh", "stars": 2, "forks": 2, "updated_at": "2025-06-05T08:09:22Z", "language": "Shell", "archived": false}, {"id": "pterodactyl_ynh", "name": "pterodactyl", "description": "Pterodactyl package for YunoHost", "url": "https://github.com/YunoHost-Apps/pterodactyl_ynh", "stars": 5, "forks": 7, "updated_at": "2024-10-21T18:02:08Z", "language": "Shell", "archived": false}, {"id": "pufferpanel_ynh", "name": "pufferpanel", "description": "PufferPanel package for YunoHost", "url": "https://github.com/YunoHost-Apps/pufferpanel_ynh", "stars": 4, "forks": 3, "updated_at": "2025-03-23T21:09:51Z", "language": "Shell", "archived": false}, {"id": "pydio_ynh", "name": "pydio", "description": "Pydio package for YunoHost", "url": "https://github.com/YunoHost-Apps/pydio_ynh", "stars": 8, "forks": 9, "updated_at": "2025-06-05T17:30:09Z", "language": "Shell", "archived": false}, {"id": "pydisaur_ynh", "name": "pyd<PERSON><PERSON>", "description": "Pydisaur package for YunoHost.", "url": "https://github.com/YunoHost-Apps/pydisaur_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:59:49Z", "language": "Shell", "archived": true}, {"id": "pyinventory_ynh", "name": "pyinventory", "description": "YunoHost app package for: https://github.com/jedie/PyInventory", "url": "https://github.com/YunoHost-Apps/pyinventory_ynh", "stars": 6, "forks": 1, "updated_at": "2025-03-23T17:50:40Z", "language": "Python", "archived": false}, {"id": "pyload_ynh", "name": "pyload", "description": "", "url": "https://github.com/YunoHost-Apps/pyload_ynh", "stars": 0, "forks": 3, "updated_at": "2025-03-24T16:28:07Z", "language": "Shell", "archived": false}, {"id": "pytition_ynh", "name": "pytition", "description": "Self-hosted privacy-friendly online petitions", "url": "https://github.com/YunoHost-Apps/pytition_ynh", "stars": 5, "forks": 3, "updated_at": "2025-03-24T22:28:24Z", "language": "Shell", "archived": false}, {"id": "qbittorrent_ynh", "name": "qbittorrent", "description": "qBittorrent package for YunoHost", "url": "https://github.com/YunoHost-Apps/qbittorrent_ynh", "stars": 2, "forks": 1, "updated_at": "2025-05-19T20:33:38Z", "language": "Shell", "archived": false}, {"id": "qfieldcloud_ynh", "name": "qfieldcloud", "description": "QFieldCloud is a web server to synchronize projects and data between QGIS and QField. It allows seamless synchronization of your field data with change tracking, team management and online-offline work capabilities. https://qfield.cloud/", "url": "https://github.com/YunoHost-Apps/qfieldcloud_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-02T07:37:44Z", "language": "Shell", "archived": false}, {"id": "question2answer_ynh", "name": "question2answer", "description": "Question2Answer package for YunoHost", "url": "https://github.com/YunoHost-Apps/question2answer_ynh", "stars": 1, "forks": 3, "updated_at": "2024-06-23T12:35:05Z", "language": "Shell", "archived": false}, {"id": "quizzes_ynh", "name": "quizzes", "description": "Quizzes package for YunoHost", "url": "https://github.com/YunoHost-Apps/quizzes_ynh", "stars": 1, "forks": 1, "updated_at": "2024-09-04T12:07:43Z", "language": "Shell", "archived": false}, {"id": "radarr_ynh", "name": "radarr", "description": "Movie collection manager for Usenet and BitTorrent users, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/radarr_ynh", "stars": 4, "forks": 4, "updated_at": "2025-05-28T20:14:03Z", "language": "Shell", "archived": false}, {"id": "radicale_ynh", "name": "radicale", "description": "Radicale package for YunoHost", "url": "https://github.com/YunoHost-Apps/radicale_ynh", "stars": 2, "forks": 5, "updated_at": "2025-05-18T20:36:18Z", "language": "JavaScript", "archived": false}, {"id": "rainloop_ynh", "name": "rainloop", "description": "Rainloop package for YunoHost", "url": "https://github.com/YunoHost-Apps/rainloop_ynh", "stars": 13, "forks": 11, "updated_at": "2024-07-26T20:16:01Z", "language": "Shell", "archived": true}, {"id": "rallly_ynh", "name": "rallly", "description": "Rallly package for YunoHost", "url": "https://github.com/YunoHost-Apps/rallly_ynh", "stars": 7, "forks": 1, "updated_at": "2025-05-05T01:35:09Z", "language": "Shell", "archived": false}, {"id": "rclone_ynh", "name": "rclone", "description": "RClone GUI package for YunoHost", "url": "https://github.com/YunoHost-Apps/rclone_ynh", "stars": 6, "forks": 2, "updated_at": "2025-05-22T06:58:23Z", "language": "Shell", "archived": false}, {"id": "reactive-resume_ynh", "name": "reactive-resume", "description": "Reactive Resume package for YunoHost", "url": "https://github.com/YunoHost-Apps/reactive-resume_ynh", "stars": 0, "forks": 0, "updated_at": "2025-01-29T13:40:59Z", "language": "Shell", "archived": false}, {"id": "readarr_ynh", "name": "readarr", "description": "Readarr package for YunoHost", "url": "https://github.com/YunoHost-Apps/readarr_ynh", "stars": 0, "forks": 1, "updated_at": "2024-11-15T21:50:40Z", "language": "Shell", "archived": false}, {"id": "readeck_ynh", "name": "readeck", "description": "Readeck package for YunoHost", "url": "https://github.com/YunoHost-Apps/readeck_ynh", "stars": 3, "forks": 4, "updated_at": "2025-05-30T06:59:34Z", "language": "Shell", "archived": false}, {"id": "readflow_ynh", "name": "readflow", "description": "", "url": "https://github.com/YunoHost-Apps/readflow_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-01T19:36:17Z", "language": "Shell", "archived": false}, {"id": "redirect_ynh", "name": "redirect", "description": "Redirection app for YunoHost", "url": "https://github.com/YunoHost-Apps/redirect_ynh", "stars": 48, "forks": 23, "updated_at": "2025-02-15T12:44:41Z", "language": "Shell", "archived": false}, {"id": "redlib_ynh", "name": "redlib", "description": "Redlib package for YunoHost", "url": "https://github.com/YunoHost-Apps/redlib_ynh", "stars": 8, "forks": 5, "updated_at": "2025-05-04T21:25:51Z", "language": "Shell", "archived": false}, {"id": "redmine_ynh", "name": "redmine", "description": "Redmine package for YunoHost", "url": "https://github.com/YunoHost-Apps/redmine_ynh", "stars": 4, "forks": 6, "updated_at": "2025-05-02T20:15:01Z", "language": "Shell", "archived": false}, {"id": "reel2bits_ynh", "name": "reel2bits", "description": "Soundcloud-like but simple, easy and KISS (and ActivityPub) for YunoHost", "url": "https://github.com/YunoHost-Apps/reel2bits_ynh", "stars": 13, "forks": 1, "updated_at": "2024-06-19T09:14:28Z", "language": "Shell", "archived": true}, {"id": "rei3_ynh", "name": "rei3", "description": "REI3 package for YunoHost", "url": "https://github.com/YunoHost-Apps/rei3_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-08T20:05:12Z", "language": "Shell", "archived": false}, {"id": "reiverr_ynh", "name": "reiverr", "description": "Reiverr package for YunoHost", "url": "https://github.com/YunoHost-Apps/reiverr_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-05T06:33:24Z", "language": "Shell", "archived": false}, {"id": "remotestorage_ynh", "name": "remotestorage", "description": "php-remote-storage package for YunoHost", "url": "https://github.com/YunoHost-Apps/remotestorage_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:54:17Z", "language": "Shell", "archived": true}, {"id": "resetssh_ynh", "name": "resetssh", "description": "A special package to reset your SSH config via webadmin if you broke it", "url": "https://github.com/YunoHost-Apps/resetssh_ynh", "stars": 0, "forks": 0, "updated_at": "2024-06-19T09:30:38Z", "language": "Shell", "archived": true}, {"id": "restic_ynh", "name": "restic", "description": "Restic package for YunoHost", "url": "https://github.com/YunoHost-Apps/restic_ynh", "stars": 6, "forks": 11, "updated_at": "2025-05-02T20:17:16Z", "language": "Shell", "archived": false}, {"id": "restic-s3_ynh", "name": "restic-s3", "description": "Backup your YunoHost server with Restic over an S3 bucket", "url": "https://github.com/YunoHost-Apps/restic-s3_ynh", "stars": 0, "forks": 0, "updated_at": "2024-08-17T09:11:55Z", "language": "Shell", "archived": true}, {"id": "retroarch_ynh", "name": "retroarch", "description": "Emulate videogames system and play videogames through your web browser... and more!", "url": "https://github.com/YunoHost-Apps/retroarch_ynh", "stars": 7, "forks": 1, "updated_at": "2025-05-04T10:55:48Z", "language": "Shell", "archived": false}, {"id": "revealjs_ynh", "name": "revealjs", "description": "The HTML presentation framework", "url": "https://github.com/YunoHost-Apps/revealjs_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-17T18:35:31Z", "language": "Shell", "archived": false}, {"id": "reverseproxy_ynh", "name": "reverseproxy", "description": "WIP fork of redirect_ynh", "url": "https://github.com/YunoHost-Apps/reverseproxy_ynh", "stars": 4, "forks": 8, "updated_at": "2024-06-22T22:27:59Z", "language": "Shell", "archived": false}, {"id": "roadiz_ynh", "name": "roadiz", "description": "Modern CMS based on a node system", "url": "https://github.com/YunoHost-Apps/roadiz_ynh", "stars": 2, "forks": 3, "updated_at": "2024-06-19T09:29:46Z", "language": "Shell", "archived": true}, {"id": "rocketchat_ynh", "name": "rocketchat", "description": "Rocket.Chat package for YunoHost", "url": "https://github.com/YunoHost-Apps/rocketchat_ynh", "stars": 16, "forks": 22, "updated_at": "2025-03-19T18:07:56Z", "language": "Shell", "archived": false}, {"id": "roundcube_ynh", "name": "roundcube", "description": "Roundcube package for YunoHost", "url": "https://github.com/YunoHost-Apps/roundcube_ynh", "stars": 11, "forks": 24, "updated_at": "2025-06-03T07:50:19Z", "language": "Shell", "archived": false}, {"id": "rportd_ynh", "name": "rportd", "description": "Rportd package for YunoHost", "url": "https://github.com/YunoHost-Apps/rportd_ynh", "stars": 1, "forks": 2, "updated_at": "2024-05-15T18:02:39Z", "language": "Shell", "archived": true}, {"id": "rspamd_ynh", "name": "rspamd", "description": "Rspamd (spam filtering system) packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/rspamd_ynh", "stars": 3, "forks": 2, "updated_at": "2025-04-18T10:17:14Z", "language": "Shell", "archived": false}, {"id": "rspamdui_ynh", "name": "rspamdui", "description": "Web UI for rspamd", "url": "https://github.com/YunoHost-Apps/rspamdui_ynh", "stars": 1, "forks": 4, "updated_at": "2025-05-01T15:02:59Z", "language": "Shell", "archived": false}, {"id": "rss_ynh", "name": "rss", "description": "RSS packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/rss_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-20T08:22:31Z", "language": "Shell", "archived": false}, {"id": "rss-bridge_ynh", "name": "rss-bridge", "description": "RSS-Bridge package for YunoHost", "url": "https://github.com/YunoHost-Apps/rss-bridge_ynh", "stars": 16, "forks": 8, "updated_at": "2025-06-04T09:23:59Z", "language": "Shell", "archived": false}, {"id": "rsshub_ynh", "name": "rsshub", "description": "RSSHub package for YunoHost", "url": "https://github.com/YunoHost-Apps/rsshub_ynh", "stars": 3, "forks": 2, "updated_at": "2025-02-17T00:09:15Z", "language": "Shell", "archived": false}, {"id": "runalyze_ynh", "name": "runalyze", "description": "Runalyze package for yunohost", "url": "https://github.com/YunoHost-Apps/runalyze_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:54:21Z", "language": null, "archived": true}, {"id": "rustdesk-server_ynh", "name": "rustdesk-server", "description": "RustDesk package for YunoHost", "url": "https://github.com/YunoHost-Apps/rustdesk-server_ynh", "stars": 8, "forks": 6, "updated_at": "2025-04-29T09:53:23Z", "language": "Shell", "archived": false}, {"id": "rutorrent_ynh", "name": "ruto<PERSON>nt", "description": "rutorrent/rtorrent for YunoHost", "url": "https://github.com/YunoHost-Apps/rutorrent_ynh", "stars": 0, "forks": 0, "updated_at": "2024-06-19T09:15:21Z", "language": "Shell", "archived": true}, {"id": "saltcorn_ynh", "name": "saltcorn", "description": "", "url": "https://github.com/YunoHost-Apps/saltcorn_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-11T09:26:55Z", "language": "Shell", "archived": false}, {"id": "samba_ynh", "name": "samba", "description": "Samba package for YunoHost", "url": "https://github.com/YunoHost-Apps/samba_ynh", "stars": 1, "forks": 7, "updated_at": "2025-02-27T09:26:23Z", "language": "Shell", "archived": false}, {"id": "sat_ynh", "name": "sat", "description": "Salut à Toi package for YunoHost", "url": "https://github.com/YunoHost-Apps/sat_ynh", "stars": 4, "forks": 1, "updated_at": "2024-05-15T17:55:03Z", "language": "Shell", "archived": true}, {"id": "satdress_ynh", "name": "satdress", "description": "Satdress package for YunoHost", "url": "https://github.com/YunoHost-Apps/satdress_ynh", "stars": 6, "forks": 3, "updated_at": "2025-03-26T18:34:36Z", "language": "Shell", "archived": false}, {"id": "scovie_ynh", "name": "scovie", "description": "Scovie package for YunoHost", "url": "https://github.com/YunoHost-Apps/scovie_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-26T18:33:52Z", "language": "Shell", "archived": false}, {"id": "scratch_ynh", "name": "scratch", "description": "Scratch package for YunoHost", "url": "https://github.com/YunoHost-Apps/scratch_ynh", "stars": 3, "forks": 1, "updated_at": "2025-06-08T06:58:10Z", "language": "Shell", "archived": false}, {"id": "screego_ynh", "name": "screego", "description": "Screego package for YunoHost", "url": "https://github.com/YunoHost-Apps/screego_ynh", "stars": 1, "forks": 0, "updated_at": "2025-05-19T20:22:11Z", "language": "Shell", "archived": false}, {"id": "scribe_ynh", "name": "scribe", "description": "Interface web de transcription audio.", "url": "https://github.com/YunoHost-Apps/scribe_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:04:58Z", "language": null, "archived": false}, {"id": "scrumblr_ynh", "name": "scrumblr", "description": "Scrumblr package for YunoHost", "url": "https://github.com/YunoHost-Apps/scrumblr_ynh", "stars": 2, "forks": 4, "updated_at": "2025-03-23T21:11:53Z", "language": "Shell", "archived": false}, {"id": "scrutiny_ynh", "name": "scrutiny", "description": "WebUI for smartd S.M.A.R.T monitoring", "url": "https://github.com/YunoHost-Apps/scrutiny_ynh", "stars": 0, "forks": 1, "updated_at": "2024-11-19T09:45:55Z", "language": "Shell", "archived": false}, {"id": "seafile_ynh", "name": "seafile", "description": "Seafile package for YunoHost", "url": "https://github.com/YunoHost-Apps/seafile_ynh", "stars": 15, "forks": 19, "updated_at": "2025-06-07T08:53:34Z", "language": "Shell", "archived": false}, {"id": "searx_ynh", "name": "searx", "description": "Searx package for YunoHost", "url": "https://github.com/YunoHost-Apps/searx_ynh", "stars": 20, "forks": 18, "updated_at": "2024-06-23T12:35:45Z", "language": "Shell", "archived": false}, {"id": "searxng_ynh", "name": "searxng", "description": "Searxng package for YunoHost", "url": "https://github.com/YunoHost-Apps/searxng_ynh", "stars": 13, "forks": 4, "updated_at": "2025-06-02T06:10:58Z", "language": "Shell", "archived": false}, {"id": "selfoss_ynh", "name": "selfoss", "description": "Selfoss package for YunoHost", "url": "https://github.com/YunoHost-Apps/selfoss_ynh", "stars": 3, "forks": 3, "updated_at": "2024-08-31T13:08:23Z", "language": "Shell", "archived": false}, {"id": "send_ynh", "name": "send", "description": "Send package for YunoHost", "url": "https://github.com/YunoHost-Apps/send_ynh", "stars": 8, "forks": 2, "updated_at": "2025-04-24T07:35:40Z", "language": "Shell", "archived": false}, {"id": "shaarli_ynh", "name": "s<PERSON><PERSON><PERSON>", "description": "S<PERSON><PERSON>li package for YunoHost", "url": "https://github.com/YunoHost-Apps/shaarli_ynh", "stars": 20, "forks": 15, "updated_at": "2025-05-28T13:34:41Z", "language": "Shell", "archived": false}, {"id": "shadowsocks_ynh", "name": "shadowsocks", "description": "A secure socks5 proxy, designed to protect your Internet traffic.", "url": "https://github.com/YunoHost-Apps/shadowsocks_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:56:36Z", "language": "Shell", "archived": false}, {"id": "sharkey_ynh", "name": "sharkey", "description": "🌎 A Sharkish microblogging platform 🦈🚀", "url": "https://github.com/YunoHost-Apps/sharkey_ynh", "stars": 4, "forks": 6, "updated_at": "2025-05-31T12:56:30Z", "language": "Shell", "archived": false}, {"id": "shellinabox_ynh", "name": "shellinabox", "description": "Shell In A Box package for YunoHost", "url": "https://github.com/YunoHost-Apps/shellinabox_ynh", "stars": 7, "forks": 3, "updated_at": "2024-06-23T12:35:53Z", "language": "Shell", "archived": false}, {"id": "shields_ynh", "name": "shields", "description": "Shields package for YunoHost", "url": "https://github.com/YunoHost-Apps/shields_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-02T07:35:17Z", "language": "Shell", "archived": false}, {"id": "shinken_ynh", "name": "shinken", "description": "Shinken for YunoHost", "url": "https://github.com/YunoHost-Apps/shinken_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:56:31Z", "language": "Shell", "archived": true}, {"id": "shiori_ynh", "name": "shiori", "description": "Shiori package for YunoHost", "url": "https://github.com/YunoHost-Apps/shiori_ynh", "stars": 4, "forks": 3, "updated_at": "2025-03-19T17:44:05Z", "language": "Shell", "archived": false}, {"id": "shlink_ynh", "name": "shlink", "description": "Shlink package for YunoHost", "url": "https://github.com/YunoHost-Apps/shlink_ynh", "stars": 0, "forks": 2, "updated_at": "2025-03-02T21:42:59Z", "language": "Shell", "archived": false}, {"id": "shsd_ynh", "name": "shsd", "description": "SHSD package for YunoHost", "url": "https://github.com/YunoHost-Apps/shsd_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:55:02Z", "language": "Shell", "archived": true}, {"id": "shuri_ynh", "name": "shuri", "description": "<PERSON><PERSON> packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/shuri_ynh", "stars": 2, "forks": 2, "updated_at": "2025-02-19T21:46:43Z", "language": "Shell", "archived": false}, {"id": "sickbeard_ynh", "name": "sickbeard", "description": "Sickbeard package for Yunohost (install from fork)", "url": "https://github.com/YunoHost-Apps/sickbeard_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:50:42Z", "language": "Shell", "archived": true}, {"id": "sickrage_ynh", "name": "sickrage", "description": "SickRage for YunoHost", "url": "https://github.com/YunoHost-Apps/sickrage_ynh", "stars": 0, "forks": 2, "updated_at": "2024-05-15T17:52:58Z", "language": "Shell", "archived": true}, {"id": "sieve_ynh", "name": "sieve", "description": "Sieve Script Editor packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/sieve_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:58Z", "language": "Shell", "archived": false}, {"id": "signaturepdf_ynh", "name": "signaturepdf", "description": "Signaturepdf package fot YunoHost", "url": "https://github.com/YunoHost-Apps/signaturepdf_ynh", "stars": 2, "forks": 5, "updated_at": "2025-03-19T23:08:52Z", "language": "Shell", "archived": false}, {"id": "silverbullet_ynh", "name": "silverbullet", "description": "A creative space where you collect, create and expand your personal knowledge.", "url": "https://github.com/YunoHost-Apps/silverbullet_ynh", "stars": 3, "forks": 2, "updated_at": "2025-05-08T18:44:21Z", "language": "Shell", "archived": false}, {"id": "simpad_ynh", "name": "simpad", "description": "A simple Markdown Notepad", "url": "https://github.com/YunoHost-Apps/simpad_ynh", "stars": 2, "forks": 0, "updated_at": "2024-05-15T17:53:32Z", "language": "Shell", "archived": true}, {"id": "simple-file-manager_ynh", "name": "simple-file-manager", "description": "Simple File Manager package for YunoHost", "url": "https://github.com/YunoHost-Apps/simple-file-manager_ynh", "stars": 0, "forks": 1, "updated_at": "2024-10-23T06:20:36Z", "language": "PHP", "archived": true}, {"id": "simple-hash-generator_ynh", "name": "simple-hash-generator", "description": "Simple Hash Generator package for YunoHost", "url": "https://github.com/YunoHost-Apps/simple-hash-generator_ynh", "stars": 1, "forks": 1, "updated_at": "2024-11-15T21:52:50Z", "language": "Shell", "archived": false}, {"id": "simple-torrent_ynh", "name": "simple-torrent", "description": "Simple Torrent package for YunoHost", "url": "https://github.com/YunoHost-Apps/simple-torrent_ynh", "stars": 6, "forks": 2, "updated_at": "2024-06-19T10:32:05Z", "language": "Shell", "archived": true}, {"id": "simplex_ynh", "name": "simplex", "description": "SimpleX Chat package for YunoHost", "url": "https://github.com/YunoHost-Apps/simplex_ynh", "stars": 3, "forks": 5, "updated_at": "2025-04-16T20:15:41Z", "language": "Shell", "archived": false}, {"id": "simplytranslate_ynh", "name": "simplytranslate", "description": "SimplyTranslate package for YunoHost", "url": "https://github.com/YunoHost-Apps/simplytranslate_ynh", "stars": 0, "forks": 3, "updated_at": "2024-06-23T12:36:08Z", "language": "Shell", "archived": false}, {"id": "SitemagicCMS_ynh", "name": "SitemagicCMS", "description": "Sitemagic CMS package for YunoHost", "url": "https://github.com/YunoHost-Apps/SitemagicCMS_ynh", "stars": 1, "forks": 2, "updated_at": "2024-06-23T12:36:10Z", "language": "Shell", "archived": false}, {"id": "slingcode_ynh", "name": "slingcode", "description": "Slingcode package for YunoHost ", "url": "https://github.com/YunoHost-Apps/slingcode_ynh", "stars": 2, "forks": 2, "updated_at": "2024-08-05T11:51:51Z", "language": "HTML", "archived": false}, {"id": "smithereen_ynh", "name": "smithereen", "description": "Federated, ActivityPub-compatible social network server with friends, walls, and groups. https://github.com/grishka/Smithereen", "url": "https://github.com/YunoHost-Apps/smithereen_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T18:05:16Z", "language": "Shell", "archived": false}, {"id": "snappymail_ynh", "name": "snappymail", "description": "SnappyMail package for YunoHost", "url": "https://github.com/YunoHost-Apps/snappymail_ynh", "stars": 13, "forks": 8, "updated_at": "2025-05-18T20:31:12Z", "language": "Shell", "archived": false}, {"id": "snipeit_ynh", "name": "snipeit", "description": "Snipe-IT package for YunoHost", "url": "https://github.com/YunoHost-Apps/snipeit_ynh", "stars": 2, "forks": 2, "updated_at": "2025-06-07T08:48:33Z", "language": "Shell", "archived": false}, {"id": "snserver_ynh", "name": "s<PERSON><PERSON><PERSON>", "description": "Standard Notes Syncing Server", "url": "https://github.com/YunoHost-Apps/snserver_ynh", "stars": 6, "forks": 4, "updated_at": "2025-03-26T00:06:49Z", "language": "Shell", "archived": false}, {"id": "snweb_ynh", "name": "snweb", "description": "Standard Notes Web App", "url": "https://github.com/YunoHost-Apps/snweb_ynh", "stars": 5, "forks": 4, "updated_at": "2025-03-26T02:01:03Z", "language": "Shell", "archived": false}, {"id": "soapbox_ynh", "name": "soapbox", "description": "Soapbox package for YUnoHost", "url": "https://github.com/YunoHost-Apps/soapbox_ynh", "stars": 1, "forks": 1, "updated_at": "2024-10-02T10:16:41Z", "language": "Shell", "archived": false}, {"id": "sogo_ynh", "name": "sogo", "description": "SOGo Groupware for YunoHost.", "url": "https://github.com/YunoHost-Apps/sogo_ynh", "stars": 7, "forks": 7, "updated_at": "2025-05-18T20:31:23Z", "language": "Shell", "archived": false}, {"id": "soju_ynh", "name": "soju", "description": "", "url": "https://github.com/YunoHost-Apps/soju_ynh", "stars": 0, "forks": 0, "updated_at": "2024-12-07T18:42:02Z", "language": "Shell", "archived": false}, {"id": "sonarr_ynh", "name": "sonarr", "description": "Series collection manager for Usenet and BitTorrent users, packaged for YunoHost ", "url": "https://github.com/YunoHost-Apps/sonarr_ynh", "stars": 1, "forks": 2, "updated_at": "2025-03-19T00:39:02Z", "language": "Shell", "archived": false}, {"id": "sonerezh_ynh", "name": "sonerezh", "description": "Sorenezh package for YunoHost", "url": "https://github.com/YunoHost-Apps/sonerezh_ynh", "stars": 7, "forks": 3, "updated_at": "2024-05-15T17:51:11Z", "language": "Shell", "archived": true}, {"id": "spacedeck_ynh", "name": "<PERSON><PERSON>", "description": "Spacedeck, a web based, real time, collaborative whiteboard application with rich media support, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/spacedeck_ynh", "stars": 5, "forks": 1, "updated_at": "2025-03-24T22:28:54Z", "language": "Shell", "archived": false}, {"id": "spftoolbox_ynh", "name": "spftoolbox", "description": "See your record and check your domain settings", "url": "https://github.com/YunoHost-Apps/spftoolbox_ynh", "stars": 8, "forks": 1, "updated_at": "2025-05-19T20:29:45Z", "language": "Shell", "archived": false}, {"id": "sphinx_ynh", "name": "sphinx", "description": "Sphinx package for YunoHost", "url": "https://github.com/YunoHost-Apps/sphinx_ynh", "stars": 2, "forks": 0, "updated_at": "2024-06-19T09:12:01Z", "language": "Shell", "archived": true}, {"id": "spip_ynh", "name": "spip", "description": "SPIP package for YunoHost", "url": "https://github.com/YunoHost-Apps/spip_ynh", "stars": 5, "forks": 8, "updated_at": "2025-04-14T15:21:25Z", "language": "Shell", "archived": false}, {"id": "spip2_ynh", "name": "spip2", "description": "Spip2 for YunoHost", "url": "https://github.com/YunoHost-Apps/spip2_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:52:36Z", "language": "Shell", "archived": true}, {"id": "squash-tm_ynh", "name": "squash-tm", "description": "Squash-TM for YunoHost", "url": "https://github.com/YunoHost-Apps/squash-tm_ynh", "stars": 0, "forks": 1, "updated_at": "2024-05-15T17:57:08Z", "language": "TSQL", "archived": true}, {"id": "squid3_ynh", "name": "squid3", "description": "Squid is a caching proxy for the Web supporting HTTP, HTTPS, FTP, and more. ", "url": "https://github.com/YunoHost-Apps/squid3_ynh", "stars": 4, "forks": 2, "updated_at": "2025-04-15T15:49:09Z", "language": "Shell", "archived": false}, {"id": "ssbroom_ynh", "name": "ssbroom", "description": "Secure Scuttlebutt room server package for YunoHost. ", "url": "https://github.com/YunoHost-Apps/ssbroom_ynh", "stars": 12, "forks": 1, "updated_at": "2025-05-16T16:58:45Z", "language": "Shell", "archived": false}, {"id": "ssh_chroot_dir_ynh", "name": "ssh_chroot_dir", "description": "", "url": "https://github.com/YunoHost-Apps/ssh_chroot_dir_ynh", "stars": 2, "forks": 2, "updated_at": "2024-09-03T21:24:43Z", "language": "Shell", "archived": false}, {"id": "sshwifty_ynh", "name": "sshwifty", "description": "sshwifty package for YunoHost", "url": "https://github.com/YunoHost-Apps/sshwifty_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-07T05:51:51Z", "language": "Shell", "archived": false}, {"id": "staticwebapp_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Minimal web application for static files (vhost)", "url": "https://github.com/YunoHost-Apps/staticwebapp_ynh", "stars": 3, "forks": 1, "updated_at": "2024-05-15T17:51:24Z", "language": "Shell", "archived": true}, {"id": "statpingng_ynh", "name": "statpingng", "description": "StatpingNG package for YunoHost", "url": "https://github.com/YunoHost-Apps/statpingng_ynh", "stars": 2, "forks": 2, "updated_at": "2025-06-06T06:40:08Z", "language": "Shell", "archived": false}, {"id": "stirling-pdf_ynh", "name": "stirling-pdf", "description": "Stirling PDF package for YunoHost", "url": "https://github.com/YunoHost-Apps/stirling-pdf_ynh", "stars": 2, "forks": 3, "updated_at": "2025-05-11T09:47:37Z", "language": "Shell", "archived": false}, {"id": "streama_ynh", "name": "streama", "description": "Streama package for YunoHost", "url": "https://github.com/YunoHost-Apps/streama_ynh", "stars": 6, "forks": 5, "updated_at": "2024-06-09T14:47:53Z", "language": "Shell", "archived": false}, {"id": "streams_ynh", "name": "streams", "description": "Streams package for YunoHost", "url": "https://github.com/YunoHost-Apps/streams_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-08T21:48:46Z", "language": "Shell", "archived": false}, {"id": "stremio_ynh", "name": "stremio", "description": "Stremio package for YunoHost", "url": "https://github.com/YunoHost-Apps/stremio_ynh", "stars": 2, "forks": 0, "updated_at": "2025-02-01T19:03:04Z", "language": "Shell", "archived": false}, {"id": "strut_ynh", "name": "strut", "description": "Strut package for YunoHost", "url": "https://github.com/YunoHost-Apps/strut_ynh", "stars": 10, "forks": 3, "updated_at": "2024-08-31T13:11:18Z", "language": "Shell", "archived": false}, {"id": "studs_ynh", "name": "studs", "description": "A survey tool, the ancestor of OpenSondage", "url": "https://github.com/YunoHost-Apps/studs_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:56:20Z", "language": "Shell", "archived": true}, {"id": "stylo_ynh", "name": "stylo", "description": "", "url": "https://github.com/YunoHost-Apps/stylo_ynh", "stars": 1, "forks": 1, "updated_at": "2024-08-30T17:35:21Z", "language": "Shell", "archived": false}, {"id": "subscribe_ynh", "name": "subscribe", "description": "", "url": "https://github.com/YunoHost-Apps/subscribe_ynh", "stars": 1, "forks": 2, "updated_at": "2024-05-15T17:49:59Z", "language": "CSS", "archived": true}, {"id": "sugarizer_ynh", "name": "sugarizer", "description": "", "url": "https://github.com/YunoHost-Apps/sugarizer_ynh", "stars": 0, "forks": 0, "updated_at": "2024-10-24T20:03:20Z", "language": "Shell", "archived": false}, {"id": "superset_ynh", "name": "superset", "description": "Data Visualization and Data Exploration Platform, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/superset_ynh", "stars": 3, "forks": 3, "updated_at": "2025-05-06T23:54:39Z", "language": "Shell", "archived": false}, {"id": "sutom_ynh", "name": "<PERSON><PERSON>", "description": "Sutom package for YunoHost", "url": "https://github.com/YunoHost-Apps/sutom_ynh", "stars": 2, "forks": 2, "updated_at": "2025-05-02T20:28:06Z", "language": "Shell", "archived": false}, {"id": "svgedit_ynh", "name": "svgedit", "description": "SVG-edit package for YunoHost", "url": "https://github.com/YunoHost-Apps/svgedit_ynh", "stars": 4, "forks": 2, "updated_at": "2025-02-13T22:05:38Z", "language": "Shell", "archived": false}, {"id": "swingmusic_ynh", "name": "swingmusic", "description": "Swing Music package for Yun<PERSON><PERSON>", "url": "https://github.com/YunoHost-Apps/swingmusic_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-29T06:39:02Z", "language": "Shell", "archived": false}, {"id": "sympa_ynh", "name": "sympa", "description": "Attempt to package Sympa for Yunohost ...", "url": "https://github.com/YunoHost-Apps/sympa_ynh", "stars": 5, "forks": 2, "updated_at": "2024-05-15T17:52:51Z", "language": "Shell", "archived": true}, {"id": "synapse_ynh", "name": "synapse", "description": "Matrix server (synapse) package for YunoHost", "url": "https://github.com/YunoHost-Apps/synapse_ynh", "stars": 82, "forks": 43, "updated_at": "2025-06-02T21:43:48Z", "language": "Shell", "archived": false}, {"id": "synapse-admin_ynh", "name": "synapse-admin", "description": "synapse-admin package for YunoHost", "url": "https://github.com/YunoHost-Apps/synapse-admin_ynh", "stars": 6, "forks": 3, "updated_at": "2025-06-03T07:49:52Z", "language": "Shell", "archived": false}, {"id": "syncserver-rs_ynh", "name": "syncserver-rs", "description": "Firefox Sync Storage packaged for YunoHost.", "url": "https://github.com/YunoHost-Apps/syncserver-rs_ynh", "stars": 5, "forks": 2, "updated_at": "2025-06-04T07:49:32Z", "language": "Shell", "archived": false}, {"id": "syncthing_ynh", "name": "syncthing", "description": "Syncthing package for YunoHost", "url": "https://github.com/YunoHost-Apps/syncthing_ynh", "stars": 24, "forks": 6, "updated_at": "2025-06-07T08:35:39Z", "language": "Shell", "archived": false}, {"id": "tableaunoir_ynh", "name": "tableaunoir", "description": "Tableaunoir package for YunoHost", "url": "https://github.com/YunoHost-Apps/tableaunoir_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-03T17:37:47Z", "language": "Shell", "archived": false}, {"id": "tagspaces_ynh", "name": "tagspaces", "description": "TagSpaces package for YunoHost", "url": "https://github.com/YunoHost-Apps/tagspaces_ynh", "stars": 3, "forks": 1, "updated_at": "2024-05-15T17:51:19Z", "language": "JavaScript", "archived": true}, {"id": "tandoor_ynh", "name": "tandoor", "description": "Tandoor package for YunoHost", "url": "https://github.com/YunoHost-Apps/tandoor_ynh", "stars": 3, "forks": 7, "updated_at": "2025-05-18T20:31:25Z", "language": "Shell", "archived": false}, {"id": "taskboard_ynh", "name": "taskboard", "description": "TaskBoard package for YunoHost", "url": "https://github.com/YunoHost-Apps/taskboard_ynh", "stars": 1, "forks": 1, "updated_at": "2024-06-24T10:36:31Z", "language": "Shell", "archived": false}, {"id": "teampass_ynh", "name": "teampass", "description": "Teampass package for YunoHost", "url": "https://github.com/YunoHost-Apps/teampass_ynh", "stars": 2, "forks": 2, "updated_at": "2025-05-18T20:31:29Z", "language": "Shell", "archived": false}, {"id": "teamspeak-3_ynh", "name": "teamspeak-3", "description": "TeamSpeak package for YunoHost", "url": "https://github.com/YunoHost-Apps/teamspeak-3_ynh", "stars": 2, "forks": 5, "updated_at": "2024-05-15T17:55:36Z", "language": "Shell", "archived": true}, {"id": "technitium-dns_ynh", "name": "technitium-dns", "description": "DNS server for privacy & security package for YunoHost", "url": "https://github.com/YunoHost-Apps/technitium-dns_ynh", "stars": 4, "forks": 3, "updated_at": "2025-05-02T20:29:15Z", "language": "Shell", "archived": false}, {"id": "teddit_ynh", "name": "teddit", "description": "Teddit package for YunoHost", "url": "https://github.com/YunoHost-Apps/teddit_ynh", "stars": 3, "forks": 1, "updated_at": "2024-05-15T18:01:32Z", "language": "Shell", "archived": true}, {"id": "telegram_chatbot_ynh", "name": "telegram_chatbot", "description": "Installer un bot sur telegram avec YunoHost", "url": "https://github.com/YunoHost-Apps/telegram_chatbot_ynh", "stars": 3, "forks": 2, "updated_at": "2025-02-15T19:07:13Z", "language": "Shell", "archived": true}, {"id": "terraforming-mars_ynh", "name": "terraforming-mars", "description": " terraforming-mars package for YunoHost", "url": "https://github.com/YunoHost-Apps/terraforming-mars_ynh", "stars": 1, "forks": 2, "updated_at": "2025-06-02T07:39:17Z", "language": "Shell", "archived": false}, {"id": "tes3mp_ynh", "name": "tes3mp", "description": "tes3mp for YunoHost", "url": "https://github.com/YunoHost-Apps/tes3mp_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T17:56:22Z", "language": "Shell", "archived": true}, {"id": "test_app_for_translation_ynh", "name": "test_app_for_translation", "description": "This is a TEST APP to work on translating applications, do not interact with it plz :<", "url": "https://github.com/YunoHost-Apps/test_app_for_translation_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:07:05Z", "language": null, "archived": false}, {"id": "testupgrade_ynh", "name": "testupgrade", "description": "Test upgrade apps, for debug only ", "url": "https://github.com/YunoHost-Apps/testupgrade_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:52:30Z", "language": "Shell", "archived": true}, {"id": "text2quiz_ynh", "name": "text2quiz", "description": "Tex2quiz package for YunoHost", "url": "https://github.com/YunoHost-Apps/text2quiz_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:05:56Z", "language": "Shell", "archived": false}, {"id": "theia_ide_ynh", "name": "theia_ide", "description": "A cloud & desktop IDE framework implemented in TypeScript.", "url": "https://github.com/YunoHost-Apps/theia_ide_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:35Z", "language": "Shell", "archived": false}, {"id": "thelounge_ynh", "name": "thelounge", "description": "The Lounge package for YunoHost", "url": "https://github.com/YunoHost-Apps/thelounge_ynh", "stars": 10, "forks": 8, "updated_at": "2025-03-27T02:00:49Z", "language": "JavaScript", "archived": false}, {"id": "tiddlywiki_ynh", "name": "tid<PERSON><PERSON><PERSON>", "description": "TiddlyWiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/tiddlywiki_ynh", "stars": 6, "forks": 4, "updated_at": "2025-05-29T10:54:49Z", "language": "Shell", "archived": false}, {"id": "tiki_ynh", "name": "tiki", "description": "Tiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/tiki_ynh", "stars": 4, "forks": 4, "updated_at": "2025-02-12T22:35:14Z", "language": "Shell", "archived": false}, {"id": "timemachine_ynh", "name": "timemachine", "description": "Use your local YunoHost server as a Time Machine Backup Drive", "url": "https://github.com/YunoHost-Apps/timemachine_ynh", "stars": 5, "forks": 3, "updated_at": "2025-04-05T14:37:35Z", "language": "Shell", "archived": false}, {"id": "timeoff_ynh", "name": "timeoff", "description": "Absence management software for small and medium size business.", "url": "https://github.com/YunoHost-Apps/timeoff_ynh", "stars": 1, "forks": 2, "updated_at": "2025-05-09T09:40:05Z", "language": "Shell", "archived": false}, {"id": "tinyfilemanager_ynh", "name": "tinyfilemanager", "description": "Tiny File Manager package for YunoHost", "url": "https://github.com/YunoHost-Apps/tinyfilemanager_ynh", "stars": 2, "forks": 2, "updated_at": "2025-05-18T20:31:40Z", "language": "Shell", "archived": false}, {"id": "tldraw_ynh", "name": "tldraw", "description": "A tiny little drawing app", "url": "https://github.com/YunoHost-Apps/tldraw_ynh", "stars": 1, "forks": 3, "updated_at": "2025-03-24T16:27:13Z", "language": "Shell", "archived": false}, {"id": "tooljet_ynh", "name": "tooljet", "description": "Open-source low-code framework for building React-based web applications and internal tools", "url": "https://github.com/YunoHost-Apps/tooljet_ynh", "stars": 5, "forks": 2, "updated_at": "2025-01-28T19:46:48Z", "language": "Shell", "archived": false}, {"id": "torrelay_ynh", "name": "torrelay", "description": "TorRelay package for YunoHost", "url": "https://github.com/YunoHost-Apps/torrelay_ynh", "stars": 1, "forks": 2, "updated_at": "2025-02-13T22:50:02Z", "language": "Shell", "archived": false}, {"id": "traccar_ynh", "name": "traccar", "description": "Traccar package for YunoHost", "url": "https://github.com/YunoHost-Apps/traccar_ynh", "stars": 0, "forks": 3, "updated_at": "2025-06-02T07:36:33Z", "language": "Shell", "archived": false}, {"id": "tracim_ynh", "name": "tracim", "description": "Tracim package for YunoHost", "url": "https://github.com/YunoHost-Apps/tracim_ynh", "stars": 3, "forks": 0, "updated_at": "2025-01-31T20:05:13Z", "language": "Shell", "archived": false}, {"id": "traggo_ynh", "name": "traggo", "description": "Traggo package for YunoHost", "url": "https://github.com/YunoHost-Apps/traggo_ynh", "stars": 2, "forks": 1, "updated_at": "2025-04-29T08:16:04Z", "language": "Shell", "archived": false}, {"id": "transfersh_ynh", "name": "transfersh", "description": "Transfersh package for YunoHost", "url": "https://github.com/YunoHost-Apps/transfersh_ynh", "stars": 3, "forks": 1, "updated_at": "2025-01-21T21:38:52Z", "language": "Shell", "archived": false}, {"id": "transmission_ynh", "name": "transmission", "description": "Transmission package for YunoHost", "url": "https://github.com/YunoHost-Apps/transmission_ynh", "stars": 7, "forks": 19, "updated_at": "2024-12-17T17:41:44Z", "language": "Shell", "archived": false}, {"id": "transpay_ynh", "name": "transpay", "description": "Transpay is a donation interface for Stripe. This is a Yunohost package to install it.", "url": "https://github.com/YunoHost-Apps/transpay_ynh", "stars": 7, "forks": 0, "updated_at": "2024-05-15T17:57:05Z", "language": "Shell", "archived": true}, {"id": "trilium_ynh", "name": "trilium", "description": "Trilium package for YunoHost", "url": "https://github.com/YunoHost-Apps/trilium_ynh", "stars": 12, "forks": 9, "updated_at": "2025-04-26T07:49:39Z", "language": "Shell", "archived": false}, {"id": "trustyhash_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Trusty Hash package for YunoHost", "url": "https://github.com/YunoHost-Apps/trustyhash_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-01T22:18:21Z", "language": "Shell", "archived": false}, {"id": "ttrss_ynh", "name": "ttrss", "description": "Tiny Tiny RSS package for YunoHost", "url": "https://github.com/YunoHost-Apps/ttrss_ynh", "stars": 17, "forks": 14, "updated_at": "2025-06-09T09:16:51Z", "language": "Shell", "archived": false}, {"id": "tube_ynh", "name": "tube", "description": "Tube package for YunoHost", "url": "https://github.com/YunoHost-Apps/tube_ynh", "stars": 4, "forks": 2, "updated_at": "2024-12-21T12:29:42Z", "language": "Shell", "archived": false}, {"id": "turtl_ynh", "name": "turtl", "description": "Turtl package for YunoHost", "url": "https://github.com/YunoHost-Apps/turtl_ynh", "stars": 6, "forks": 5, "updated_at": "2024-06-23T12:37:28Z", "language": "Shell", "archived": false}, {"id": "tutao_ynh", "name": "tutao", "description": "encrypted mail client", "url": "https://github.com/YunoHost-Apps/tutao_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:52:53Z", "language": "Shell", "archived": true}, {"id": "tvheadend_ynh", "name": "tvheadend", "description": "Tvheadend package for YunoHost", "url": "https://github.com/YunoHost-Apps/tvheadend_ynh", "stars": 6, "forks": 3, "updated_at": "2025-01-09T21:19:21Z", "language": "Shell", "archived": false}, {"id": "tyto_ynh", "name": "tyto", "description": "Tyto package for YunoHost", "url": "https://github.com/YunoHost-Apps/tyto_ynh", "stars": 6, "forks": 1, "updated_at": "2024-06-23T12:41:48Z", "language": "Shell", "archived": false}, {"id": "ulogger_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "μlogger package for YunoHost", "url": "https://github.com/YunoHost-Apps/ulogger_ynh", "stars": 5, "forks": 1, "updated_at": "2025-03-23T13:59:12Z", "language": "Shell", "archived": false}, {"id": "umami_ynh", "name": "umami", "description": "Umami package for YunoHost", "url": "https://github.com/YunoHost-Apps/umami_ynh", "stars": 3, "forks": 3, "updated_at": "2025-05-13T10:11:18Z", "language": "Shell", "archived": false}, {"id": "umap_ynh", "name": "umap", "description": "\"uMap let you create maps with OpenStreetMap layers in a minute and embed them in your site.\"[uMap Website]", "url": "https://github.com/YunoHost-Apps/umap_ynh", "stars": 12, "forks": 7, "updated_at": "2024-05-15T17:52:44Z", "language": "Shell", "archived": true}, {"id": "ums_ynh", "name": "ums", "description": "UniversalMediaServer : A DLNA, UPnP and HTTP(S) Media Server packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/ums_ynh", "stars": 1, "forks": 4, "updated_at": "2025-05-01T08:05:53Z", "language": "Shell", "archived": false}, {"id": "unattended_upgrades_ynh", "name": "unattended_upgrades", "description": "unattended_upgrades package for YunoHost", "url": "https://github.com/YunoHost-Apps/unattended_upgrades_ynh", "stars": 8, "forks": 2, "updated_at": "2025-05-06T23:55:17Z", "language": "Shell", "archived": false}, {"id": "unbound_ynh", "name": "unbound", "description": "Unbound package for YunoHost", "url": "https://github.com/YunoHost-Apps/unbound_ynh", "stars": 3, "forks": 0, "updated_at": "2024-06-19T09:18:45Z", "language": "Shell", "archived": true}, {"id": "uptime-kuma_ynh", "name": "uptime-kuma", "description": "Uptime Kuma package for YunoHost", "url": "https://github.com/YunoHost-Apps/uptime-kuma_ynh", "stars": 13, "forks": 5, "updated_at": "2025-01-31T00:49:39Z", "language": "Shell", "archived": false}, {"id": "urbackup-server_ynh", "name": "urbackup-server", "description": "", "url": "https://github.com/YunoHost-Apps/urbackup-server_ynh", "stars": 1, "forks": 0, "updated_at": "2024-06-19T09:17:33Z", "language": "Shell", "archived": true}, {"id": "vaultwarden_ynh", "name": "vaultwarden", "description": "Open source password management solutions for YunoHost", "url": "https://github.com/YunoHost-Apps/vaultwarden_ynh", "stars": 59, "forks": 20, "updated_at": "2025-06-08T17:59:11Z", "language": "Shell", "archived": false}, {"id": "veloren_ynh", "name": "veloren", "description": "Veloren server package for YunoHost", "url": "https://github.com/YunoHost-Apps/veloren_ynh", "stars": 3, "forks": 0, "updated_at": "2024-06-23T12:41:58Z", "language": "Shell", "archived": false}, {"id": "vert_ynh", "name": "vert", "description": "Vert package for YunoHost", "url": "https://github.com/YunoHost-Apps/vert_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-03T17:02:24Z", "language": "Shell", "archived": false}, {"id": "vikunja_ynh", "name": "vik<PERSON><PERSON>", "description": "Vikunja package for YunoHost", "url": "https://github.com/YunoHost-Apps/vikunja_ynh", "stars": 11, "forks": 9, "updated_at": "2025-05-18T20:31:45Z", "language": "Shell", "archived": false}, {"id": "vintagestory_ynh", "name": "vintagestory", "description": "Vintage Story package for YunoHost", "url": "https://github.com/YunoHost-Apps/vintagestory_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:55:56Z", "language": "Shell", "archived": true}, {"id": "vore_ynh", "name": "vore", "description": "Vore package for YunoHost", "url": "https://github.com/YunoHost-Apps/vore_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-21T15:34:19Z", "language": "Shell", "archived": false}, {"id": "vpnclient_ynh", "name": "vpnclient", "description": "VPN Client app for YunoHost", "url": "https://github.com/YunoHost-Apps/vpnclient_ynh", "stars": 43, "forks": 27, "updated_at": "2025-06-02T07:39:10Z", "language": "Shell", "archived": false}, {"id": "vpnserver_ynh", "name": "vpnserver", "description": "OpenVPN package for YunoHost", "url": "https://github.com/YunoHost-Apps/vpnserver_ynh", "stars": 19, "forks": 21, "updated_at": "2025-04-29T14:51:49Z", "language": "Shell", "archived": true}, {"id": "vvveb_ynh", "name": "vvveb", "description": "Vvveb package for YunoHost", "url": "https://github.com/YunoHost-Apps/vvveb_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-03T11:39:48Z", "language": "Shell", "archived": false}, {"id": "wallabag_ynh", "name": "wallabag", "description": "Wallabag v1 package for YunoHost", "url": "https://github.com/YunoHost-Apps/wallabag_ynh", "stars": 5, "forks": 10, "updated_at": "2024-05-15T17:48:33Z", "language": "Shell", "archived": true}, {"id": "wallabag2_ynh", "name": "wallabag2", "description": "Wallabag v2 package for YunoHost", "url": "https://github.com/YunoHost-Apps/wallabag2_ynh", "stars": 62, "forks": 15, "updated_at": "2025-06-07T09:02:51Z", "language": "Shell", "archived": false}, {"id": "wallos_ynh", "name": "wallos", "description": "Wallos package for YunoHost", "url": "https://github.com/YunoHost-Apps/wallos_ynh", "stars": 1, "forks": 0, "updated_at": "2025-06-10T07:43:02Z", "language": "Shell", "archived": false}, {"id": "wanderer_ynh", "name": "wanderer", "description": "Wanderer package for YunoHost", "url": "https://github.com/YunoHost-Apps/wanderer_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-27T07:47:31Z", "language": "Shell", "archived": false}, {"id": "watchdog_ynh", "name": "watchdog", "description": "Service to automagically reboot your server in case of freeze, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/watchdog_ynh", "stars": 1, "forks": 1, "updated_at": "2024-09-17T14:14:58Z", "language": "Shell", "archived": false}, {"id": "watchyour<PERSON>_ynh", "name": "<PERSON>yo<PERSON><PERSON>", "description": "WatchYourLAN package for YunoHost", "url": "https://github.com/YunoHost-Apps/watchyourlan_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-02T20:10:40Z", "language": "Shell", "archived": false}, {"id": "waterbear_ynh", "name": "waterbear", "description": "", "url": "https://github.com/YunoHost-Apps/waterbear_ynh", "stars": 0, "forks": 0, "updated_at": "2024-06-19T09:16:47Z", "language": "Shell", "archived": true}, {"id": "webdav_ynh", "name": "webdav", "description": "WebDAV package for YunoHost", "url": "https://github.com/YunoHost-Apps/webdav_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-25T20:59:24Z", "language": "Shell", "archived": false}, {"id": "webhook_ynh", "name": "webhook", "description": "Webhook package for YunoHost", "url": "https://github.com/YunoHost-Apps/webhook_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-23T07:54:30Z", "language": "Shell", "archived": false}, {"id": "weblate_ynh", "name": "weblate", "description": "Weblate package for YunoHost", "url": "https://github.com/YunoHost-Apps/weblate_ynh", "stars": 13, "forks": 11, "updated_at": "2025-05-12T19:38:17Z", "language": "Python", "archived": false}, {"id": "webmin_ynh", "name": "webmin", "description": "Webmin package for YunoHost", "url": "https://github.com/YunoHost-Apps/webmin_ynh", "stars": 5, "forks": 5, "updated_at": "2025-06-05T06:35:42Z", "language": "Shell", "archived": false}, {"id": "webogram_ynh", "name": "webogram", "description": "Webogram for <PERSON><PERSON><PERSON>", "url": "https://github.com/YunoHost-Apps/webogram_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:53:06Z", "language": "Shell", "archived": true}, {"id": "webtrees_ynh", "name": "webtrees", "description": "Webtrees package for YunoHost", "url": "https://github.com/YunoHost-Apps/webtrees_ynh", "stars": 7, "forks": 5, "updated_at": "2025-01-03T14:31:46Z", "language": "Shell", "archived": false}, {"id": "wekan_ynh", "name": "wekan", "description": "Wekan package for YunoHost", "url": "https://github.com/YunoHost-Apps/wekan_ynh", "stars": 17, "forks": 18, "updated_at": "2025-05-26T05:58:21Z", "language": "Shell", "archived": false}, {"id": "wemawema_ynh", "name": "we<PERSON><PERSON><PERSON>", "description": "WemaWema package for YunoHost", "url": "https://github.com/YunoHost-Apps/wemawema_ynh", "stars": 5, "forks": 1, "updated_at": "2025-01-22T14:24:26Z", "language": "Shell", "archived": false}, {"id": "wetty_ynh", "name": "wetty", "description": "Wetty package for YunoHost", "url": "https://github.com/YunoHost-Apps/wetty_ynh", "stars": 5, "forks": 0, "updated_at": "2025-03-31T14:07:09Z", "language": "Shell", "archived": false}, {"id": "whitebophir_ynh", "name": "white<PERSON><PERSON>r", "description": "Whitebophir package for YunoHost", "url": "https://github.com/YunoHost-Apps/whitebophir_ynh", "stars": 1, "forks": 3, "updated_at": "2025-04-06T15:26:10Z", "language": "Shell", "archived": false}, {"id": "whoogle_ynh", "name": "whoogle", "description": "Whoogle package for YunoHost", "url": "https://github.com/YunoHost-Apps/whoogle_ynh", "stars": 0, "forks": 1, "updated_at": "2025-03-02T16:24:18Z", "language": "Shell", "archived": false}, {"id": "wiki-go_ynh", "name": "wiki-go", "description": "", "url": "https://github.com/YunoHost-Apps/wiki-go_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-15T17:07:45Z", "language": "Shell", "archived": false}, {"id": "wikijs_ynh", "name": "wikijs", "description": "Modern and powerful wiki app package for YunoHost", "url": "https://github.com/YunoHost-Apps/wikijs_ynh", "stars": 18, "forks": 5, "updated_at": "2025-03-25T03:36:42Z", "language": "Shell", "archived": false}, {"id": "wildfly_ynh", "name": "wildfly", "description": "WildFly is a flexible, lightweight, managed application runtime that helps you build amazing applications. http://wildfly.org", "url": "https://github.com/YunoHost-Apps/wildfly_ynh", "stars": 1, "forks": 0, "updated_at": "2024-06-19T09:20:08Z", "language": "Shell", "archived": true}, {"id": "wireguard_ynh", "name": "wireguard", "description": "WireGuard VPN software with a web UI configuration companion, packaged for YunoHost.", "url": "https://github.com/YunoHost-Apps/wireguard_ynh", "stars": 37, "forks": 12, "updated_at": "2025-05-12T13:20:58Z", "language": "Shell", "archived": false}, {"id": "wireguard_client_ynh", "name": "wireguard_client", "description": "WireGuard Client configuration app, for YunoHost", "url": "https://github.com/YunoHost-Apps/wireguard_client_ynh", "stars": 4, "forks": 2, "updated_at": "2025-01-20T10:36:29Z", "language": "Shell", "archived": false}, {"id": "wisemapping_ynh", "name": "wisemapping", "description": "Wisemapping package for YunoHost", "url": "https://github.com/YunoHost-Apps/wisemapping_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T17:54:10Z", "language": "Shell", "archived": true}, {"id": "wishthis_ynh", "name": "<PERSON><PERSON><PERSON>", "description": "Wishthis package for YunoHost", "url": "https://github.com/YunoHost-Apps/wishthis_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:06:21Z", "language": "Shell", "archived": false}, {"id": "wondercms_ynh", "name": "wondercms", "description": "WonderCMS package for YunoHost", "url": "https://github.com/YunoHost-Apps/wondercms_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-19T00:44:51Z", "language": "Shell", "archived": false}, {"id": "woodpecker_ynh", "name": "woodpecker", "description": "Woodpecker, package for YunoHost", "url": "https://github.com/YunoHost-Apps/woodpecker_ynh", "stars": 2, "forks": 1, "updated_at": "2025-06-07T06:06:01Z", "language": "Shell", "archived": false}, {"id": "woodpecker-agent_ynh", "name": "woodpecker-agent", "description": "Yunohost package for Woodpecker Agents, the CI runners for Woodpecker", "url": "https://github.com/YunoHost-Apps/woodpecker-agent_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:05:38Z", "language": "Shell", "archived": false}, {"id": "wordpress_ynh", "name": "wordpress", "description": "WordPress package for YunoHost", "url": "https://github.com/YunoHost-Apps/wordpress_ynh", "stars": 27, "forks": 30, "updated_at": "2025-05-19T06:53:24Z", "language": "Shell", "archived": false}, {"id": "workout-tracker_ynh", "name": "workout-tracker", "description": "workout-tracker package for YunoHost", "url": "https://github.com/YunoHost-Apps/workout-tracker_ynh", "stars": 0, "forks": 2, "updated_at": "2025-04-15T06:18:46Z", "language": "Shell", "archived": false}, {"id": "writefreely_ynh", "name": "writefreely", "description": "WriteFreely is a beautifully pared-down blogging platform that's simple on the surface, yet powerful underneath for YunoHost", "url": "https://github.com/YunoHost-Apps/writefreely_ynh", "stars": 17, "forks": 1, "updated_at": "2025-05-02T20:17:55Z", "language": "Shell", "archived": false}, {"id": "X-prober_ynh", "name": "X-prober", "description": "Prober for PHP environment packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/X-prober_ynh", "stars": 2, "forks": 1, "updated_at": "2025-05-09T20:53:35Z", "language": "Shell", "archived": false}, {"id": "xbackbone_ynh", "name": "xbackbone", "description": "XBackBone package for YunoHost", "url": "https://github.com/YunoHost-Apps/xbackbone_ynh", "stars": 0, "forks": 1, "updated_at": "2025-04-15T15:50:52Z", "language": "Shell", "archived": false}, {"id": "xdownloader_ynh", "name": "xdownloader", "description": "XDownloader for YunoHost", "url": "https://github.com/YunoHost-Apps/xdownloader_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T17:56:14Z", "language": "Shell", "archived": true}, {"id": "xwiki_ynh", "name": "xwiki", "description": "xWiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/xwiki_ynh", "stars": 0, "forks": 1, "updated_at": "2025-06-03T19:48:39Z", "language": "Shell", "archived": false}, {"id": "yacy_ynh", "name": "yacy", "description": "Libre and decentralized search engine for YunoHost", "url": "https://github.com/YunoHost-Apps/yacy_ynh", "stars": 7, "forks": 7, "updated_at": "2025-03-23T19:02:30Z", "language": "Shell", "archived": false}, {"id": "yarr_ynh", "name": "yarr", "description": "Yarr package for YunoHost", "url": "https://github.com/YunoHost-Apps/yarr_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-27T18:23:53Z", "language": "Shell", "archived": false}, {"id": "yeetfile_ynh", "name": "yeetfile", "description": "YeetFile package for YunoHost", "url": "https://github.com/YunoHost-Apps/yeetfile_ynh", "stars": 0, "forks": 0, "updated_at": "2025-04-16T14:13:04Z", "language": "Shell", "archived": false}, {"id": "yellow_ynh", "name": "yellow", "description": "Datenstrom Yellow package for YunoHost", "url": "https://github.com/YunoHost-Apps/yellow_ynh", "stars": 2, "forks": 1, "updated_at": "2025-04-09T14:55:45Z", "language": "Shell", "archived": false}, {"id": "yeswiki_ynh", "name": "<PERSON>wiki", "description": "YesWiki package for YunoHost", "url": "https://github.com/YunoHost-Apps/yeswiki_ynh", "stars": 6, "forks": 6, "updated_at": "2025-05-21T17:48:24Z", "language": "Shell", "archived": false}, {"id": "your_spotify_ynh", "name": "your_spotify", "description": "Your Spotify package for Yunohost", "url": "https://github.com/YunoHost-Apps/your_spotify_ynh", "stars": 0, "forks": 0, "updated_at": "2024-05-15T18:05:48Z", "language": "Shell", "archived": false}, {"id": "yourls_ynh", "name": "yourls", "description": "YOURLS package for YunoHost", "url": "https://github.com/YunoHost-Apps/yourls_ynh", "stars": 8, "forks": 5, "updated_at": "2025-04-26T12:01:58Z", "language": "Shell", "archived": false}, {"id": "Youtube-dl-WebUI_ynh", "name": "Youtube-dl-WebUI", "description": "Yunohost package for p1rox's Youtube-dl-WebUI", "url": "https://github.com/YunoHost-Apps/Youtube-dl-WebUI_ynh", "stars": 2, "forks": 2, "updated_at": "2024-05-15T17:50:49Z", "language": "Shell", "archived": true}, {"id": "yuno_goti_notify_ynh", "name": "yuno_goti_notify", "description": "A collection of Yunohost hooks to send notifications to a Gotify server", "url": "https://github.com/YunoHost-Apps/yuno_goti_notify_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-07T19:19:59Z", "language": "Shell", "archived": false}, {"id": "yuno-archive_ynh", "name": "yuno-archive", "description": "", "url": "https://github.com/YunoHost-Apps/yuno-archive_ynh", "stars": 0, "forks": 0, "updated_at": "2025-06-07T08:45:26Z", "language": "Shell", "archived": false}, {"id": "yunofav_ynh", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Favorite links for YunoHost", "url": "https://github.com/YunoHost-Apps/yunofav_ynh", "stars": 4, "forks": 1, "updated_at": "2024-06-19T09:19:28Z", "language": "CSS", "archived": true}, {"id": "yunohost_appgenerator_ynh", "name": "yunohost_appgenerator", "description": "", "url": "https://github.com/YunoHost-Apps/yunohost_appgenerator_ynh", "stars": 2, "forks": 0, "updated_at": "2024-11-24T22:51:15Z", "language": "Shell", "archived": false}, {"id": "yunohost_appstore_ynh", "name": "yunohost_appstore", "description": "Appstore package for YunoHost ", "url": "https://github.com/YunoHost-Apps/yunohost_appstore_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-28T14:23:16Z", "language": "Shell", "archived": false}, {"id": "yunohost_demo_ynh", "name": "yunohost_demo", "description": "", "url": "https://github.com/YunoHost-Apps/yunohost_demo_ynh", "stars": 0, "forks": 0, "updated_at": "2024-12-23T11:17:31Z", "language": "Shell", "archived": false}, {"id": "yunohost_diagnosis_ynh", "name": "yunohost_diagnosis", "description": "Source code of diagnosis.yunohost.org", "url": "https://github.com/YunoHost-Apps/yunohost_diagnosis_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-17T16:33:58Z", "language": "Shell", "archived": false}, {"id": "yunohost_dynette_ynh", "name": "yunohost_dynette", "description": "YunoHost package YunoHost DynDNS Server", "url": "https://github.com/YunoHost-Apps/yunohost_dynette_ynh", "stars": 2, "forks": 0, "updated_at": "2024-11-17T16:34:07Z", "language": "Shell", "archived": false}, {"id": "yunohost_landingpage_ynh", "name": "yunohost_landingpage", "description": "YunoHost landingpage app", "url": "https://github.com/YunoHost-Apps/yunohost_landingpage_ynh", "stars": 0, "forks": 0, "updated_at": "2025-03-31T12:44:59Z", "language": "Shell", "archived": false}, {"id": "yuno<PERSON>t_myip_ynh", "name": "yuno<PERSON>t_myip", "description": "What's-my-ip-like service ", "url": "https://github.com/YunoHost-Apps/yunohost_myip_ynh", "stars": 0, "forks": 0, "updated_at": "2024-11-17T16:34:21Z", "language": "Shell", "archived": false}, {"id": "yunomonitor_ynh", "name": "yuno<PERSON><PERSON>", "description": "An home made automated tools to monitor your servers", "url": "https://github.com/YunoHost-Apps/yunomonitor_ynh", "stars": 7, "forks": 2, "updated_at": "2024-08-03T17:17:20Z", "language": "Shell", "archived": false}, {"id": "yunorunner_ynh", "name": "yuno<PERSON>", "description": "YunoRunner package  for YunoHost", "url": "https://github.com/YunoHost-Apps/yunorunner_ynh", "stars": 6, "forks": 5, "updated_at": "2025-01-31T08:40:32Z", "language": "Shell", "archived": false}, {"id": "z-push_ynh", "name": "z-push", "description": "Application to synchronize ActiveSync compatible devices with email, contacts and calendar for YunoHost", "url": "https://github.com/YunoHost-Apps/z-push_ynh", "stars": 13, "forks": 13, "updated_at": "2025-05-18T20:35:33Z", "language": "PHP", "archived": false}, {"id": "zabbix_ynh", "name": "zabbix", "description": "Zabbix package for YunoHost", "url": "https://github.com/YunoHost-Apps/zabbix_ynh", "stars": 6, "forks": 6, "updated_at": "2025-06-06T21:17:36Z", "language": "Shell", "archived": false}, {"id": "zam<PERSON>_ynh", "name": "<PERSON>am<PERSON>", "description": "Web-based, open source user support/ticketing solution.", "url": "https://github.com/YunoHost-Apps/zammad_ynh", "stars": 1, "forks": 0, "updated_at": "2024-05-15T18:04:11Z", "language": "Shell", "archived": false}, {"id": "zap_ynh", "name": "zap", "description": "Zap package for YunoHost", "url": "https://github.com/YunoHost-Apps/zap_ynh", "stars": 6, "forks": 5, "updated_at": "2024-06-23T12:42:39Z", "language": "Shell", "archived": false}, {"id": "zerobin_ynh", "name": "zerobin", "description": "PrivateBin package for YunoHost", "url": "https://github.com/YunoHost-Apps/zerobin_ynh", "stars": 11, "forks": 12, "updated_at": "2025-02-19T21:48:21Z", "language": "Shell", "archived": false}, {"id": "zeronet_ynh", "name": "zeronet", "description": "Zeronet package for YunoHost", "url": "https://github.com/YunoHost-Apps/zeronet_ynh", "stars": 9, "forks": 3, "updated_at": "2024-05-15T17:52:39Z", "language": "Shell", "archived": true}, {"id": "zerotier_ynh", "name": "zerotier", "description": "ZeroTier networking app for YunoHost", "url": "https://github.com/YunoHost-Apps/zerotier_ynh", "stars": 7, "forks": 2, "updated_at": "2024-11-01T08:57:17Z", "language": "Shell", "archived": false}, {"id": "zeroui_ynh", "name": "zeroui", "description": "ZeroTier Controller Web UI, packaged for YunoHost", "url": "https://github.com/YunoHost-Apps/zeroui_ynh", "stars": 0, "forks": 2, "updated_at": "2025-05-29T11:34:48Z", "language": "Shell", "archived": false}, {"id": "zigbee2mqtt_ynh", "name": "zigbee2mqtt", "description": "zigbee2mqtt as YunoHost package", "url": "https://github.com/YunoHost-Apps/zigbee2mqtt_ynh", "stars": 1, "forks": 1, "updated_at": "2024-05-15T18:06:43Z", "language": "Shell", "archived": false}, {"id": "zipline_ynh", "name": "zipline", "description": "Zipline package for YunoHost", "url": "https://github.com/YunoHost-Apps/zipline_ynh", "stars": 0, "forks": 3, "updated_at": "2025-06-07T06:05:31Z", "language": "Shell", "archived": false}, {"id": "zola_ynh", "name": "zola", "description": "Zola package for YunoHost", "url": "https://github.com/YunoHost-Apps/zola_ynh", "stars": 3, "forks": 3, "updated_at": "2025-05-05T21:59:06Z", "language": "Shell", "archived": false}, {"id": "zoraxy_ynh", "name": "z<PERSON><PERSON>", "description": "Zoraxy package for YunoHost", "url": "https://github.com/YunoHost-Apps/zoraxy_ynh", "stars": 0, "forks": 2, "updated_at": "2025-02-21T22:13:05Z", "language": "Shell", "archived": false}, {"id": "ztncui_ynh", "name": "ztncui", "description": "ZeroTier network controller user interface for YunoHost", "url": "https://github.com/YunoHost-Apps/ztncui_ynh", "stars": 2, "forks": 3, "updated_at": "2025-03-24T20:27:22Z", "language": "Shell", "archived": false}, {"id": "zulip_ynh", "name": "<PERSON><PERSON>", "description": "Zulip package for YunoHost", "url": "https://github.com/YunoHost-Apps/zulip_ynh", "stars": 0, "forks": 2, "updated_at": "2025-05-17T11:51:56Z", "language": "Shell", "archived": false}, {"id": "zusam_ynh", "name": "zusam", "description": "Zusam package for YunoHost", "url": "https://github.com/YunoHost-Apps/zusam_ynh", "stars": 1, "forks": 1, "updated_at": "2025-03-24T16:28:54Z", "language": "Shell", "archived": false}, {"id": "zwave-js-ui_ynh", "name": "zwave-js-ui", "description": "Fully configurable Z-Wave to MQTT Gateway and Control Panel", "url": "https://github.com/YunoHost-Apps/zwave-js-ui_ynh", "stars": 0, "forks": 1, "updated_at": "2025-05-30T06:57:48Z", "language": "Shell", "archived": false}, {"id": "zwiicms_ynh", "name": "zwiicms", "description": "Zwii - le CMS simple, léger, sans base de données (Flat-File), modulable et responsive !", "url": "https://github.com/YunoHost-Apps/zwiicms_ynh", "stars": 0, "forks": 0, "updated_at": "2025-05-04T16:17:42Z", "language": "Shell", "archived": false}]}