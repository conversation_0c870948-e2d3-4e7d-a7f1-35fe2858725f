# 2.2 Local vs. Remote Solutions

We must acknowledge that local needs are not always best served by local servers. Remote cloud hosting services or partnerships may be more suitable when communities require:

- External-facing services (web radio, e-commerce websites)
- Platforms with primarily external audiences
- High-availability services where local power failures or maintenance issues pose significant risks

Local servers in such cases may introduce latency issues, requiring tunnel connections that slow access for external users.