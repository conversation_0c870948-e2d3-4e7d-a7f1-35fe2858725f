{"guides/mesh-network-basics.md": {"hash": "bdfef7a9563c6f6aaae7d1b79bec062a", "content": "<h1 id=\"mesh-network-basics\">Mesh Network Basics</h1><h2 id=\"introduction-to-mesh-networks\">Introduction to Mesh Networks</h2><p>Mesh networks represent a fundamentally different approach to network architecture compared to traditional hub-and-spoke models. In mesh networking, devices (nodes) connect directly, dynamically, and non-hierarchically to as many other nodes as possible, creating multiple paths for data transmission[13]. This decentralized structure provides several advantages over conventional networks, particularly in community deployments.</p>\n<p><code>[Wireless Channel Map](wireless-channel-map.png) — Example showing proper channel selections to reduce interference.]</code></p>\n<h2 id=\"key-networking-concepts-for-libremesh\">Key Networking Concepts for LibreMesh</h2><h3 id=\"routing-in-mesh-networks\">Routing in Mesh Networks</h3><p>Mesh networks handle routing through peer-to-peer communication where each node acts as both sender and receiver of data[12]. This cooperative approach to data transmission creates resilient networks that can maintain connectivity even when individual nodes fail.</p>\n<h4 id=\"routing-protocols\">Routing Protocols</h4><p>LibreMesh employs two primary routing protocols in its dual-layer architecture[20]:</p>\n<ol>\n<li><p><strong>BATMAN-adv (Better Approach To Mobile Ad hoc Networking)</strong>: A layer 2 protocol that creates a virtual network switch out of mesh nodes, making the entire mesh appear as a single link-local network. This protocol:</p>\n<ul>\n<li>Handles layer 2 routing (MAC addressing)</li>\n<li>Enables seamless roaming within a mesh cloud</li>\n<li>Creates what appears to clients as a single network collision domain</li>\n<li>Works at the data link layer (OSI layer 2)</li>\n</ul>\n</li>\n<li><p><strong>Babel</strong>: A distance-vector routing protocol for IPv6 and IPv4 with fast convergence properties. This protocol:</p>\n<ul>\n<li>Handles layer 3 routing (IP addressing)</li>\n<li>Provides inter-cloud connectivity</li>\n<li>Enables intelligent gateway selection</li>\n<li>Works at the network layer (OSI layer 3)</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"network-topology-concepts\">Network Topology Concepts</h3><p>Mesh networks can implement different topology structures depending on needs[18]:</p>\n<ul>\n<li><strong>Full Mesh</strong>: Every node communicates directly with every other node</li>\n<li><strong>Partial Mesh</strong>: Nodes communicate primarily with nearby nodes, with data &quot;hopping&quot; across multiple nodes to reach distant destinations</li>\n</ul>\n<p>LibreMesh supports both configurations, with partial mesh being more common in larger deployments for efficiency.</p>\n<p><code>[Node Discovery UI Screenshot](node-discovery-ui.png) — LibreMesh&#39;s interface showing known nodes.]</code></p>\n<h3 id=\"automatic-configuration\">Automatic Configuration</h3><p>A critical feature of LibreMesh is its ability to automatically configure network parameters[20]. This includes:</p>\n<ul>\n<li><strong>Node Discovery</strong>: Automatic detection of other mesh nodes within range</li>\n<li><strong>Route Establishment</strong>: Dynamic creation of optimal routing paths</li>\n<li><strong>Address Assignment</strong>: Automatic IP address allocation based on deterministic algorithms</li>\n<li><strong>Gateway Selection</strong>: Intelligent choice of internet exit points</li>\n</ul>\n<p>This auto-configuration significantly reduces the technical expertise required to deploy and maintain mesh networks.</p>\n<h2 id=\"network-architecture-in-libremesh\">Network Architecture in LibreMesh</h2><h3 id=\"dual-layer-design\">Dual-Layer Design</h3><p>The LibreMesh network architecture is built on a two-layer system that provides both flexibility and performance[8][17][20]:</p>\n<h4 id=\"layer-2-cloud\">Layer 2 Cloud</h4><p>The cloud layer uses BATMAN-adv to create mesh &quot;clouds&quot; - local network segments that function as a single collision domain. Key characteristics include:</p>\n<ul>\n<li>All devices within a cloud appear on the same local network</li>\n<li>Seamless roaming for client devices within the cloud</li>\n<li>Isolation of broadcast traffic within cloud boundaries</li>\n<li>Simplified local networking with automatic discovery</li>\n</ul>\n<h4 id=\"layer-3-network\">Layer 3 Network</h4><p>The network layer uses Babel (previously BMX6/BMX7) to interconnect different clouds and handle routing between them. This provides:</p>\n<ul>\n<li>Scalable connectivity between separate mesh clouds</li>\n<li>Intelligent gateway selection for internet access</li>\n<li>Reduced broadcast traffic across the wider network</li>\n<li>IPv6-native routing with IPv4 compatibility</li>\n</ul>\n<h3 id=\"vlan-segmentation\">VLAN Segmentation</h3><p>LibreMesh employs VLAN tagging to isolate routing protocols[17]:</p>\n<ul>\n<li>The Babel VLAN is consistent across all nodes, allowing link-layer connected nodes to find each other</li>\n<li>The BATMAN-adv VLAN depends on a cloud identifier (typically derived from the AP SSID)</li>\n<li>This configuration allows isolating layer 2 clouds while maintaining layer 3 connectivity</li>\n</ul>\n<h2 id=\"wireless-channel-planning\">Wireless Channel Planning</h2><h3 id=\"channel-selection-principles\">Channel Selection Principles</h3><p>Proper channel selection is crucial for wireless mesh performance[34][35]:</p>\n<h4 id=\"24-ghz-considerations\">2.4 GHz Considerations</h4><ul>\n<li>Only channels 1, 6, and 11 are non-overlapping in most regions</li>\n<li>Adjacent channel interference (ACI) occurs when nearby nodes use partially overlapping channels</li>\n<li>Co-channel interference (CCI) happens when nodes in range use the same channel</li>\n</ul>\n<h4 id=\"5-ghz-planning\">5 GHz Planning</h4><ul>\n<li>More available channels allow for better spatial reuse</li>\n<li>Dynamic Frequency Selection (DFS) channels may require radar detection capabilities</li>\n<li>Higher frequencies have reduced range but often less interference</li>\n</ul>\n<h3 id=\"channel-allocation-strategies\">Channel Allocation Strategies</h3><p>For optimal mesh performance, consider these channel planning strategies:</p>\n<ol>\n<li><strong>Channel Separation</strong>: Ensure mesh backhaul links use different channels from client access</li>\n<li><strong>Spatial Reuse</strong>: Plan channel assignments based on physical node placement</li>\n<li><strong>Interference Mitigation</strong>: Select channels with minimal external interference</li>\n<li><strong>Regulatory Compliance</strong>: Adhere to local regulations for frequency use and power levels</li>\n</ol>\n<h2 id=\"fault-tolerance-and-redundancy\">Fault Tolerance and Redundancy</h2><h3 id=\"path-redundancy\">Path Redundancy</h3><p>Mesh networks excel at providing multiple paths for data transmission[18][19]. This redundancy ensures:</p>\n<ul>\n<li>Communication can continue if individual nodes fail</li>\n<li>Traffic can be rerouted around congested or damaged nodes</li>\n<li>Network capacity can scale with additional nodes</li>\n</ul>\n<h3 id=\"self-healing-capabilities\">Self-Healing Capabilities</h3><p>LibreMesh networks automatically adapt to changing conditions[18]:</p>\n<ul>\n<li>Detecting node failures and establishing alternative routes</li>\n<li>Adapting to varying link quality by selecting optimal paths</li>\n<li>Incorporating new nodes into the mesh as they come online</li>\n<li>Balancing traffic across available paths</li>\n</ul>\n<h2 id=\"roaming-in-mesh-networks\">Roaming in Mesh Networks</h2><h3 id=\"layer-2-roaming\">Layer 2 Roaming</h3><p>One significant advantage of LibreMesh&#39;s BATMAN-adv implementation is its support for seamless layer 2 roaming[8][17][20][42]:</p>\n<ul>\n<li>Devices can move between access points without changing IP addresses</li>\n<li>Active connections (TCP sessions, video streams, VoIP calls) remain intact during transitions</li>\n<li>No special client configuration is required for roaming support</li>\n</ul>\n<h3 id=\"roaming-limitations\">Roaming Limitations</h3><p>Roaming in LibreMesh has some constraints[42]:</p>\n<ul>\n<li>Roaming works only within a single BATMAN-adv cloud</li>\n<li>Devices cannot seamlessly roam across different mesh clouds</li>\n<li>Roaming performance depends partly on client device capabilities</li>\n</ul>\n<h2 id=\"performance-considerations\">Performance Considerations</h2><h3 id=\"multi-radio-configurations\">Multi-Radio Configurations</h3><p>For optimal performance, mesh nodes can use multiple radios[7]:</p>\n<ul>\n<li>One radio for mesh backhaul connections (ideally 5 GHz)</li>\n<li>Another radio for client access (typically 2.4 GHz)</li>\n<li>Additional radios for dedicated point-to-point links</li>\n</ul>\n<h3 id=\"network-scalability\">Network Scalability</h3><p>LibreMesh networks can scale through thoughtful design[8]:</p>\n<ul>\n<li>Segmenting larger networks into multiple mesh clouds</li>\n<li>Using directional antennas for long-distance links</li>\n<li>Implementing hierarchical network structures for large deployments</li>\n<li>Careful gateway placement and capacity planning</li>\n</ul>\n<h2 id=\"advanced-mesh-concepts\">Advanced Mesh Concepts</h2><h3 id=\"quality-based-routing\">Quality-Based Routing</h3><p>Modern mesh protocols consider link quality when making routing decisions[16]:</p>\n<ul>\n<li>Measuring packet delivery rates between nodes</li>\n<li>Calculating transmission times across different paths</li>\n<li>Adapting to changing wireless conditions</li>\n<li>Balancing between shortest path and highest quality</li>\n</ul>\n<h3 id=\"network-segmentation\">Network Segmentation</h3><p>LibreMesh supports logical segmentation of networks[8][17]:</p>\n<ul>\n<li>Creating separate mesh clouds for different areas or purposes</li>\n<li>Isolating broadcast domains to improve performance</li>\n<li>Implementing security boundaries between network segments</li>\n<li>Allowing for different policies in different parts of the network</li>\n</ul>\n<h2 id=\"troubleshooting-mesh-connectivity\">Troubleshooting Mesh Connectivity</h2><h3 id=\"common-issues-and-solutions\">Common Issues and Solutions</h3><p>When troubleshooting mesh networks, consider these common scenarios[56]:</p>\n<ol>\n<li><p><strong>Visible mesh network but no internet connectivity</strong></p>\n<ul>\n<li>Check if at least one root access point is connected via wired LAN</li>\n<li>Verify gateway configuration is correct</li>\n<li>Ensure proper routing between mesh and internet gateway</li>\n</ul>\n</li>\n<li><p><strong>Mesh network not visible</strong></p>\n<ul>\n<li>Allow sufficient time (up to 5 minutes) for network initialization</li>\n<li>Verify that mesh access points are on the same channel</li>\n<li>Check if Spanning Tree Protocol is blocking mesh configuration</li>\n</ul>\n</li>\n<li><p><strong>Missing nodes in mesh network</strong></p>\n<ul>\n<li>Ensure all nodes have the proper configuration</li>\n<li>Verify wireless channel compatibility</li>\n<li>Check physical placement and distance between nodes</li>\n</ul>\n</li>\n<li><p><strong>Poor performance</strong></p>\n<ul>\n<li>Look for channel interference</li>\n<li>Check signal strength between nodes</li>\n<li>Verify antenna positioning and orientation</li>\n<li>Monitor for bandwidth-intensive clients or services</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"next-steps\">Next Steps</h2><p>For practical implementation of these concepts:</p>\n<ul>\n<li>Consult <a href=\"#libremesh-overviewmd\" >libremesh-overview.md</a> for a broader understanding of LibreMesh</li>\n<li>Follow <a href=\"#libremesh-installationmd\" >libremesh-installation.md</a> for hardware and installation guidance</li>\n<li>Explore <a href=\"#captive-portal-guidemd\" >captive-portal-guide.md</a> for user management</li>\n<li>Reference <a href=\"#dns-configurationmd\" >dns-configuration.md</a> for local service configuration</li>\n</ul>\n<p>By understanding these fundamental mesh networking concepts, you&#39;ll be better equipped to design, deploy, and maintain an effective LibreMesh network that meets your community&#39;s needs.</p>\n"}, "guides/mesh-clustering-analysis.md": {"hash": "5069808c4c27a79ddd49fc46d58b5c04", "content": "<h1 id=\"mesh-network-clustering-viability-analysis-for-community-networks\">Mesh Network Clustering Viability Analysis for Community Networks</h1><h2 id=\"executive-summary\">Executive Summary</h2><p>This comprehensive analysis evaluates the viability and path forward for clustering computers connected through mesh networks, specifically focusing on LibreMesh deployments for community network scaling. Based on extensive research of real-world deployments including NYC Mesh (2,000+ nodes), Guifi.net (17,000+ nodes), and performance studies, we present a framework for understanding when mesh clustering becomes viable and how to mitigate reliability challenges.</p>\n<p><strong>Key Findings:</strong></p>\n<ul>\n<li>Mesh clustering becomes viable at 4-8 nodes for parallel workloads</li>\n<li>Network performance degrades exponentially beyond 16-32 nodes without optimization</li>\n<li>Fault tolerance requires redundant pathways and distributed consensus protocols</li>\n<li>Community contribution models can scale effectively with proper network segmentation</li>\n</ul>\n<h2 id=\"libremesh-architecture-for-clustering\">LibreMesh Architecture for Clustering</h2><h3 id=\"dual-layer-design\">Dual-Layer Design</h3><p>LibreMesh employs a sophisticated dual-layer architecture optimized for community scaling:</p>\n<p><strong>Layer 2 (Cloud Layer):</strong> BATMAN-ADV protocol creates self-healing mesh clouds with automatic load balancing and seamless roaming capabilities within local neighborhoods.</p>\n<p><strong>Layer 3 (Network Layer):</strong> BMX protocol provides scalable routing across multiple mesh clouds, enabling city-wide connectivity while maintaining local autonomy.</p>\n<p>This design allows clustering workloads to operate within stable Layer 2 clouds while maintaining broader network connectivity through Layer 3 routing.</p>\n<h3 id=\"clustering-performance-characteristics\">Clustering Performance Characteristics</h3><p>Real-world LibreMesh deployments demonstrate specific performance patterns critical for clustering applications:</p>\n<ul>\n<li><strong>Optimal clustering performance:</strong> 4-12 nodes per mesh cloud</li>\n<li><strong>Mean throughput:</strong> 13.6 Mbps in dense deployments (Guifi.net data)</li>\n<li><strong>Latency characteristics:</strong> 2-4 hop average with sub-100ms local latency</li>\n<li><strong>Reliability metrics:</strong> 88-95% uptime in community deployments</li>\n</ul>\n<h2 id=\"scalability-analysis\">Scalability Analysis</h2><h3 id=\"network-size-vs-performance\">Network Size vs Performance</h3><p>Mesh networks exhibit predictable performance degradation patterns as they scale. Our analysis reveals three distinct operational zones:</p>\n<p><strong>Zone 1 (2-8 nodes): Optimal Performance</strong></p>\n<ul>\n<li>Bandwidth efficiency: 85-95%</li>\n<li>Latency multiplier: 1.2-1.8x</li>\n<li>Clustering viability: Excellent for parallel workloads</li>\n</ul>\n<p><strong>Zone 2 (8-32 nodes): Acceptable Performance</strong> </p>\n<ul>\n<li>Bandwidth efficiency: 35-70%</li>\n<li>Latency multiplier: 2.5-6.5x</li>\n<li>Clustering viability: Good for fault-tolerant applications</li>\n</ul>\n<p><strong>Zone 3 (32+ nodes): Degraded Performance</strong></p>\n<ul>\n<li>Bandwidth efficiency: &lt;35%</li>\n<li>Latency multiplier: &gt;6.5x</li>\n<li>Clustering viability: Limited to highly parallel, delay-tolerant workloads</li>\n</ul>\n<h3 id=\"community-contribution-scaling-model\">Community Contribution Scaling Model</h3><p>The community contribution model can effectively scale through hierarchical mesh organization:</p>\n<ol>\n<li><strong>Local Clusters (4-12 nodes):</strong> Neighborhood-level computing resources</li>\n<li><strong>District Networks (50-200 nodes):</strong> Multiple local clusters interconnected</li>\n<li><strong>City-Wide Federation (1000+ nodes):</strong> Multiple district networks with fiber backbone connections</li>\n</ol>\n<h2 id=\"fault-tolerance-and-reliability\">Fault Tolerance and Reliability</h2><h3 id=\"node-failure-impact-analysis\">Node Failure Impact Analysis</h3><p>Mesh networks demonstrate non-linear degradation under node failures:</p>\n<ul>\n<li><strong>0-10% node failures:</strong> Minimal impact due to redundant pathways</li>\n<li><strong>10-20% node failures:</strong> Noticeable performance degradation</li>\n<li><strong>20-30% node failures:</strong> Significant clustering capability loss</li>\n<li><strong>30%+ node failures:</strong> Network fragmentation and potential isolation</li>\n</ul>\n<h3 id=\"failure-mitigation-strategies\">Failure Mitigation Strategies</h3><p><strong>1. Redundant Gateway Connections</strong></p>\n<ul>\n<li>Multiple internet gateways prevent single points of failure</li>\n<li>Automatic failover protocols maintain connectivity during outages</li>\n<li>Load balancing across gateways improves overall resilience</li>\n</ul>\n<p><strong>2. Distributed Consensus Protocols</strong></p>\n<ul>\n<li>Byzantine fault tolerance algorithms handle malicious nodes</li>\n<li>Gossip protocols maintain network state consistency</li>\n<li>Quorum-based decision making prevents split-brain scenarios</li>\n</ul>\n<p><strong>3. Self-Healing Network Topology</strong></p>\n<ul>\n<li>Automatic route discovery and optimization</li>\n<li>Dynamic load balancing based on link quality</li>\n<li>Proactive link monitoring and failure prediction</li>\n</ul>\n<h2 id=\"implementation-roadmap\">Implementation Roadmap</h2><h3 id=\"phase-1-foundation-months-1-6\">Phase 1: Foundation (Months 1-6)</h3><p><strong>Network Infrastructure Setup</strong></p>\n<ul>\n<li>Deploy LibreMesh on community-contributed hardware</li>\n<li>Establish redundant gateway connections</li>\n<li>Implement basic monitoring and management tools</li>\n</ul>\n<p><strong>Initial Clustering Deployment</strong></p>\n<ul>\n<li>Start with 4-8 node clusters for specific applications</li>\n<li>Focus on embarrassingly parallel workloads (data processing, content distribution)</li>\n<li>Establish baseline performance metrics</li>\n</ul>\n<h3 id=\"phase-2-expansion-months-6-18\">Phase 2: Expansion (Months 6-18)</h3><p><strong>Horizontal Scaling</strong></p>\n<ul>\n<li>Expand to multiple neighborhood clusters</li>\n<li>Implement inter-cluster communication protocols</li>\n<li>Deploy advanced load balancing algorithms</li>\n</ul>\n<p><strong>Reliability Enhancements</strong></p>\n<ul>\n<li>Add redundant nodes and pathways</li>\n<li>Implement automated failure detection and recovery</li>\n<li>Deploy distributed consensus mechanisms</li>\n</ul>\n<h3 id=\"phase-3-federation-months-18-36\">Phase 3: Federation (Months 18-36)</h3><p><strong>City-Wide Network Integration</strong></p>\n<ul>\n<li>Connect multiple district networks</li>\n<li>Implement hierarchical routing protocols</li>\n<li>Deploy high-capacity backbone connections</li>\n</ul>\n<p><strong>Advanced Services</strong></p>\n<ul>\n<li>Edge computing services for IoT applications</li>\n<li>Distributed content delivery networks</li>\n<li>Community-owned cloud services</li>\n</ul>\n<h2 id=\"use-cases-and-applications\">Use Cases and Applications</h2><h3 id=\"high-viability-applications\">High-Viability Applications</h3><p><strong>Content Distribution Networks</strong></p>\n<ul>\n<li>Local caching reduces internet bandwidth requirements</li>\n<li>Mesh topology provides natural load distribution</li>\n<li>Community content remains accessible during internet outages</li>\n</ul>\n<p><strong>Distributed Data Processing</strong></p>\n<ul>\n<li>Scientific computing projects benefit from community CPU resources</li>\n<li>Citizen science applications can leverage distributed processing power</li>\n<li>Educational institutions can share computational resources</li>\n</ul>\n<p><strong>IoT and Smart City Services</strong></p>\n<ul>\n<li>Environmental monitoring across neighborhoods</li>\n<li>Traffic management and optimization</li>\n<li>Community safety and emergency response systems</li>\n</ul>\n<h3 id=\"medium-viability-applications\">Medium-Viability Applications</h3><p><strong>Development and Testing Environments</strong></p>\n<ul>\n<li>Distributed software development platforms</li>\n<li>Continuous integration and deployment systems</li>\n<li>Collaborative development tools</li>\n</ul>\n<p><strong>Machine Learning and AI</strong></p>\n<ul>\n<li>Distributed training of community-relevant models</li>\n<li>Edge inference for privacy-sensitive applications</li>\n<li>Federated learning preserving individual privacy</li>\n</ul>\n<h3 id=\"limited-viability-applications\">Limited-Viability Applications</h3><p><strong>Real-Time Gaming and VR</strong></p>\n<ul>\n<li>High latency requirements limit mesh clustering effectiveness</li>\n<li>Better suited for direct connections or traditional infrastructure</li>\n</ul>\n<p><strong>High-Frequency Trading</strong></p>\n<ul>\n<li>Ultra-low latency requirements incompatible with mesh overhead</li>\n<li>Requires dedicated infrastructure</li>\n</ul>\n<h2 id=\"economic-analysis\">Economic Analysis</h2><h3 id=\"cost-effectiveness-thresholds\">Cost-Effectiveness Thresholds</h3><p>Community mesh clustering becomes economically viable when:</p>\n<ul>\n<li><strong>Hardware costs:</strong> &lt;$100 per node for basic clustering</li>\n<li><strong>Internet costs:</strong> Shared gateway expenses &lt;$50/month per neighborhood</li>\n<li><strong>Maintenance:</strong> Volunteer time &lt;10 hours/month per cluster</li>\n</ul>\n<h3 id=\"revenue-and-sustainability-models\">Revenue and Sustainability Models</h3><p><strong>Community Investment Model</strong></p>\n<ul>\n<li>Residents contribute hardware and internet connections</li>\n<li>Shared ownership reduces individual costs</li>\n<li>Volunteer maintenance keeps operational costs low</li>\n</ul>\n<p><strong>Hybrid Commercial Model</strong></p>\n<ul>\n<li>Commercial sponsors provide backbone connectivity</li>\n<li>Premium services generate revenue for network maintenance</li>\n<li>Educational partnerships provide technical expertise</li>\n</ul>\n<p><strong>Cooperative Model</strong></p>\n<ul>\n<li>Formal cooperative structure for resource sharing</li>\n<li>Member fees fund network expansion and maintenance</li>\n<li>Democratic governance ensures community control</li>\n</ul>\n<h2 id=\"technical-challenges-and-solutions\">Technical Challenges and Solutions</h2><h3 id=\"latency-management\">Latency Management</h3><p><strong>Challenge:</strong> Multi-hop latency impacts real-time applications<br><strong>Solutions:</strong></p>\n<ul>\n<li>Intelligent workload placement near data sources</li>\n<li>Edge computing to minimize hop counts</li>\n<li>Predictive caching for commonly accessed resources</li>\n</ul>\n<h3 id=\"security-and-privacy\">Security and Privacy</h3><p><strong>Challenge:</strong> Open networks create security vulnerabilities<br><strong>Solutions:</strong></p>\n<ul>\n<li>End-to-end encryption for all cluster communications</li>\n<li>Identity management through distributed systems</li>\n<li>Regular security audits and vulnerability assessments</li>\n</ul>\n<h3 id=\"quality-of-service\">Quality of Service</h3><p><strong>Challenge:</strong> Ensuring consistent performance for critical applications<br><strong>Solutions:</strong></p>\n<ul>\n<li>Traffic prioritization protocols</li>\n<li>Bandwidth reservation mechanisms</li>\n<li>Service level agreements between community members</li>\n</ul>\n<h2 id=\"regulatory-and-legal-considerations\">Regulatory and Legal Considerations</h2><h3 id=\"spectrum-management\">Spectrum Management</h3><ul>\n<li>Ensure compliance with local unlicensed spectrum regulations</li>\n<li>Coordinate with other wireless users to minimize interference</li>\n<li>Plan for future spectrum policy changes</li>\n</ul>\n<h3 id=\"data-protection\">Data Protection</h3><ul>\n<li>Implement GDPR-compliant data handling procedures</li>\n<li>Establish clear privacy policies for community members</li>\n<li>Regular legal compliance audits</li>\n</ul>\n<h3 id=\"network-neutrality\">Network Neutrality</h3><ul>\n<li>Maintain open access principles</li>\n<li>Avoid prioritizing commercial traffic over community use</li>\n<li>Transparent governance of network policies</li>\n</ul>\n<h2 id=\"performance-monitoring-and-optimization\">Performance Monitoring and Optimization</h2><h3 id=\"key-performance-indicators\">Key Performance Indicators</h3><p><strong>Network Performance Metrics</strong></p>\n<ul>\n<li>Throughput and latency measurements</li>\n<li>Packet loss and jitter statistics</li>\n<li>Link quality and stability metrics</li>\n</ul>\n<p><strong>Clustering Performance Metrics</strong></p>\n<ul>\n<li>Job completion times and success rates</li>\n<li>Resource utilization across nodes</li>\n<li>Load balancing effectiveness</li>\n</ul>\n<p><strong>Community Engagement Metrics</strong></p>\n<ul>\n<li>Number of active contributing nodes</li>\n<li>Volunteer participation in maintenance</li>\n<li>Community satisfaction and feedback</li>\n</ul>\n<h3 id=\"optimization-strategies\">Optimization Strategies</h3><p><strong>Dynamic Load Balancing</strong></p>\n<ul>\n<li>Real-time workload distribution based on node capacity</li>\n<li>Predictive algorithms for resource allocation</li>\n<li>Automatic scaling based on demand patterns</li>\n</ul>\n<p><strong>Network Topology Optimization</strong></p>\n<ul>\n<li>Regular analysis of network connectivity patterns</li>\n<li>Strategic placement of new nodes to improve coverage</li>\n<li>Removal or relocation of underperforming nodes</li>\n</ul>\n<h2 id=\"conclusion-and-recommendations\">Conclusion and Recommendations</h2><p>Mesh network clustering through LibreMesh represents a viable path forward for community networks, with specific deployment strategies required for success:</p>\n<h3 id=\"immediate-actions-next-6-months\">Immediate Actions (Next 6 Months)</h3><ol>\n<li><strong>Start Small:</strong> Deploy 4-8 node clusters in high-interest neighborhoods</li>\n<li><strong>Choose Appropriate Workloads:</strong> Focus on delay-tolerant, parallel applications</li>\n<li><strong>Establish Governance:</strong> Create community decision-making processes</li>\n<li><strong>Build Technical Capacity:</strong> Train local volunteers in network management</li>\n</ol>\n<h3 id=\"medium-term-goals-6-18-months\">Medium-Term Goals (6-18 Months)</h3><ol>\n<li><strong>Expand Strategically:</strong> Connect successful clusters into district networks</li>\n<li><strong>Improve Reliability:</strong> Implement redundancy and failover mechanisms</li>\n<li><strong>Develop Services:</strong> Create value-added applications for community members</li>\n<li><strong>Document and Share:</strong> Publish deployment guides and lessons learned</li>\n</ol>\n<h3 id=\"long-term-vision-18-months\">Long-Term Vision (18+ Months)</h3><ol>\n<li><strong>Scale Sustainably:</strong> Build city-wide federation of community networks</li>\n<li><strong>Innovate Continuously:</strong> Develop new applications and services</li>\n<li><strong>Advocate for Policy:</strong> Work with regulators to support community networking</li>\n<li><strong>Replicate Globally:</strong> Share successful models with other communities</li>\n</ol>\n<p>The success of mesh network clustering depends not just on technical implementation but on community engagement, sustainable governance, and adaptive management of both technical and social challenges. With proper planning and execution, mesh clustering can provide a foundation for community-controlled digital infrastructure that scales with local needs and resources.</p>\n"}, "guides/hardware-clustering-analysis.md": {"hash": "cc9f88381d58c90029c196c18e760433", "content": "<h1 id=\"hardware-clustering-analysis-pi-vs-zimaboard-vs-nuc\">Hardware Clustering Analysis: Pi vs ZimaBoard vs NUC</h1><h2 id=\"executive-summary\">Executive Summary</h2><p>This comprehensive analysis compares clustering solutions using Raspberry Pi, ZimaBoard, and Intel NUC hardware platforms to determine when clustering becomes cost-effective versus single powerful systems. Based on performance benchmarks, cost analysis, and power consumption data, we&#39;ve identified specific scenarios where clustering provides value.</p>\n<p><strong>Key Findings:</strong></p>\n<ul>\n<li><strong>Raspberry Pi 5 clusters</strong> offer the best cost-effectiveness for parallel workloads at 2-8 nodes</li>\n<li><strong>ZimaBoard</strong> provides excellent x86 compatibility but limited clustering cost advantages</li>\n<li><strong>Intel NUC clusters</strong> become viable for high-performance computing at 8+ nodes</li>\n<li><strong>Single powerful systems</strong> are generally more cost-effective for non-parallel workloads</li>\n</ul>\n<h2 id=\"hardware-platform-comparison\">Hardware Platform Comparison</h2><h3 id=\"performance-and-cost-overview\">Performance and Cost Overview</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Platform</th>\n<th>CPU Performance*</th>\n<th>RAM</th>\n<th>Price</th>\n<th>Cost/Performance</th>\n<th>Power (TDP)</th>\n<th>Architecture</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Raspberry Pi 4</td>\n<td>1.0×</td>\n<td>1-8GB</td>\n<td>$35-75</td>\n<td>Excellent</td>\n<td>4W</td>\n<td>ARM</td>\n</tr>\n<tr>\n<td>Raspberry Pi 5</td>\n<td>2.5×</td>\n<td>4-8GB</td>\n<td>$60-80</td>\n<td>Outstanding</td>\n<td>5W</td>\n<td>ARM</td>\n</tr>\n<tr>\n<td>ZimaBoard 232</td>\n<td>2.0×</td>\n<td>2GB</td>\n<td>$120</td>\n<td>Good</td>\n<td>6W</td>\n<td>x86</td>\n</tr>\n<tr>\n<td>ZimaBoard 832</td>\n<td>3.0×</td>\n<td>8GB</td>\n<td>$200</td>\n<td>Good</td>\n<td>6W</td>\n<td>x86</td>\n</tr>\n<tr>\n<td>Intel NUC (Celeron)</td>\n<td>5.0×</td>\n<td>8GB+</td>\n<td>$300-500</td>\n<td>Fair</td>\n<td>10W</td>\n<td>x86</td>\n</tr>\n<tr>\n<td>Intel NUC (i5)</td>\n<td>10.0×</td>\n<td>16GB+</td>\n<td>$700-1200</td>\n<td>Poor</td>\n<td>15-28W</td>\n<td>x86</td>\n</tr>\n</tbody>\n      </table><p>*Relative to Raspberry Pi 4 baseline</p>\n<h2 id=\"when-clustering-makes-sense\">When Clustering Makes Sense</h2><h3 id=\"cost-effectiveness-threshold-analysis\">Cost-Effectiveness Threshold Analysis</h3><p>Based on our analysis, clustering becomes cost-effective when:</p>\n<ol>\n<li><strong>Raspberry Pi 5</strong>: 2-4 nodes optimal (Performance Ratio: 1.36-1.40×)</li>\n<li><strong>Intel NUC (i5)</strong>: 8-16 nodes for HPC workloads (Performance Ratio: 1.18×)</li>\n<li><strong>All other platforms</strong>: Generally favor single systems for cost efficiency</li>\n</ol>\n<h3 id=\"use-case-decision-matrix\">Use Case Decision Matrix</h3><h4 id=\"educationallearning-100-500\">Educational/Learning ($100-500)</h4><ul>\n<li><strong>Best Choice</strong>: 2-4× Raspberry Pi 4/5 cluster</li>\n<li><strong>Advantages</strong>: Low cost, excellent learning platform, easy management</li>\n<li><strong>Limitations</strong>: Limited performance, ARM architecture constraints</li>\n</ul>\n<h4 id=\"home-labdevelopment-300-1000\">Home Lab/Development ($300-1000)</h4><ul>\n<li><strong>Best Choice</strong>: 4-8× Raspberry Pi 5 or 4× ZimaBoard cluster</li>\n<li><strong>Advantages</strong>: Good performance/cost ratio, x86 compatibility, power efficient</li>\n<li><strong>Limitations</strong>: Moderate complexity, network overhead</li>\n</ul>\n<h4 id=\"production-edge-computing-1000-3000\">Production Edge Computing ($1000-3000)</h4><ul>\n<li><strong>Best Choice</strong>: 4-8× ZimaBoard or 4× Intel NUC cluster</li>\n<li><strong>Advantages</strong>: Industrial reliability, high availability, redundancy</li>\n<li><strong>Limitations</strong>: Higher cost, complex management</li>\n</ul>\n<h4 id=\"high-performance-computing-2000\">High Performance Computing ($2000+)</h4><ul>\n<li><strong>Best Choice</strong>: 8-16× Intel NUC cluster</li>\n<li><strong>Advantages</strong>: High computational power, professional support</li>\n<li><strong>Limitations</strong>: High cost, high power consumption</li>\n</ul>\n<h2 id=\"platform-specific-analysis\">Platform-Specific Analysis</h2><h3 id=\"raspberry-pi-clusters\">Raspberry Pi Clusters</h3><p><strong>Strengths:</strong></p>\n<ul>\n<li>Lowest entry cost</li>\n<li>Excellent community support</li>\n<li>Great for learning distributed computing</li>\n<li>Low power consumption</li>\n<li>Modular scaling</li>\n</ul>\n<p><strong>Weaknesses:</strong></p>\n<ul>\n<li>ARM architecture limits software compatibility</li>\n<li>Limited I/O performance</li>\n<li>SD card reliability concerns</li>\n<li>Network bottlenecks at scale</li>\n</ul>\n<p><strong>Sweet Spot</strong>: 2-4 node clusters for educational and development use</p>\n<h3 id=\"zimaboard-clusters\">ZimaBoard Clusters</h3><p><strong>Strengths:</strong></p>\n<ul>\n<li>x86 architecture compatibility</li>\n<li>Dual Gigabit Ethernet</li>\n<li>Built-in eMMC storage</li>\n<li>Industrial design</li>\n<li>PCIe expansion</li>\n</ul>\n<p><strong>Weaknesses:</strong></p>\n<ul>\n<li>Higher cost per node</li>\n<li>Limited clustering software ecosystem</li>\n<li>Thermal challenges in dense setups</li>\n<li>No significant cost advantage over NUCs</li>\n</ul>\n<p><strong>Sweet Spot</strong>: 2-4 node clusters for edge computing and NAS applications</p>\n<h3 id=\"intel-nuc-clusters\">Intel NUC Clusters</h3><p><strong>Strengths:</strong></p>\n<ul>\n<li>Highest single-thread performance</li>\n<li>Enterprise-grade components</li>\n<li>Excellent expansion options</li>\n<li>Professional support available</li>\n<li>Full x86 compatibility</li>\n</ul>\n<p><strong>Weaknesses:</strong></p>\n<ul>\n<li>High initial investment</li>\n<li>Significant power consumption</li>\n<li>Complex cooling requirements</li>\n<li>Diminishing returns below 8 nodes</li>\n</ul>\n<p><strong>Sweet Spot</strong>: 8+ node clusters for production workloads and HPC</p>\n<h2 id=\"power-consumption-analysis\">Power Consumption Analysis</h2><h3 id=\"power-scaling-by-platform\">Power Scaling by Platform</h3><ul>\n<li><strong>Raspberry Pi 4</strong>: 5W → 85W (1-16 nodes)</li>\n<li><strong>Raspberry Pi 5</strong>: 7W → 117W (1-16 nodes)</li>\n<li><strong>ZimaBoard 832</strong>: 8W → 133W (1-16 nodes)</li>\n<li><strong>Intel NUC (Celeron)</strong>: 12W → 197W (1-16 nodes)</li>\n<li><strong>Intel NUC (i5)</strong>: 25W → 405W (1-16 nodes)</li>\n</ul>\n<p><strong>Key Insight</strong>: Raspberry Pi maintains the best power efficiency across all cluster sizes, while Intel NUC clusters require significant power infrastructure.</p>\n<h2 id=\"workload-suitability-matrix\">Workload Suitability Matrix</h2><table class=\"pdf-table\">\n        <thead><tr>\n<th>Workload Type</th>\n<th>Pi 4 Cluster</th>\n<th>Pi 5 Cluster</th>\n<th>ZimaBoard</th>\n<th>NUC Cluster</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Web Services/APIs</td>\n<td>Limited</td>\n<td>Good</td>\n<td>Excellent</td>\n<td>Excellent</td>\n</tr>\n<tr>\n<td>Database Clusters</td>\n<td>Not Suitable</td>\n<td>Limited</td>\n<td>Good</td>\n<td>Excellent</td>\n</tr>\n<tr>\n<td>Machine Learning</td>\n<td>Not Suitable</td>\n<td>Basic</td>\n<td>Good</td>\n<td>Excellent</td>\n</tr>\n<tr>\n<td>CI/CD Pipelines</td>\n<td>Basic</td>\n<td>Good</td>\n<td>Excellent</td>\n<td>Excellent</td>\n</tr>\n<tr>\n<td>Media Processing</td>\n<td>Limited</td>\n<td>Good</td>\n<td>Excellent</td>\n<td>Excellent</td>\n</tr>\n<tr>\n<td>IoT Data Processing</td>\n<td>Good</td>\n<td>Excellent</td>\n<td>Excellent</td>\n<td>Good</td>\n</tr>\n<tr>\n<td>Container Orchestration</td>\n<td>Limited</td>\n<td>Good</td>\n<td>Excellent</td>\n<td>Excellent</td>\n</tr>\n<tr>\n<td>Development/Testing</td>\n<td>Excellent</td>\n<td>Excellent</td>\n<td>Good</td>\n<td>Good</td>\n</tr>\n</tbody>\n      </table><h2 id=\"clustering-vs-single-system-recommendations\">Clustering vs Single System Recommendations</h2><h3 id=\"choose-clustering-when\">Choose Clustering When:</h3><ol>\n<li><strong>Workload is highly parallel</strong> (embarrassingly parallel problems)</li>\n<li><strong>High availability is required</strong> (redundancy needs)</li>\n<li><strong>Learning distributed computing</strong> (educational value)</li>\n<li><strong>Incremental scaling needed</strong> (start small, grow gradually)</li>\n<li><strong>Power efficiency is critical</strong> (edge deployments)</li>\n<li><strong>Budget constraints exist</strong> (can&#39;t afford single powerful system)</li>\n</ol>\n<h3 id=\"choose-single-system-when\">Choose Single System When:</h3><ol>\n<li><strong>Workload is single-threaded</strong> (sequential processing)</li>\n<li><strong>Simplicity is prioritized</strong> (easier management)</li>\n<li><strong>Performance per watt matters</strong> (compute density)</li>\n<li><strong>Professional support needed</strong> (enterprise requirements)</li>\n<li><strong>Storage performance critical</strong> (database workloads)</li>\n</ol>\n<h2 id=\"economic-break-even-points\">Economic Break-Even Points</h2><h3 id=\"budget-based-recommendations\">Budget-Based Recommendations</h3><p><strong>$300 Budget:</strong></p>\n<ul>\n<li>Single: Entry-level NUC or high-end Pi 5</li>\n<li>Cluster: 4× Pi 4 cluster (learning focus)</li>\n</ul>\n<p><strong>$500 Budget:</strong></p>\n<ul>\n<li>Single: Mid-range NUC</li>\n<li>Cluster: 4× Pi 5 cluster (<strong>best parallel performance/cost</strong>)</li>\n</ul>\n<p><strong>$1000 Budget:</strong></p>\n<ul>\n<li>Single: High-end NUC</li>\n<li>Cluster: 4× ZimaBoard or 8× Pi 5 cluster</li>\n</ul>\n<p><strong>$2000+ Budget:</strong></p>\n<ul>\n<li>Single: Entry workstation</li>\n<li>Cluster: 8× ZimaBoard or 4× NUC cluster</li>\n</ul>\n<h2 id=\"practical-considerations\">Practical Considerations</h2><h3 id=\"network-infrastructure-requirements\">Network Infrastructure Requirements</h3><ul>\n<li><strong>2-4 nodes</strong>: Standard gigabit switch ($30-50)</li>\n<li><strong>8 nodes</strong>: Managed switch with PoE+ ($100-150)</li>\n<li><strong>16+ nodes</strong>: Enterprise switch with monitoring ($200+)</li>\n</ul>\n<h3 id=\"cooling-and-power\">Cooling and Power</h3><ul>\n<li><strong>Pi clusters</strong>: Passive cooling sufficient up to 8 nodes</li>\n<li><strong>ZimaBoard</strong>: Requires active cooling at 4+ nodes</li>\n<li><strong>NUC clusters</strong>: Dedicated cooling solution and UPS recommended</li>\n</ul>\n<h3 id=\"management-complexity\">Management Complexity</h3><ul>\n<li><strong>Learning curve</strong>: Pi &lt; ZimaBoard &lt; NUC</li>\n<li><strong>Monitoring tools</strong>: Prometheus/Grafana recommended for 4+ nodes</li>\n<li><strong>Orchestration</strong>: Kubernetes viable at 8+ nodes</li>\n</ul>\n<h2 id=\"conclusion\">Conclusion</h2><p>Clustering becomes economically viable in specific scenarios:</p>\n<ol>\n<li><strong>Educational use</strong>: Pi 4/5 clusters excel for learning at low cost</li>\n<li><strong>Parallel workloads</strong>: Pi 5 clusters offer best price/performance at 2-4 nodes</li>\n<li><strong>High availability</strong>: ZimaBoard clusters provide x86 compatibility with redundancy</li>\n<li><strong>HPC applications</strong>: NUC clusters scale effectively at 8+ nodes</li>\n</ol>\n<p>For most production workloads requiring single-threaded performance, a single powerful system remains more cost-effective than clustering. However, for applications requiring high availability, parallel processing, or incremental scaling, clustering provides clear advantages beyond raw performance metrics.</p>\n<p>The decision should be based on specific workload characteristics, budget constraints, and operational requirements rather than purely on cost-per-performance calculations.</p>\n"}, "guides/hardware-compatibility-check.md": {"hash": "c6729fb188c545fa4c0cb57f08f22401", "content": "<h1 id=\"hardware-compatibility-check-guide-for-recycled-servers\">Hardware Compatibility Check Guide for Recycled Servers</h1><h2 id=\"overview\">Overview</h2><p>This guide provides comprehensive procedures for verifying and testing component compatibility in recycled computers before deploying them as community servers. Proper compatibility testing prevents deployment issues and ensures stable operation.</p>\n<h2 id=\"pre-testing-preparation\">Pre-Testing Preparation</h2><h3 id=\"initial-assessment-checklist\">Initial Assessment Checklist</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Document all visible hardware components</li>\n<li><input disabled=\"\" type=\"checkbox\"> Take photos of current configuration</li>\n<li><input disabled=\"\" type=\"checkbox\"> Record model numbers and serial numbers</li>\n<li><input disabled=\"\" type=\"checkbox\"> Check for obvious physical damage</li>\n<li><input disabled=\"\" type=\"checkbox\"> Verify all cables and connections are present</li>\n<li><input disabled=\"\" type=\"checkbox\"> Ensure adequate workspace and tools available</li>\n</ul>\n<h3 id=\"required-tools-and-software\">Required Tools and Software</h3><ul>\n<li><p><strong>Hardware Tools</strong>:</p>\n<ul>\n<li>Phillips head screwdriver set</li>\n<li>Anti-static wrist strap</li>\n<li>Digital multimeter</li>\n<li>Compressed air canister</li>\n<li>Thermal paste (if needed)</li>\n<li>Spare known-good components for testing</li>\n</ul>\n</li>\n<li><p><strong>Software Tools</strong>:</p>\n<ul>\n<li>Bootable USB with diagnostic tools</li>\n<li>CPU-Z for component identification</li>\n<li>MemTest86+ for RAM testing</li>\n<li>CrystalDiskInfo for storage health</li>\n<li>Prime95 or stress testing software</li>\n<li>Hardware vendor diagnostic utilities</li>\n</ul>\n</li>\n</ul>\n<h2 id=\"cpu-compatibility-verification\">CPU Compatibility Verification</h2><h3 id=\"socket-and-chipset-compatibility\">Socket and Chipset Compatibility</h3><ol>\n<li><p><strong>Identify Current CPU</strong>:</p>\n<ul>\n<li>Use CPU-Z to identify exact model</li>\n<li>Check socket type (LGA1151, AM4, etc.)</li>\n<li>Verify chipset compatibility</li>\n</ul>\n</li>\n<li><p><strong>Check Upgrade Options</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Search Format: &quot;[Motherboard Model] CPU compatibility list&quot;\nExample: &quot;Dell OptiPlex 7050 supported CPU list&quot;</code></pre></li>\n<li><p><strong>BIOS Support Verification</strong>:</p>\n<ul>\n<li>Check current BIOS version</li>\n<li>Verify if CPU requires BIOS update</li>\n<li>Download latest BIOS from manufacturer</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"cpu-testing-procedure\">CPU Testing Procedure</h3><pre class=\"code-block\" data-language=\"bash\"><code># Basic CPU Information\nlscpu                    # Linux command\nwmic cpu get name        # Windows command\n\n# Stress Testing\nprime95                  # Run for 30 minutes minimum\nstress -c 4 -t 1800     # Linux stress test</code></pre><h3 id=\"performance-validation\">Performance Validation</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> All cores detected correctly</li>\n<li><input disabled=\"\" type=\"checkbox\"> Operating frequencies within specification</li>\n<li><input disabled=\"\" type=\"checkbox\"> Temperature stays below 80°C under load</li>\n<li><input disabled=\"\" type=\"checkbox\"> No thermal throttling detected</li>\n<li><input disabled=\"\" type=\"checkbox\"> Virtualization features enabled (if required)</li>\n</ul>\n<h2 id=\"memory-ram-compatibility-testing\">Memory (RAM) Compatibility Testing</h2><h3 id=\"memory-specification-verification\">Memory Specification Verification</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Factor</th>\n<th>Check Method</th>\n<th>Requirements</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Type</td>\n<td>Physical inspection/CPU-Z</td>\n<td>DDR3/DDR4 match motherboard</td>\n</tr>\n<tr>\n<td>Speed</td>\n<td>BIOS/UEFI settings</td>\n<td>Supported by both CPU and motherboard</td>\n</tr>\n<tr>\n<td>Capacity</td>\n<td>System Properties</td>\n<td>Within motherboard maximum</td>\n</tr>\n<tr>\n<td>Timing</td>\n<td>Memory stress testing</td>\n<td>Stable at rated speeds</td>\n</tr>\n<tr>\n<td>ECC</td>\n<td>Hardware documentation</td>\n<td>Match motherboard ECC support</td>\n</tr>\n</tbody>\n      </table><h3 id=\"memory-testing-protocol\">Memory Testing Protocol</h3><h4 id=\"step-1-basic-recognition-test\">Step 1: Basic Recognition Test</h4><pre class=\"code-block\" data-language=\"bash\"><code># Linux Memory Detection\nfree -h\ncat /proc/meminfo\ndmidecode --type memory\n\n# Windows Memory Detection\nwmic memorychip get capacity,speed,memorytype</code></pre><h4 id=\"step-2-comprehensive-memory-test\">Step 2: Comprehensive Memory Test</h4><ol>\n<li><strong>Boot MemTest86+</strong> from USB drive</li>\n<li><strong>Run complete test cycles</strong> (minimum 2 passes)</li>\n<li><strong>Test each stick individually</strong> if errors occur</li>\n<li><strong>Test different slot combinations</strong></li>\n</ol>\n<h4 id=\"step-3-operating-system-memory-test\">Step 3: Operating System Memory Test</h4><pre class=\"code-block\" data-language=\"bash\"><code># Windows Memory Diagnostic\nmdsched.exe\n\n# Linux Memory Test\nmemtester 1024M 5    # Test 1GB, 5 iterations</code></pre><h3 id=\"memory-compatibility-matrix\">Memory Compatibility Matrix</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Motherboard Generation</th>\n<th>Supported Types</th>\n<th>Max Capacity</th>\n<th>Speeds</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Intel 6th-8th Gen</td>\n<td>DDR4</td>\n<td>32-64 GB</td>\n<td>2133-2666 MHz</td>\n</tr>\n<tr>\n<td>Intel 4th-5th Gen</td>\n<td>DDR3/DDR3L</td>\n<td>16-32 GB</td>\n<td>1333-1600 MHz</td>\n</tr>\n<tr>\n<td>AMD AM4</td>\n<td>DDR4</td>\n<td>64-128 GB</td>\n<td>2133-3200 MHz</td>\n</tr>\n<tr>\n<td>AMD FM2+</td>\n<td>DDR3</td>\n<td>32 GB</td>\n<td>1333-2133 MHz</td>\n</tr>\n</tbody>\n      </table><h2 id=\"storage-compatibility-assessment\">Storage Compatibility Assessment</h2><h3 id=\"interface-compatibility-check\">Interface Compatibility Check</h3><h4 id=\"sata-compatibility\">SATA Compatibility</h4><pre class=\"code-block\" data-language=\"bash\"><code># Check SATA mode (AHCI/IDE)\ndmesg | grep -i sata\nlspci | grep -i sata\n\n# Windows SATA information\ndevmgmt.msc (Device Manager &gt; IDE ATA/ATAPI controllers)</code></pre><h4 id=\"nvme-compatibility\">NVMe Compatibility</h4><ul>\n<li><strong>Requirements</strong>: PCIe 3.0+ M.2 slot with NVMe support</li>\n<li><strong>BIOS Setting</strong>: NVMe mode enabled</li>\n<li><strong>Boot Support</strong>: UEFI firmware with NVMe boot capability</li>\n</ul>\n<h3 id=\"storage-testing-procedures\">Storage Testing Procedures</h3><h4 id=\"health-assessment\">Health Assessment</h4><ol>\n<li><p><strong>Check Drive Health</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Linux\nsmartctl -a /dev/sda\nhdparm -I /dev/sda\n\n# Windows\nwmic diskdrive get status\nchkdsk C: /f</code></pre></li>\n<li><p><strong>Performance Testing</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Sequential Read/Write Test\ndd if=/dev/zero of=/tmp/testfile bs=1G count=1 oflag=direct\ndd if=/tmp/testfile of=/dev/null bs=1G count=1 iflag=direct\n\n# Windows (using CrystalDiskMark)\ncrystaldiskmark.exe</code></pre></li>\n</ol>\n<h3 id=\"storage-compatibility-matrix\">Storage Compatibility Matrix</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Interface</th>\n<th>Speed</th>\n<th>Hot-Swap</th>\n<th>Boot Support</th>\n<th>Recommended Use</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>SATA III</td>\n<td>6 Gbps</td>\n<td>Yes*</td>\n<td>Yes</td>\n<td>Primary storage</td>\n</tr>\n<tr>\n<td>SATA II</td>\n<td>3 Gbps</td>\n<td>Yes*</td>\n<td>Yes</td>\n<td>Secondary storage</td>\n</tr>\n<tr>\n<td>NVMe</td>\n<td>32 Gbps</td>\n<td>No</td>\n<td>Yes**</td>\n<td>Boot/system drive</td>\n</tr>\n<tr>\n<td>USB 3.0</td>\n<td>5 Gbps</td>\n<td>Yes</td>\n<td>Limited</td>\n<td>External/backup</td>\n</tr>\n</tbody>\n      </table><p>*Requires hot-swap capable chassis<br>**Requires UEFI firmware</p>\n<h2 id=\"network-component-testing\">Network Component Testing</h2><h3 id=\"ethernet-controller-verification\">Ethernet Controller Verification</h3><pre class=\"code-block\" data-language=\"bash\"><code># Linux Network Interface Check\nip link show\nethtool eth0\nlspci | grep -i ethernet\n\n# Windows Network Adapter Check\nipconfig /all\ndevmgmt.msc (Network adapters)</code></pre><h3 id=\"network-performance-testing\">Network Performance Testing</h3><ol>\n<li><p><strong>Link Speed Verification</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Check negotiated speed\nethtool eth0 | grep Speed\ncat /sys/class/net/eth0/speed</code></pre></li>\n<li><p><strong>Throughput Testing</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># iperf3 network performance test\niperf3 -s                    # Server mode\niperf3 -c [server_ip]        # Client mode</code></pre></li>\n</ol>\n<h3 id=\"wireless-compatibility-if-applicable\">Wireless Compatibility (if applicable)</h3><ul>\n<li><strong>Driver Availability</strong>: Check Linux/Windows driver support</li>\n<li><strong>Standard Support</strong>: 802.11n minimum (802.11ac preferred)</li>\n<li><strong>Enterprise Features</strong>: WPA2-Enterprise, 802.1X support</li>\n</ul>\n<h2 id=\"power-supply-compatibility\">Power Supply Compatibility</h2><h3 id=\"power-requirements-calculation\">Power Requirements Calculation</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Component</th>\n<th>Typical Power Draw</th>\n<th>High-End Power Draw</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Intel i5 CPU</td>\n<td>65W</td>\n<td>95W</td>\n</tr>\n<tr>\n<td>Intel i7 CPU</td>\n<td>65W</td>\n<td>125W</td>\n</tr>\n<tr>\n<td>DDR4 RAM (8GB)</td>\n<td>3W</td>\n<td>5W</td>\n</tr>\n<tr>\n<td>2.5&quot; SSD</td>\n<td>2W</td>\n<td>3W</td>\n</tr>\n<tr>\n<td>3.5&quot; HDD</td>\n<td>6W</td>\n<td>10W</td>\n</tr>\n<tr>\n<td>Motherboard</td>\n<td>25W</td>\n<td>35W</td>\n</tr>\n<tr>\n<td>Network Card</td>\n<td>5W</td>\n<td>10W</td>\n</tr>\n</tbody>\n      </table><h3 id=\"psu-testing-protocol\">PSU Testing Protocol</h3><ol>\n<li><p><strong>Voltage Testing</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Check PSU voltages under load\nsensors            # Linux hardware monitoring\n+3.3V: 3.14V - 3.47V (±5%)\n+5V:   4.75V - 5.25V (±5%)\n+12V:  11.40V - 12.60V (±5%)</code></pre></li>\n<li><p><strong>Load Testing</strong>:</p>\n<ul>\n<li>Run stress test on CPU and GPU simultaneously</li>\n<li>Monitor voltages throughout test</li>\n<li>Check for system stability under maximum load</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"psu-compatibility-requirements\">PSU Compatibility Requirements</h3><ul>\n<li><strong>Wattage</strong>: 150% of calculated maximum power draw</li>\n<li><strong>Efficiency</strong>: 80 PLUS Bronze minimum</li>\n<li><strong>Connectors</strong>: Match motherboard and storage requirements</li>\n<li><strong>Form Factor</strong>: ATX/SFX compatibility with chassis</li>\n</ul>\n<h2 id=\"expansion-card-compatibility\">Expansion Card Compatibility</h2><h3 id=\"pcie-slot-verification\">PCIe Slot Verification</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check available PCIe slots\nlspci -vv\nlspci | grep -i pci\n\n# Check PCIe versions and lanes\nlspci -vvv | grep LnkCap</code></pre><h3 id=\"common-expansion-cards-testing\">Common Expansion Cards Testing</h3><h4 id=\"network-interface-cards\">Network Interface Cards</h4><ul>\n<li><strong>Driver Compatibility</strong>: Operating system support</li>\n<li><strong>Performance</strong>: Gigabit minimum throughput</li>\n<li><strong>Features</strong>: VLAN, jumbo frame support</li>\n</ul>\n<h4 id=\"storage-controllers\">Storage Controllers</h4><ul>\n<li><strong>RAID Support</strong>: Hardware vs. software RAID</li>\n<li><strong>Drive Compatibility</strong>: SATA/SAS support matrix</li>\n<li><strong>Performance</strong>: IOPs and throughput benchmarks</li>\n</ul>\n<h2 id=\"thermal-management-verification\">Thermal Management Verification</h2><h3 id=\"cooling-system-assessment\">Cooling System Assessment</h3><ol>\n<li><p><strong>Fan Operation Check</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Monitor fan speeds\nsensors\npwmconfig          # Configure PWM fan control</code></pre></li>\n<li><p><strong>Temperature Monitoring</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Continuous temperature monitoring\nwatch sensors\nstress --cpu 4 --timeout 600s  # 10-minute stress test</code></pre></li>\n</ol>\n<h3 id=\"thermal-testing-protocol\">Thermal Testing Protocol</h3><ul>\n<li><strong>Idle Temperatures</strong>: &lt; 40°C for CPU</li>\n<li><strong>Load Temperatures</strong>: &lt; 80°C for CPU under stress</li>\n<li><strong>Fan Response</strong>: Automatic speed adjustment based on temperature</li>\n<li><strong>Thermal Throttling</strong>: No throttling under normal server loads</li>\n</ul>\n<h2 id=\"compatibility-testing-automation\">Compatibility Testing Automation</h2><h3 id=\"hardware-detection-script\">Hardware Detection Script</h3><pre class=\"code-block\" data-language=\"bash\"><code>#!/bin/bash\n# Automated hardware compatibility check\n\necho &quot;=== Hardware Compatibility Check ===&quot;\necho &quot;Date: $(date)&quot;\necho\n\necho &quot;=== CPU Information ===&quot;\nlscpu | grep -E &quot;(Model name|Architecture|CPU MHz|Core|Thread)&quot;\n\necho &quot;=== Memory Information ===&quot;\nfree -h\ndmidecode --type memory | grep -E &quot;(Size|Speed|Type):&quot;\n\necho &quot;=== Storage Information ===&quot;\nlsblk -o NAME,SIZE,TYPE,MOUNTPOINT\ndf -h\n\necho &quot;=== Network Information ===&quot;\nip link show\nethtool eth0 2&gt;/dev/null | grep -E &quot;(Speed|Duplex)&quot;\n\necho &quot;=== PCIe Information ===&quot;\nlspci | grep -E &quot;(VGA|Ethernet|SATA|USB)&quot;\n\necho &quot;=== Temperature Information ===&quot;\nsensors 2&gt;/dev/null || echo &quot;lm-sensors not installed&quot;</code></pre><h3 id=\"windows-powershell-script\">Windows PowerShell Script</h3><pre class=\"code-block\" data-language=\"powershell\"><code># Hardware compatibility check for Windows\nWrite-Host &quot;=== Hardware Compatibility Check ===&quot; -ForegroundColor Green\nWrite-Host &quot;Date: $(Get-Date)&quot; -ForegroundColor Yellow\n\nWrite-Host &quot;`n=== CPU Information ===&quot; -ForegroundColor Green\nGet-WmiObject -Class Win32_Processor | Select-Object Name, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed\n\nWrite-Host &quot;`n=== Memory Information ===&quot; -ForegroundColor Green\nGet-WmiObject -Class Win32_PhysicalMemory | Select-Object Capacity, Speed, MemoryType\n\nWrite-Host &quot;`n=== Storage Information ===&quot; -ForegroundColor Green\nGet-WmiObject -Class Win32_DiskDrive | Select-Object Model, Size, InterfaceType\n\nWrite-Host &quot;`n=== Network Information ===&quot; -ForegroundColor Green\nGet-WmiObject -Class Win32_NetworkAdapter | Where-Object {$_.NetEnabled -eq $true} | Select-Object Name, Speed</code></pre><h2 id=\"compatibility-documentation\">Compatibility Documentation</h2><h3 id=\"test-results-template\">Test Results Template</h3><pre class=\"code-block\" data-language=\"text\"><code>Hardware Compatibility Test Results\n=====================================\nDate: ___________\nTechnician: ___________\nSystem Model: ___________\n\nCPU Compatibility:\n[ ] Socket match confirmed\n[ ] BIOS support verified\n[ ] Stress test passed (30 min)\n[ ] All cores operational\nNotes: ________________________\n\nMemory Compatibility:\n[ ] Type match (DDR3/DDR4)\n[ ] Speed supported\n[ ] All slots functional\n[ ] MemTest86+ passed (2 cycles)\nNotes: ________________________\n\nStorage Compatibility:\n[ ] Interface supported (SATA/NVMe)\n[ ] BIOS boot support\n[ ] Health check passed\n[ ] Performance adequate\nNotes: ________________________\n\nNetwork Compatibility:\n[ ] Driver available\n[ ] Gigabit speed confirmed\n[ ] Auto-negotiation working\n[ ] Cable test passed\nNotes: ________________________\n\nPower Supply:\n[ ] Wattage adequate\n[ ] All voltages stable\n[ ] Load test passed\n[ ] Efficiency acceptable\nNotes: ________________________\n\nFinal Assessment:\n[ ] All components compatible\n[ ] System ready for deployment\n[ ] Requires component replacement: ________\n[ ] Not suitable for server use\n\nSignature: ___________________</code></pre><h2 id=\"troubleshooting-common-issues\">Troubleshooting Common Issues</h2><h3 id=\"memory-compatibility-problems\">Memory Compatibility Problems</h3><p><strong>Symptoms</strong>: System won&#39;t boot, random crashes, blue screens<br><strong>Solutions</strong>:</p>\n<ul>\n<li>Test memory sticks individually</li>\n<li>Check supported memory types in motherboard manual</li>\n<li>Update BIOS to latest version</li>\n<li>Adjust memory timing in BIOS</li>\n<li>Use memory from motherboard QVL (Qualified Vendor List)</li>\n</ul>\n<h3 id=\"storage-compatibility-issues\">Storage Compatibility Issues</h3><p><strong>Symptoms</strong>: Drive not detected, slow performance, boot failures<br><strong>Solutions</strong>:</p>\n<ul>\n<li>Verify SATA mode (AHCI vs IDE) in BIOS</li>\n<li>Check cable connections and try different SATA ports</li>\n<li>Update storage controller drivers</li>\n<li>Enable NVMe support in BIOS for M.2 drives</li>\n<li>Check for firmware updates on storage devices</li>\n</ul>\n<h3 id=\"network-compatibility-problems\">Network Compatibility Problems</h3><p><strong>Symptoms</strong>: No network connectivity, limited speeds, driver issues<br><strong>Solutions</strong>:</p>\n<ul>\n<li>Download and install latest network drivers</li>\n<li>Check cable and port connections</li>\n<li>Test with known-good network cable</li>\n<li>Verify network settings in BIOS</li>\n<li>Consider add-in network card if onboard fails</li>\n</ul>\n<h2 id=\"quality-assurance-checklist\">Quality Assurance Checklist</h2><h3 id=\"final-compatibility-verification\">Final Compatibility Verification</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> All components properly detected by OS</li>\n<li><input disabled=\"\" type=\"checkbox\"> No hardware conflicts in Device Manager</li>\n<li><input disabled=\"\" type=\"checkbox\"> System stable under load for 24+ hours</li>\n<li><input disabled=\"\" type=\"checkbox\"> All expected features functional</li>\n<li><input disabled=\"\" type=\"checkbox\"> Performance meets minimum requirements</li>\n<li><input disabled=\"\" type=\"checkbox\"> Documentation complete and accurate</li>\n</ul>\n<h3 id=\"burn-in-testing-protocol\">Burn-in Testing Protocol</h3><ol>\n<li><p><strong>48-Hour Stress Test</strong>:</p>\n<ul>\n<li>CPU: 100% load for 8 hours daily</li>\n<li>Memory: Continuous testing rotation</li>\n<li>Storage: Read/write cycle testing</li>\n<li>Network: Continuous throughput testing</li>\n</ul>\n</li>\n<li><p><strong>Temperature Monitoring</strong>:</p>\n<ul>\n<li>Log temperatures throughout burn-in</li>\n<li>Verify no thermal throttling</li>\n<li>Confirm fan operation at various loads</li>\n</ul>\n</li>\n<li><p><strong>Stability Verification</strong>:</p>\n<ul>\n<li>No system crashes or freezes</li>\n<li>No error messages in system logs</li>\n<li>All components maintain rated performance</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"next-steps\">Next Steps</h2><p>After completing compatibility testing:</p>\n<ol>\n<li><strong>Document Results</strong>: Complete compatibility test report</li>\n<li><strong>Physical Preparation</strong>: Follow <a href=\"recycled-preparation.md\" target=\"_blank\">Recycled Preparation</a> guide</li>\n<li><strong>BIOS Configuration</strong>: Use <a href=\"bios-access-and-setup.md\" target=\"_blank\">BIOS Access and Setup</a> for server optimization</li>\n<li><strong>Operating System</strong>: Install and configure server OS</li>\n<li><strong>Service Configuration</strong>: Deploy intended server services</li>\n</ol>\n<hr>\n<p><em>Compatibility testing should be performed in a controlled environment with proper safety precautions. Always verify compatibility with intended operating system and server applications.</em></p>\n"}, "guides/mini-pc-decision-framework.md": {"hash": "62c3384cebae0eb95d198441b67e912e", "content": "<h1 id=\"mini-pc-ai-deployment-decision-framework\">Mini PC AI Deployment Decision Framework</h1><h2 id=\"quick-decision-matrix\">Quick Decision Matrix</h2><table class=\"pdf-table\">\n        <thead><tr>\n<th>Use Case</th>\n<th>Community Size</th>\n<th>Recommended Solution</th>\n<th>Estimated Cost</th>\n<th>Deployment Complexity</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Light AI tasks</td>\n<td>1-5 users</td>\n<td>Mac Mini M4</td>\n<td>$800-1,200</td>\n<td>Low</td>\n</tr>\n<tr>\n<td>Medium AI workloads</td>\n<td>5-15 users</td>\n<td>Mini PC + mid-range eGPU</td>\n<td>$1,200-1,800</td>\n<td>Medium</td>\n</tr>\n<tr>\n<td>Heavy AI processing</td>\n<td>15+ users</td>\n<td>Mini PC + high-end eGPU</td>\n<td>$2,000-3,000</td>\n<td>Medium-High</td>\n</tr>\n<tr>\n<td>Development/Research</td>\n<td>Variable</td>\n<td>Multiple mini PCs + shared eGPU</td>\n<td>$1,500-4,000</td>\n<td>High</td>\n</tr>\n</tbody>\n      </table><h2 id=\"selection-criteria-scoring\">Selection Criteria Scoring</h2><h3 id=\"performance-requirements-weight-40\">Performance Requirements (Weight: 40%)</h3><ul>\n<li><strong>Light AI Tasks (Score 1-3)</strong>: Document processing, basic chatbots</li>\n<li><strong>Medium AI Tasks (Score 4-6)</strong>: Image generation, medium LLMs</li>\n<li><strong>Heavy AI Tasks (Score 7-10)</strong>: Large language models, video processing</li>\n</ul>\n<h3 id=\"budget-constraints-weight-25\">Budget Constraints (Weight: 25%)</h3><ul>\n<li><strong>Low Budget (Score 1-3)</strong>: Under $1,000 total</li>\n<li><strong>Medium Budget (Score 4-6)</strong>: $1,000-2,500 total</li>\n<li><strong>High Budget (Score 7-10)</strong>: $2,500+ total</li>\n</ul>\n<h3 id=\"spaceaesthetics-weight-15\">Space/Aesthetics (Weight: 15%)</h3><ul>\n<li><strong>Very Limited (Score 1-3)</strong>: Must be nearly invisible</li>\n<li><strong>Limited (Score 4-6)</strong>: Small footprint acceptable</li>\n<li><strong>Flexible (Score 7-10)</strong>: Space not a major constraint</li>\n</ul>\n<h3 id=\"technical-expertise-weight-10\">Technical Expertise (Weight: 10%)</h3><ul>\n<li><strong>Beginner (Score 1-3)</strong>: Plug-and-play solutions only</li>\n<li><strong>Intermediate (Score 4-6)</strong>: Some configuration acceptable</li>\n<li><strong>Advanced (Score 7-10)</strong>: Complex setups manageable</li>\n</ul>\n<h3 id=\"future-expansion-weight-10\">Future Expansion (Weight: 10%)</h3><ul>\n<li><strong>Fixed (Score 1-3)</strong>: No upgrade plans</li>\n<li><strong>Moderate (Score 4-6)</strong>: Some upgrade capability desired</li>\n<li><strong>High (Score 7-10)</strong>: Maximum upgrade flexibility needed</li>\n</ul>\n<h2 id=\"recommended-configurations-by-score\">Recommended Configurations by Score</h2><h3 id=\"total-score-15-25-entry-level\">Total Score 15-25: Entry Level</h3><p><strong>Recommended</strong>: Mac Mini M4 or ASUS NUC 14 Pro AI</p>\n<ul>\n<li>Best for: Basic AI tasks, small communities</li>\n<li>Pros: Simple setup, low power, quiet operation</li>\n<li>Cons: Limited scalability, no external GPU support (Mac)</li>\n</ul>\n<h3 id=\"total-score-26-35-intermediate\">Total Score 26-35: Intermediate</h3><p><strong>Recommended</strong>: Beelink GTi14 + Thunderbolt eGPU</p>\n<ul>\n<li>Best for: Growing communities, mixed workloads</li>\n<li>Pros: Good balance of performance and simplicity</li>\n<li>Cons: Some performance limitations with Thunderbolt</li>\n</ul>\n<h3 id=\"total-score-36-45-advanced\">Total Score 36-45: Advanced</h3><p><strong>Recommended</strong>: AOOSTAR GEM12 Pro + OCuLink eGPU</p>\n<ul>\n<li>Best for: High-performance requirements, experienced users</li>\n<li>Pros: Maximum performance potential</li>\n<li>Cons: More complex setup, higher power consumption</li>\n</ul>\n<h3 id=\"total-score-46-50-professional\">Total Score 46-50: Professional</h3><p><strong>Recommended</strong>: Multiple mini PC cluster with shared resources</p>\n<ul>\n<li>Best for: Research institutions, large communities</li>\n<li>Pros: Scalable, redundant, high performance</li>\n<li>Cons: Complex management, higher total cost</li>\n</ul>\n<h2 id=\"implementation-phases\">Implementation Phases</h2><h3 id=\"phase-1-planning-weeks-1-2\">Phase 1: Planning (Weeks 1-2)</h3><ol>\n<li>Assess community needs and technical requirements</li>\n<li>Calculate budget including hidden costs</li>\n<li>Evaluate physical space and infrastructure</li>\n<li>Determine support and maintenance capabilities</li>\n</ol>\n<h3 id=\"phase-2-procurement-weeks-3-4\">Phase 2: Procurement (Weeks 3-4)</h3><ol>\n<li>Source hardware from reliable vendors</li>\n<li>Purchase necessary accessories and cables</li>\n<li>Acquire software licenses if needed</li>\n<li>Set up remote management accounts</li>\n</ol>\n<h3 id=\"phase-3-setup-weeks-5-6\">Phase 3: Setup (Weeks 5-6)</h3><ol>\n<li>Configure mini PC with operating system</li>\n<li>Install AI software and frameworks</li>\n<li>Set up external GPU if applicable</li>\n<li>Configure remote access and monitoring</li>\n</ol>\n<h3 id=\"phase-4-testing-week-7\">Phase 4: Testing (Week 7)</h3><ol>\n<li>Run performance benchmarks</li>\n<li>Test multi-user scenarios</li>\n<li>Validate thermal and power performance</li>\n<li>Document any issues or optimizations</li>\n</ol>\n<h3 id=\"phase-5-deployment-week-8\">Phase 5: Deployment (Week 8)</h3><ol>\n<li>Train users on access methods</li>\n<li>Implement monitoring and backup procedures</li>\n<li>Create maintenance schedule</li>\n<li>Establish support procedures</li>\n</ol>\n<h2 id=\"risk-mitigation-strategies\">Risk Mitigation Strategies</h2><h3 id=\"technical-risks\">Technical Risks</h3><ul>\n<li><strong>Hardware failure</strong>: Purchase extended warranties, keep spare components</li>\n<li><strong>Performance issues</strong>: Conduct thorough testing before full deployment</li>\n<li><strong>Compatibility problems</strong>: Verify all components before purchase</li>\n</ul>\n<h3 id=\"operational-risks\">Operational Risks</h3><ul>\n<li><strong>Power outages</strong>: Install UPS systems for critical deployments</li>\n<li><strong>Network issues</strong>: Ensure reliable internet connectivity</li>\n<li><strong>User support</strong>: Provide clear documentation and training</li>\n</ul>\n<h3 id=\"financial-risks\">Financial Risks</h3><ul>\n<li><strong>Cost overruns</strong>: Include 20% buffer in budget calculations</li>\n<li><strong>Rapid obsolescence</strong>: Plan for 3-4 year replacement cycles</li>\n<li><strong>Hidden costs</strong>: Account for cooling, accessories, and maintenance</li>\n</ul>\n<h2 id=\"success-metrics\">Success Metrics</h2><h3 id=\"performance-metrics\">Performance Metrics</h3><ul>\n<li>Average response time for AI queries</li>\n<li>System uptime percentage</li>\n<li>Concurrent user capacity</li>\n<li>Power efficiency (performance per watt)</li>\n</ul>\n<h3 id=\"user-satisfaction-metrics\">User Satisfaction Metrics</h3><ul>\n<li>User adoption rate</li>\n<li>Support ticket volume</li>\n<li>System ease-of-use ratings</li>\n<li>Feature utilization statistics</li>\n</ul>\n<h3 id=\"financial-metrics\">Financial Metrics</h3><ul>\n<li>Total cost of ownership vs. budget</li>\n<li>Performance per dollar spent</li>\n<li>Operational cost trends</li>\n<li>Return on investment</li>\n</ul>\n<h2 id=\"maintenance-schedule\">Maintenance Schedule</h2><h3 id=\"daily\">Daily</h3><ul>\n<li>Monitor system status and alerts</li>\n<li>Check available storage space</li>\n<li>Verify backup completion</li>\n</ul>\n<h3 id=\"weekly\">Weekly</h3><ul>\n<li>Review performance metrics</li>\n<li>Clean dust from air intakes</li>\n<li>Update usage statistics</li>\n</ul>\n<h3 id=\"monthly\">Monthly</h3><ul>\n<li>Install security updates</li>\n<li>Review and optimize configurations</li>\n<li>Check thermal paste condition (if needed)</li>\n<li>Evaluate user feedback</li>\n</ul>\n<h3 id=\"quarterly\">Quarterly</h3><ul>\n<li>Conduct comprehensive performance review</li>\n<li>Plan for capacity expansion if needed</li>\n<li>Review vendor relationships and support</li>\n<li>Update disaster recovery procedures</li>\n</ul>\n<h3 id=\"annually\">Annually</h3><ul>\n<li>Evaluate upgrade opportunities</li>\n<li>Refresh maintenance agreements</li>\n<li>Review total cost of ownership</li>\n<li>Plan for next generation technology adoption</li>\n</ul>\n"}, "guides/recycled-pc-requirements.md": {"hash": "d0d9c7fdfa6f553ae49bc6ce9ddd81cd", "content": "<h1 id=\"recycled-pc-hardware-requirements-for-community-servers\">Recycled PC Hardware Requirements for Community Servers</h1><h2 id=\"overview\">Overview</h2><p>This guide establishes minimum and recommended hardware specifications for selecting suitable recycled computers to repurpose as community servers. These requirements balance performance needs with the practical limitations of older hardware while ensuring reliability for server workloads.</p>\n<h2 id=\"minimum-hardware-requirements\">Minimum Hardware Requirements</h2><h3 id=\"processor-cpu\">Processor (CPU)</h3><ul>\n<li><strong>Minimum</strong>: 64-bit processor (x86_64 architecture)</li>\n<li><strong>Clock Speed</strong>: 1.4 GHz or higher</li>\n<li><strong>Cores</strong>: At least 2 cores (4 cores recommended)</li>\n<li><strong>Generation</strong>: Intel 4th generation (Haswell) or AMD equivalent and newer</li>\n<li><strong>Required Features</strong>:<ul>\n<li>Support for NX bit and DEP</li>\n<li>CMPXCHG16b, LAHF/SAHF, and PrefetchW instructions</li>\n<li>Virtualization support (Intel VT-x or AMD-V)</li>\n<li>Second Level Address Translation (EPT or NPT)</li>\n</ul>\n</li>\n</ul>\n<h3 id=\"memory-ram\">Memory (RAM)</h3><ul>\n<li><strong>Absolute Minimum</strong>: 4 GB DDR3/DDR4</li>\n<li><strong>Recommended Minimum</strong>: 8 GB</li>\n<li><strong>Optimal</strong>: 16 GB or more</li>\n<li><strong>Type</strong>: DDR3-1600 or DDR4-2400 minimum</li>\n<li><strong>ECC Support</strong>: Preferred but not required</li>\n</ul>\n<h3 id=\"storage\">Storage</h3><ul>\n<li><strong>Boot Drive</strong>: Minimum 120 GB SSD (256 GB recommended)</li>\n<li><strong>Data Storage</strong>: 1 TB HDD minimum for file server functions</li>\n<li><strong>Interface</strong>: SATA 3.0 (6 Gbps) preferred</li>\n<li><strong>RAID Support</strong>: Hardware RAID controller preferred for redundancy</li>\n</ul>\n<h3 id=\"network-connectivity\">Network Connectivity</h3><ul>\n<li><strong>Ethernet</strong>: Gigabit Ethernet (1000 Mbps) required</li>\n<li><strong>Wi-Fi</strong>: 802.11n minimum (802.11ac preferred)</li>\n<li><strong>Multiple NICs</strong>: Beneficial for advanced networking configurations</li>\n</ul>\n<h3 id=\"power-supply\">Power Supply</h3><ul>\n<li><strong>Wattage</strong>: 300W minimum (efficiency rated preferred)</li>\n<li><strong>Efficiency</strong>: 80 PLUS Bronze or better</li>\n<li><strong>Redundancy</strong>: Dual PSU capability preferred for critical applications</li>\n</ul>\n<h2 id=\"recommended-hardware-specifications\">Recommended Hardware Specifications</h2><h3 id=\"enhanced-server-configuration\">Enhanced Server Configuration</h3><ul>\n<li><strong>CPU</strong>: Intel i5/i7 6th gen+ or AMD Ryzen 3/5/7</li>\n<li><strong>RAM</strong>: 16-32 GB DDR4</li>\n<li><strong>Storage</strong>: 512 GB NVMe SSD + 2+ TB HDD array</li>\n<li><strong>Network</strong>: Dual Gigabit NICs</li>\n<li><strong>Form Factor</strong>: Small Form Factor (SFF) or Micro tower preferred</li>\n</ul>\n<h2 id=\"hardware-selection-checklist\">Hardware Selection Checklist</h2><h3 id=\"pre-purchase-assessment\">Pre-Purchase Assessment</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Verify CPU meets 64-bit requirements</li>\n<li><input disabled=\"\" type=\"checkbox\"> Check maximum supported RAM capacity</li>\n<li><input disabled=\"\" type=\"checkbox\"> Confirm working Ethernet ports</li>\n<li><input disabled=\"\" type=\"checkbox\"> Test all USB ports functionality</li>\n<li><input disabled=\"\" type=\"checkbox\"> Inspect for physical damage or excessive wear</li>\n<li><input disabled=\"\" type=\"checkbox\"> Verify power supply functionality</li>\n<li><input disabled=\"\" type=\"checkbox\"> Check BIOS/UEFI accessibility</li>\n<li><input disabled=\"\" type=\"checkbox\"> Confirm expansion slot availability</li>\n</ul>\n<h3 id=\"compatibility-verification\">Compatibility Verification</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> CPU supports virtualization features</li>\n<li><input disabled=\"\" type=\"checkbox\"> Motherboard supports target RAM configuration</li>\n<li><input disabled=\"\" type=\"checkbox\"> Storage interfaces match available drives</li>\n<li><input disabled=\"\" type=\"checkbox\"> Network cards support required speeds</li>\n<li><input disabled=\"\" type=\"checkbox\"> Power supply meets total system requirements</li>\n<li><input disabled=\"\" type=\"checkbox\"> BIOS/UEFI supports modern boot modes</li>\n</ul>\n<h3 id=\"physical-condition-assessment\">Physical Condition Assessment</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> No visible component damage</li>\n<li><input disabled=\"\" type=\"checkbox\"> Clean internal components possible</li>\n<li><input disabled=\"\" type=\"checkbox\"> Adequate cooling system present</li>\n<li><input disabled=\"\" type=\"checkbox\"> All expansion slots accessible</li>\n<li><input disabled=\"\" type=\"checkbox\"> No signs of liquid damage</li>\n<li><input disabled=\"\" type=\"checkbox\"> Minimal dust accumulation</li>\n</ul>\n<h2 id=\"form-factor-considerations\">Form Factor Considerations</h2><h3 id=\"desktop-towers\">Desktop Towers</h3><ul>\n<li><strong>Pros</strong>: Excellent expandability, good cooling, multiple drive bays</li>\n<li><strong>Cons</strong>: Larger footprint, higher power consumption</li>\n<li><strong>Best For</strong>: File servers, development servers, high-capacity storage</li>\n</ul>\n<h3 id=\"small-form-factor-sff\">Small Form Factor (SFF)</h3><ul>\n<li><strong>Pros</strong>: Compact size, lower power consumption, quiet operation</li>\n<li><strong>Cons</strong>: Limited expansion, fewer drive bays</li>\n<li><strong>Best For</strong>: Web servers, lightweight applications, space-constrained environments</li>\n</ul>\n<h3 id=\"minimicro-pcs\">Mini/Micro PCs</h3><ul>\n<li><strong>Pros</strong>: Very compact, low power, silent operation</li>\n<li><strong>Cons</strong>: Very limited expansion, often laptop components</li>\n<li><strong>Best For</strong>: Basic services, edge computing, testing environments</li>\n</ul>\n<h2 id=\"budget-guidelines\">Budget Guidelines</h2><h3 id=\"cost-effective-ranges\">Cost-Effective Ranges</h3><ul>\n<li><strong>Basic Server</strong>: $100-200 (older business PCs)</li>\n<li><strong>Capable Server</strong>: $200-400 (recent business machines)</li>\n<li><strong>Performance Server</strong>: $400-600 (workstation-class hardware)</li>\n</ul>\n<h3 id=\"total-cost-of-ownership\">Total Cost of Ownership</h3><ul>\n<li>Factor in potential upgrade costs (RAM, storage)</li>\n<li>Consider power consumption over 3-5 years</li>\n<li>Account for replacement part availability</li>\n<li>Budget for initial refurbishment supplies</li>\n</ul>\n<h2 id=\"exclusion-criteria\">Exclusion Criteria</h2><h3 id=\"avoid-these-configurations\">Avoid These Configurations</h3><ul>\n<li>32-bit only processors</li>\n<li>Less than 4 GB maximum RAM capacity</li>\n<li>IDE/PATA storage interfaces only</li>\n<li>100 Mbps Ethernet maximum</li>\n<li>Proprietary form factors with limited upgrade paths</li>\n<li>Systems with known reliability issues</li>\n<li>Machines requiring expensive proprietary components</li>\n</ul>\n<h2 id=\"special-considerations\">Special Considerations</h2><h3 id=\"low-power-requirements\">Low-Power Requirements</h3><p>For energy-efficient operations:</p>\n<ul>\n<li>Target systems with 35W TDP CPUs</li>\n<li>Look for 80 PLUS Gold or higher PSUs</li>\n<li>Consider systems with solid-state cooling</li>\n<li>Prioritize Intel Atom or low-power AMD processors</li>\n</ul>\n<h3 id=\"redundancy-and-reliability\">Redundancy and Reliability</h3><p>For mission-critical applications:</p>\n<ul>\n<li>Prefer systems with ECC RAM support</li>\n<li>Look for dual PSU capability</li>\n<li>Consider RAID controller support</li>\n<li>Prioritize business/enterprise equipment</li>\n</ul>\n<h3 id=\"scalability-planning\">Scalability Planning</h3><ul>\n<li>Choose systems with available expansion slots</li>\n<li>Ensure adequate power supply headroom</li>\n<li>Verify maximum supported RAM limits</li>\n<li>Consider clustering capability for future growth</li>\n</ul>\n<h2 id=\"quick-reference-specifications\">Quick Reference Specifications</h2><table class=\"pdf-table\">\n        <thead><tr>\n<th>Component</th>\n<th>Minimum</th>\n<th>Recommended</th>\n<th>Optimal</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>CPU</td>\n<td>Dual-core 1.4GHz</td>\n<td>Quad-core 2.0GHz+</td>\n<td>6+ cores 3.0GHz+</td>\n</tr>\n<tr>\n<td>RAM</td>\n<td>4 GB DDR3</td>\n<td>8 GB DDR4</td>\n<td>16+ GB DDR4</td>\n</tr>\n<tr>\n<td>Storage</td>\n<td>120 GB SSD</td>\n<td>256 GB SSD + 1 TB HDD</td>\n<td>512 GB NVMe + 2+ TB</td>\n</tr>\n<tr>\n<td>Network</td>\n<td>100 Mbps</td>\n<td>1 Gbps</td>\n<td>Dual 1 Gbps</td>\n</tr>\n<tr>\n<td>Power</td>\n<td>300W</td>\n<td>400W 80+ Bronze</td>\n<td>500W+ 80+ Gold</td>\n</tr>\n</tbody>\n      </table><h2 id=\"next-steps\">Next Steps</h2><ol>\n<li><strong>Review</strong>: <a href=\"recycled-models-comparison.md\" target=\"_blank\">Recycled Models Comparison</a> for specific model recommendations</li>\n<li><strong>Test</strong>: Use <a href=\"hardware-compatibility-check.md\" target=\"_blank\">Hardware Compatibility Check</a> to verify components</li>\n<li><strong>Prepare</strong>: Follow <a href=\"recycled-preparation.md\" target=\"_blank\">Recycled Preparation</a> for refurbishment</li>\n<li><strong>Configure</strong>: Apply <a href=\"bios-access-and-setup.md\" target=\"_blank\">BIOS Access and Setup</a> for system configuration</li>\n</ol>\n<hr>\n<p><em>This guide provides general recommendations. Always verify specific compatibility requirements for your intended server applications and operating system.</em></p>\n"}, "guides/recycled-models-comparison.md": {"hash": "430986a18ab2b2eeabb4a99ad09368e8", "content": "<h1 id=\"recycled-pclaptop-models-comparison-for-community-servers\">Recycled PC/Laptop Models Comparison for Community Servers</h1><h2 id=\"overview\">Overview</h2><p>This comparison table highlights commonly available recycled PC and laptop models that are well-suited for repurposing as community servers. Models are evaluated based on specifications, reliability, upgrade potential, and ease of deployment.</p>\n<h2 id=\"desktop-computer-models\">Desktop Computer Models</h2><h3 id=\"dell-optiplex-series\">Dell OptiPlex Series</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Generation</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>OptiPlex 7050 SFF</td>\n<td>6th/7th Gen</td>\n<td>i3-6100 to i7-7700</td>\n<td>64 GB DDR4</td>\n<td>M.2 NVMe + SATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>Excellent choice, modern features</td>\n</tr>\n<tr>\n<td>OptiPlex 9020 MT/SFF</td>\n<td>4th Gen</td>\n<td>i3-4130 to i7-4790</td>\n<td>32 GB DDR3</td>\n<td>SATA 3.0</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐⭐</td>\n<td>Very reliable, good performance</td>\n</tr>\n<tr>\n<td>OptiPlex 7010/9010</td>\n<td>3rd Gen</td>\n<td>i3-3220 to i7-3770</td>\n<td>16 GB DDR3</td>\n<td>SATA 3.0</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐</td>\n<td>Budget option, adequate for basic tasks</td>\n</tr>\n<tr>\n<td>OptiPlex 3050</td>\n<td>6th/7th Gen</td>\n<td>Pentium to i5-7500</td>\n<td>32 GB DDR4</td>\n<td>SATA + M.2</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐⭐</td>\n<td>Good value, modern connectivity</td>\n</tr>\n</tbody>\n      </table><h3 id=\"hp-prodeskelitedesk-series\">HP ProDesk/EliteDesk Series</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Generation</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>EliteDesk 800 G3</td>\n<td>6th/7th Gen</td>\n<td>i3-6100 to i7-7700</td>\n<td>64 GB DDR4</td>\n<td>M.2 NVMe + SATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>Premium features, excellent build</td>\n</tr>\n<tr>\n<td>ProDesk 600 G2</td>\n<td>6th Gen</td>\n<td>i3-6100 to i7-6700</td>\n<td>32 GB DDR4</td>\n<td>SATA + M.2</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐⭐</td>\n<td>Good balance of features/cost</td>\n</tr>\n<tr>\n<td>EliteDesk 800 G1</td>\n<td>4th Gen</td>\n<td>i3-4130 to i7-4790</td>\n<td>32 GB DDR3</td>\n<td>SATA 3.0</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐⭐</td>\n<td>Reliable business machine</td>\n</tr>\n<tr>\n<td>ProDesk 400 G3</td>\n<td>6th Gen</td>\n<td>Pentium to i7-6700</td>\n<td>32 GB DDR4</td>\n<td>SATA + M.2</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐</td>\n<td>Entry-level but capable</td>\n</tr>\n</tbody>\n      </table><h3 id=\"lenovo-thinkcentre-series\">Lenovo ThinkCentre Series</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Generation</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>ThinkCentre M920q</td>\n<td>8th Gen</td>\n<td>i3-8100T to i7-8700T</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>Tiny form factor, very efficient</td>\n</tr>\n<tr>\n<td>ThinkCentre M710q</td>\n<td>6th/7th Gen</td>\n<td>i3-6100T to i7-7700T</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Compact and powerful</td>\n</tr>\n<tr>\n<td>ThinkCentre M93p</td>\n<td>4th Gen</td>\n<td>i3-4130 to i7-4770</td>\n<td>32 GB DDR3</td>\n<td>SATA 3.0</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐⭐</td>\n<td>Solid business desktop</td>\n</tr>\n<tr>\n<td>ThinkCentre M83</td>\n<td>4th Gen</td>\n<td>Pentium to i7-4770</td>\n<td>16 GB DDR3</td>\n<td>SATA 3.0</td>\n<td>Gigabit</td>\n<td>⭐⭐⭐</td>\n<td>Budget-friendly option</td>\n</tr>\n</tbody>\n      </table><h2 id=\"laptop-models-for-server-use\">Laptop Models for Server Use</h2><h3 id=\"dell-latitude-series\">Dell Latitude Series</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Generation</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Latitude 7480/7490</td>\n<td>7th/8th Gen</td>\n<td>i5-7300U to i7-8650U</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Excellent for edge servers</td>\n</tr>\n<tr>\n<td>Latitude E7450/E7470</td>\n<td>5th/6th Gen</td>\n<td>i5-5300U to i7-6600U</td>\n<td>16 GB DDR3L</td>\n<td>M.2 + SATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐</td>\n<td>Good for lightweight servers</td>\n</tr>\n<tr>\n<td>Latitude E6540</td>\n<td>4th Gen</td>\n<td>i5-4200M to i7-4800MQ</td>\n<td>32 GB DDR3L</td>\n<td>SATA + mSATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐</td>\n<td>Workstation laptop, good specs</td>\n</tr>\n</tbody>\n      </table><h3 id=\"hp-elitebookprobook-series\">HP EliteBook/ProBook Series</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Generation</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>EliteBook 850 G3/G4</td>\n<td>6th/7th Gen</td>\n<td>i5-6200U to i7-7600U</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Business-class reliability</td>\n</tr>\n<tr>\n<td>ProBook 650 G2/G3</td>\n<td>5th/6th Gen</td>\n<td>i3-5010U to i7-6600U</td>\n<td>16 GB DDR3L</td>\n<td>SATA + M.2</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐</td>\n<td>Good value proposition</td>\n</tr>\n<tr>\n<td>EliteBook 8570w</td>\n<td>3rd Gen</td>\n<td>i5-3360M to i7-3940XM</td>\n<td>32 GB DDR3</td>\n<td>SATA + mSATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐</td>\n<td>Mobile workstation power</td>\n</tr>\n</tbody>\n      </table><h3 id=\"lenovo-thinkpad-series\">Lenovo ThinkPad Series</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Generation</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>ThinkPad T480/T490</td>\n<td>8th Gen</td>\n<td>i5-8250U to i7-8650U</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>Excellent build quality</td>\n</tr>\n<tr>\n<td>ThinkPad T460/T470</td>\n<td>6th/7th Gen</td>\n<td>i5-6200U to i7-7600U</td>\n<td>32 GB DDR4</td>\n<td>M.2 + SATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Very reliable platform</td>\n</tr>\n<tr>\n<td>ThinkPad W541</td>\n<td>4th Gen</td>\n<td>i7-4700MQ to i7-4940MX</td>\n<td>32 GB DDR3L</td>\n<td>SATA + mSATA</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Workstation performance</td>\n</tr>\n</tbody>\n      </table><h2 id=\"specialized-server-hardware\">Specialized Server Hardware</h2><h3 id=\"small-form-factormini-pcs\">Small Form Factor/Mini PCs</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Model</th>\n<th>Type</th>\n<th>CPU Options</th>\n<th>Max RAM</th>\n<th>Storage</th>\n<th>Network</th>\n<th>Server Suitability</th>\n<th>Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Intel NUC 7i5/7i7</td>\n<td>Mini PC</td>\n<td>i5-7260U to i7-7567U</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Very compact, efficient</td>\n</tr>\n<tr>\n<td>HP EliteDesk 705 G4</td>\n<td>Mini PC</td>\n<td>AMD A10/A12 PRO</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐</td>\n<td>AMD alternative, good value</td>\n</tr>\n<tr>\n<td>Dell OptiPlex 3060 Micro</td>\n<td>Micro PC</td>\n<td>Pentium to i7-8700T</td>\n<td>32 GB DDR4</td>\n<td>M.2 NVMe</td>\n<td>Gigabit + Wi-Fi</td>\n<td>⭐⭐⭐⭐</td>\n<td>Tiny footprint, modern</td>\n</tr>\n</tbody>\n      </table><h2 id=\"selection-criteria-comparison\">Selection Criteria Comparison</h2><h3 id=\"performance-ranking-server-workloads\">Performance Ranking (Server Workloads)</h3><ol>\n<li><strong>Tier 1 (Excellent)</strong>: 8th Gen+ Intel Core i5/i7, 16+ GB RAM, NVMe SSD</li>\n<li><strong>Tier 2 (Very Good)</strong>: 6th-7th Gen Intel Core i5/i7, 8+ GB RAM, SATA SSD</li>\n<li><strong>Tier 3 (Good)</strong>: 4th-5th Gen Intel Core i5/i7, 8+ GB RAM, SATA SSD</li>\n<li><strong>Tier 4 (Adequate)</strong>: 3rd-4th Gen Intel Core i3/i5, 4+ GB RAM, HDD/SSD</li>\n</ol>\n<h3 id=\"reliability-assessment\">Reliability Assessment</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Brand/Series</th>\n<th>Reliability Rating</th>\n<th>Common Issues</th>\n<th>MTBF Estimate</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Dell OptiPlex</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>Minimal, well-built</td>\n<td>5-7 years</td>\n</tr>\n<tr>\n<td>HP EliteDesk</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>Minimal, enterprise-grade</td>\n<td>5-7 years</td>\n</tr>\n<tr>\n<td>Lenovo ThinkCentre</td>\n<td>⭐⭐⭐⭐</td>\n<td>Occasional PSU issues</td>\n<td>4-6 years</td>\n</tr>\n<tr>\n<td>HP ProDesk</td>\n<td>⭐⭐⭐⭐</td>\n<td>Generally reliable</td>\n<td>4-6 years</td>\n</tr>\n<tr>\n<td>Dell Inspiron</td>\n<td>⭐⭐⭐</td>\n<td>Consumer-grade build</td>\n<td>3-5 years</td>\n</tr>\n</tbody>\n      </table><h3 id=\"upgrade-potential\">Upgrade Potential</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Factor</th>\n<th>Desktop Towers</th>\n<th>Small Form Factor</th>\n<th>Laptops</th>\n<th>Mini PCs</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>RAM Upgrade</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>⭐⭐⭐⭐</td>\n<td>⭐⭐⭐</td>\n<td>⭐⭐</td>\n</tr>\n<tr>\n<td>Storage Expansion</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>⭐⭐⭐⭐</td>\n<td>⭐⭐</td>\n<td>⭐⭐</td>\n</tr>\n<tr>\n<td>Network Cards</td>\n<td>⭐⭐⭐⭐⭐</td>\n<td>⭐⭐⭐</td>\n<td>⭐</td>\n<td>⭐</td>\n</tr>\n<tr>\n<td>Power Supply</td>\n<td>⭐⭐⭐⭐</td>\n<td>⭐⭐</td>\n<td>⭐</td>\n<td>⭐</td>\n</tr>\n</tbody>\n      </table><h3 id=\"power-consumption\">Power Consumption</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Form Factor</th>\n<th>Idle Power</th>\n<th>Load Power</th>\n<th>Annual Cost*</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Desktop Tower</td>\n<td>60-80W</td>\n<td>150-250W</td>\n<td>$150-200</td>\n</tr>\n<tr>\n<td>Small Form Factor</td>\n<td>25-40W</td>\n<td>80-120W</td>\n<td>$75-125</td>\n</tr>\n<tr>\n<td>Laptop (plugged)</td>\n<td>15-25W</td>\n<td>45-65W</td>\n<td>$40-75</td>\n</tr>\n<tr>\n<td>Mini PC</td>\n<td>10-20W</td>\n<td>35-55W</td>\n<td>$30-60</td>\n</tr>\n</tbody>\n      </table><p>*Based on $0.12/kWh, 24/7 operation</p>\n<h2 id=\"recommended-models-by-use-case\">Recommended Models by Use Case</h2><h3 id=\"file-server-high-storage\">File Server (High Storage)</h3><ol>\n<li><strong>Dell OptiPlex 9020 MT</strong> - Multiple drive bays, reliable</li>\n<li><strong>HP EliteDesk 800 G1 MT</strong> - Good expansion, enterprise-grade</li>\n<li><strong>Lenovo ThinkCentre M93p Tower</strong> - Solid build, multiple drives</li>\n</ol>\n<h3 id=\"web-server-low-power\">Web Server (Low Power)</h3><ol>\n<li><strong>Dell OptiPlex 7050 SFF</strong> - Modern, efficient, compact</li>\n<li><strong>HP EliteDesk 800 G3 Mini</strong> - Very low power, quiet</li>\n<li><strong>Intel NUC 7i5</strong> - Minimal footprint, good performance</li>\n</ol>\n<h3 id=\"development-server-high-performance\">Development Server (High Performance)</h3><ol>\n<li><strong>Dell Precision 3620</strong> - Workstation-class, high RAM</li>\n<li><strong>HP Z240 SFF</strong> - Professional workstation features</li>\n<li><strong>Lenovo ThinkPad W541</strong> - Mobile workstation power</li>\n</ol>\n<h3 id=\"edgeremote-server-portable\">Edge/Remote Server (Portable)</h3><ol>\n<li><strong>Lenovo ThinkPad T480</strong> - Excellent reliability, good specs</li>\n<li><strong>Dell Latitude 7480</strong> - Business-grade, versatile</li>\n<li><strong>HP EliteBook 850 G4</strong> - Professional features, durable</li>\n</ol>\n<h2 id=\"quick-decision-matrix\">Quick Decision Matrix</h2><table class=\"pdf-table\">\n        <thead><tr>\n<th>Priority</th>\n<th>Desktop Choice</th>\n<th>Laptop Choice</th>\n<th>Mini PC Choice</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td><strong>Performance</strong></td>\n<td>Dell OptiPlex 7050</td>\n<td>ThinkPad T480</td>\n<td>Intel NUC 7i7</td>\n</tr>\n<tr>\n<td><strong>Reliability</strong></td>\n<td>HP EliteDesk 800 G3</td>\n<td>ThinkPad T470</td>\n<td>Dell OptiPlex 3060</td>\n</tr>\n<tr>\n<td><strong>Value</strong></td>\n<td>Dell OptiPlex 9020</td>\n<td>Dell Latitude E7470</td>\n<td>HP EliteDesk 705 G4</td>\n</tr>\n<tr>\n<td><strong>Efficiency</strong></td>\n<td>Dell OptiPlex 3050</td>\n<td>HP EliteBook 850 G4</td>\n<td>Intel NUC 8i5</td>\n</tr>\n</tbody>\n      </table><h2 id=\"availability-and-pricing\">Availability and Pricing</h2><h3 id=\"typical-market-prices-usd\">Typical Market Prices (USD)</h3><ul>\n<li><strong>Desktop (4th Gen)</strong>: $100-200</li>\n<li><strong>Desktop (6th+ Gen)</strong>: $200-400</li>\n<li><strong>Laptop (4th-5th Gen)</strong>: $150-300</li>\n<li><strong>Laptop (6th+ Gen)</strong>: $300-500</li>\n<li><strong>Mini PC</strong>: $200-400</li>\n</ul>\n<h3 id=\"best-sources\">Best Sources</h3><ul>\n<li>Corporate equipment refreshes</li>\n<li>Government surplus sales</li>\n<li>IT asset disposition companies</li>\n<li>Certified refurbisher outlets</li>\n<li>Online marketplaces (with caution)</li>\n</ul>\n<h2 id=\"next-steps\">Next Steps</h2><ol>\n<li><strong>Requirements</strong>: Review <a href=\"recycled-pc-requirements.md\" target=\"_blank\">Recycled PC Requirements</a> for detailed specs</li>\n<li><strong>Testing</strong>: Use <a href=\"hardware-compatibility-check.md\" target=\"_blank\">Hardware Compatibility Check</a> for verification</li>\n<li><strong>Preparation</strong>: Follow <a href=\"recycled-preparation.md\" target=\"_blank\">Recycled Preparation</a> for refurbishment</li>\n<li><strong>Setup</strong>: Apply <a href=\"bios-access-and-setup.md\" target=\"_blank\">BIOS Access and Setup</a> for configuration</li>\n</ol>\n<hr>\n<p><em>Prices and availability vary by region and market conditions. Always verify specific model specifications before purchase.</em></p>\n"}, "guides/recycled-preparation.md": {"hash": "efb7b93f61c468574e1360670ea5228e", "content": "<h1 id=\"recycled-computer-preparation-guide\">Recycled Computer Preparation Guide</h1><h2 id=\"overview\">Overview</h2><p>This comprehensive guide provides step-by-step instructions for physically cleaning, resetting, and preparing recycled computers for server deployment. Proper preparation ensures optimal performance, reliability, and longevity of repurposed hardware.</p>\n<h2 id=\"safety-precautions\">Safety Precautions</h2><h3 id=\"electrical-safety\">Electrical Safety</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Power Off Completely</strong>: Shut down and unplug from wall outlet</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Power Button Discharge</strong>: Press and hold power button for 10 seconds after unplugging</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Wait for Capacitor Discharge</strong>: Wait 5-10 minutes before opening case</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Remove Battery</strong>: Disconnect laptop battery if applicable</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Use Anti-Static Protection</strong>: Wrist strap or frequent grounding</li>\n</ul>\n<h3 id=\"physical-safety\">Physical Safety</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Work Area</strong>: Clean, well-lit, static-free workspace</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Eye Protection</strong>: Safety glasses when using compressed air</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Ventilation</strong>: Adequate airflow for dust removal</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Sharp Edges</strong>: Be cautious of metal case edges</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Lifting</strong>: Proper lifting techniques for heavy equipment</li>\n</ul>\n<h3 id=\"environmental-considerations\">Environmental Considerations</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Outdoor Cleaning</strong>: Use compressed air outside when possible</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Dust Containment</strong>: Cover nearby equipment during cleaning</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Disposal</strong>: Proper disposal of cleaning materials and removed components</li>\n</ul>\n<h2 id=\"required-tools-and-supplies\">Required Tools and Supplies</h2><h3 id=\"essential-tools\">Essential Tools</h3><ul>\n<li><strong>Screwdrivers</strong>: Phillips head set (#0, #1, #2)</li>\n<li><strong>Compressed Air</strong>: Multiple cans or electric air compressor</li>\n<li><strong>Cleaning Supplies</strong>: Isopropyl alcohol (90%+), microfiber cloths</li>\n<li><strong>Safety Equipment</strong>: Anti-static wrist strap, safety glasses</li>\n<li><strong>Organization</strong>: Small containers for screws, labels for cables</li>\n</ul>\n<h3 id=\"optional-tools\">Optional Tools</h3><ul>\n<li><strong>Thermal Paste</strong>: Arctic MX-4 or equivalent for CPU re-seating</li>\n<li><strong>Cable Ties</strong>: For cable management improvements</li>\n<li><strong>Torx Screwdrivers</strong>: For some laptop models</li>\n<li><strong>Plastic Prying Tools</strong>: For laptop disassembly</li>\n<li><strong>Digital Camera</strong>: For documentation before disassembly</li>\n</ul>\n<h3 id=\"cleaning-materials\">Cleaning Materials</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Material</th>\n<th>Purpose</th>\n<th>Safety Notes</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Isopropyl Alcohol 90%+</td>\n<td>Component cleaning</td>\n<td>Well-ventilated area</td>\n</tr>\n<tr>\n<td>Microfiber Cloths</td>\n<td>Surface cleaning</td>\n<td>Lint-free, anti-static</td>\n</tr>\n<tr>\n<td>Cotton Swabs</td>\n<td>Detail cleaning</td>\n<td>No cotton fibers left behind</td>\n</tr>\n<tr>\n<td>Compressed Air</td>\n<td>Dust removal</td>\n<td>Hold cans upright, short bursts</td>\n</tr>\n<tr>\n<td>Soft-bristle Brush</td>\n<td>Stubborn dust</td>\n<td>Anti-static brushes preferred</td>\n</tr>\n</tbody>\n      </table><h2 id=\"pre-disassembly-documentation\">Pre-Disassembly Documentation</h2><h3 id=\"system-information-gathering\">System Information Gathering</h3><pre class=\"code-block\" data-language=\"bash\"><code># Before powering down, collect system information\n# Windows\nmsinfo32                    # System Information\ndxdiag                     # DirectX Diagnostics\ndevmgmt.msc               # Device Manager\n\n# Linux\nlshw -html &gt; hardware.html # Hardware listing\ndmidecode &gt; dmidecode.txt  # Hardware details\nlscpu &gt; cpu_info.txt      # CPU information</code></pre><h3 id=\"physical-documentation\">Physical Documentation</h3><ol>\n<li><p><strong>Photography</strong>:</p>\n<ul>\n<li>Overall system photos from multiple angles</li>\n<li>Close-ups of cable connections</li>\n<li>Component placement before removal</li>\n<li>Any damage or unusual configurations</li>\n</ul>\n</li>\n<li><p><strong>Labels and Markings</strong>:</p>\n<ul>\n<li>Record model numbers and serial numbers</li>\n<li>Note any custom modifications</li>\n<li>Document cable routing paths</li>\n<li>Identify proprietary components</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"desktop-computer-disassembly\">Desktop Computer Disassembly</h2><h3 id=\"step-1-external-preparation\">Step 1: External Preparation</h3><ol>\n<li><p><strong>Disconnect All Cables</strong>:</p>\n<ul>\n<li>Power cord</li>\n<li>Monitor cables (VGA/HDMI/DisplayPort)</li>\n<li>USB devices (keyboard, mouse, etc.)</li>\n<li>Network cable</li>\n<li>Audio cables</li>\n</ul>\n</li>\n<li><p><strong>Remove External Components</strong>:</p>\n<ul>\n<li>Optical media from drives</li>\n<li>USB devices and SD cards</li>\n<li>Any attached accessories</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"step-2-case-opening\">Step 2: Case Opening</h3><ol>\n<li><p><strong>Side Panel Removal</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Most cases: Remove 2-4 thumbscrews on rear panel\nTool-free cases: Press tabs and slide panel\nOlder cases: Phillips screws on rear panel</code></pre></li>\n<li><p><strong>Access Verification</strong>:</p>\n<ul>\n<li>Ensure clear access to all components</li>\n<li>Remove both side panels if necessary</li>\n<li>Check for additional access panels</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"step-3-component-removal-order\">Step 3: Component Removal Order</h3><h4 id=\"power-supply-unit-if-replacement-needed\">Power Supply Unit (if replacement needed)</h4><ol>\n<li><p><strong>Disconnect All Power Cables</strong>:</p>\n<ul>\n<li>24-pin motherboard connector</li>\n<li>4/8-pin CPU power connector</li>\n<li>PCIe power connectors</li>\n<li>SATA power connectors</li>\n<li>Molex connectors</li>\n</ul>\n</li>\n<li><p><strong>Remove PSU</strong>:</p>\n<ul>\n<li>Unscrew 4 screws from rear of case</li>\n<li>Carefully remove unit</li>\n<li>Note orientation for reinstallation</li>\n</ul>\n</li>\n</ol>\n<h4 id=\"storage-drives\">Storage Drives</h4><ol>\n<li><p><strong>SATA/Power Cable Removal</strong>:</p>\n<ul>\n<li>Gently disconnect SATA data cables</li>\n<li>Remove SATA power connectors</li>\n<li>Note cable routing for reassembly</li>\n</ul>\n</li>\n<li><p><strong>Drive Removal</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>3.5&quot; HDDs: 4 screws from drive cage sides\n2.5&quot; SSDs: 4 screws from mounting bracket\nM.2 drives: Single screw and gentle lifting</code></pre></li>\n</ol>\n<h4 id=\"expansion-cards\">Expansion Cards</h4><ol>\n<li><strong>Remove in Order</strong>:<ul>\n<li>Unscrew bracket screws</li>\n<li>Unlock PCIe retention clips</li>\n<li>Carefully lift cards straight up</li>\n<li>Store in anti-static bags</li>\n</ul>\n</li>\n</ol>\n<h4 id=\"memory-ram\">Memory (RAM)</h4><ol>\n<li><strong>Removal Process</strong>:<ul>\n<li>Press down retention clips on both ends</li>\n<li>RAM modules will pop up at 30-degree angle</li>\n<li>Gently pull modules straight out</li>\n<li>Handle by edges only</li>\n</ul>\n</li>\n</ol>\n<h4 id=\"cpu-cooler-if-cleaningreplacing\">CPU Cooler (if cleaning/replacing)</h4><ol>\n<li><p><strong>Intel Socket Removal</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Push-pin coolers: Rotate pins 90° counterclockwise, pull up\nScrew-down coolers: Remove screws in X pattern</code></pre></li>\n<li><p><strong>AMD Socket Removal</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Clip retention: Lift retention arm, slide cooler off\nScrew-down: Remove screws in star pattern</code></pre></li>\n</ol>\n<p><strong>⚠️ Warning</strong>: CPU may stick to cooler due to thermal paste</p>\n<h2 id=\"laptop-disassembly\">Laptop Disassembly</h2><h3 id=\"step-1-external-preparation\">Step 1: External Preparation</h3><ol>\n<li><p><strong>Power Management</strong>:</p>\n<ul>\n<li>Shut down completely</li>\n<li>Remove battery (if removable)</li>\n<li>Disconnect AC adapter</li>\n<li>Press power button for 15 seconds</li>\n</ul>\n</li>\n<li><p><strong>External Cleaning</strong>:</p>\n<ul>\n<li>Remove any attached devices</li>\n<li>Clean exterior with damp microfiber cloth</li>\n<li>Pay attention to port areas</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"step-2-access-panel-removal\">Step 2: Access Panel Removal</h3><ol>\n<li><p><strong>Bottom Panel Access</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Modern laptops: Remove all visible screws from bottom\nOlder laptops: May have multiple access panels\nSome models: Require keyboard or palm rest removal first</code></pre></li>\n<li><p><strong>Screw Management</strong>:</p>\n<ul>\n<li>Use small containers for different screw types</li>\n<li>Some laptops use different screw lengths</li>\n<li>Take photos of screw locations</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"step-3-component-access\">Step 3: Component Access</h3><h4 id=\"memory-and-storage\">Memory and Storage</h4><ol>\n<li><p><strong>RAM Modules</strong>:</p>\n<ul>\n<li>Usually accessible through bottom panel</li>\n<li>Release clips and modules pop up at angle</li>\n<li>Remove carefully by pulling at edges</li>\n</ul>\n</li>\n<li><p><strong>Storage Drives</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>2.5&quot; SATA: Remove bracket screws, disconnect cable\nM.2 NVMe: Remove single screw, lift at angle\nmSATA: Similar to M.2 but smaller form factor</code></pre></li>\n</ol>\n<h4 id=\"advanced-disassembly-if-needed\">Advanced Disassembly (if needed)</h4><p><strong>Note</strong>: Only proceed if experienced with laptop repair</p>\n<ol>\n<li><p><strong>Keyboard Removal</strong>:</p>\n<ul>\n<li>Release clips or remove screws</li>\n<li>Disconnect ribbon cables carefully</li>\n<li>Some models require palm rest removal first</li>\n</ul>\n</li>\n<li><p><strong>Display Assembly</strong>:</p>\n<ul>\n<li>Remove hinge screws</li>\n<li>Disconnect display and Wi-Fi cables</li>\n<li>Carefully separate display assembly</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"cleaning-procedures\">Cleaning Procedures</h2><h3 id=\"compressed-air-cleaning\">Compressed Air Cleaning</h3><ol>\n<li><p><strong>Preparation</strong>:</p>\n<ul>\n<li>Take equipment outside or to well-ventilated area</li>\n<li>Wear safety glasses</li>\n<li>Hold compressed air cans upright</li>\n<li>Use short bursts (2-3 seconds)</li>\n</ul>\n</li>\n<li><p><strong>Cleaning Order</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>1. Power supply (if accessible)\n2. CPU heatsink and fan\n3. Graphics card heatsink and fan\n4. Case fans (hold fan blades while blowing)\n5. Motherboard components\n6. Expansion card slots\n7. Drive bays and connectors</code></pre></li>\n<li><p><strong>Fan Cleaning Protocol</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>⚠️ CRITICAL: Hold fan blades stationary while cleaning\n- Compressed air can spin fans beyond rated RPM\n- Over-spinning can damage bearings\n- Use cotton swab to hold fan center if needed</code></pre></li>\n</ol>\n<h3 id=\"detail-cleaning\">Detail Cleaning</h3><h4 id=\"motherboard-cleaning\">Motherboard Cleaning</h4><ol>\n<li><p><strong>Surface Cleaning</strong>:</p>\n<ul>\n<li>Use dry microfiber cloth for light dust</li>\n<li>Isopropyl alcohol for stubborn residue</li>\n<li>Cotton swabs for tight spaces</li>\n<li>Never use water or aggressive solvents</li>\n</ul>\n</li>\n<li><p><strong>Connector Cleaning</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>RAM slots: Compressed air, gentle brush\nPCIe slots: Compressed air only\nCPU socket: Compressed air, extreme care\nPower connectors: Isopropyl alcohol on contacts</code></pre></li>\n</ol>\n<h4 id=\"heat-sink-cleaning\">Heat Sink Cleaning</h4><ol>\n<li><p><strong>Dust Removal</strong>:</p>\n<ul>\n<li>Compressed air from multiple angles</li>\n<li>Brush stubborn dust buildup</li>\n<li>Ensure all fin passages are clear</li>\n</ul>\n</li>\n<li><p><strong>Thermal Paste Removal</strong> (if reseating CPU):</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Materials: Isopropyl alcohol 90%+, coffee filters\nProcess: \n1. Remove old paste with plastic scraper\n2. Clean with alcohol-soaked cloth\n3. Final cleaning with coffee filter\n4. Ensure completely dry before new paste</code></pre></li>\n</ol>\n<h3 id=\"case-and-external-cleaning\">Case and External Cleaning</h3><h4 id=\"interior-case-cleaning\">Interior Case Cleaning</h4><ol>\n<li><p><strong>Dust Removal</strong>:</p>\n<ul>\n<li>Compressed air for loose dust</li>\n<li>Microfiber cloth for surfaces</li>\n<li>Cotton swabs for corners and crevices</li>\n</ul>\n</li>\n<li><p><strong>Cable Management</strong>:</p>\n<ul>\n<li>Remove dust from cable bundles</li>\n<li>Reorganize for better airflow</li>\n<li>Replace damaged cable ties</li>\n</ul>\n</li>\n</ol>\n<h4 id=\"exterior-cleaning\">Exterior Cleaning</h4><ol>\n<li><p><strong>Plastic Surfaces</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Solution: Mild soap and water or isopropyl alcohol\nMethod: Damp cloth, not soaking wet\nDrying: Immediate drying with clean cloth</code></pre></li>\n<li><p><strong>Metal Surfaces</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Solution: Isopropyl alcohol 70%+\nMethod: Microfiber cloth application\nFinish: Dry immediately to prevent water spots</code></pre></li>\n</ol>\n<h2 id=\"component-testing-and-replacement\">Component Testing and Replacement</h2><h3 id=\"pre-cleaning-component-tests\">Pre-Cleaning Component Tests</h3><ol>\n<li><p><strong>Visual Inspection</strong>:</p>\n<ul>\n<li>Look for swollen capacitors</li>\n<li>Check for burn marks or discoloration</li>\n<li>Inspect cables for damage</li>\n<li>Verify fan blade integrity</li>\n</ul>\n</li>\n<li><p><strong>Basic Functionality</strong>:</p>\n<ul>\n<li>Test system boot before full disassembly</li>\n<li>Verify all fans spin up</li>\n<li>Check for unusual noises</li>\n<li>Monitor temperatures if possible</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"post-cleaning-verification\">Post-Cleaning Verification</h3><ol>\n<li><p><strong>Component Reseating</strong>:</p>\n<ul>\n<li>RAM: Firm, even pressure until clips engage</li>\n<li>PCIe cards: Push down until retention clip clicks</li>\n<li>Power connectors: Firm connection, retention clips engaged</li>\n<li>SATA cables: Gentle but secure connection</li>\n</ul>\n</li>\n<li><p><strong>Cable Management</strong>:</p>\n<ul>\n<li>Route cables away from fans</li>\n<li>Secure loose cables with ties</li>\n<li>Ensure no cables obstruct airflow</li>\n<li>Verify all connections are secure</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"bioscmos-reset-procedures\">BIOS/CMOS Reset Procedures</h2><h3 id=\"when-to-reset-bios\">When to Reset BIOS</h3><ul>\n<li>Unknown BIOS passwords</li>\n<li>Corrupted BIOS settings</li>\n<li>Hardware compatibility issues</li>\n<li>Instability after component changes</li>\n</ul>\n<h3 id=\"cmos-battery-replacement\">CMOS Battery Replacement</h3><ol>\n<li><p><strong>Battery Location</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Desktop: Usually visible on motherboard (CR2032)\nLaptop: May be under keyboard or accessible panel</code></pre></li>\n<li><p><strong>Replacement Process</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>1. Power off completely and unplug system\n2. Remove CMOS battery (note orientation)\n3. Wait 5-10 minutes for complete discharge\n4. Install new battery (match orientation)\n5. Power on and configure BIOS</code></pre></li>\n</ol>\n<h3 id=\"cmos-jumper-reset-desktop\">CMOS Jumper Reset (Desktop)</h3><ol>\n<li><p><strong>Locate CMOS Jumper</strong>:</p>\n<ul>\n<li>Usually labeled &quot;CLRTC&quot;, &quot;CLEAR&quot;, or &quot;JBAT1&quot;</li>\n<li>Near CMOS battery or BIOS chip</li>\n<li>Consult motherboard manual if uncertain</li>\n</ul>\n</li>\n<li><p><strong>Reset Process</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>1. Power off and unplug system\n2. Move jumper from pins 1-2 to pins 2-3\n3. Wait 10-15 seconds\n4. Return jumper to original position (1-2)\n5. Power on system</code></pre></li>\n</ol>\n<h3 id=\"alternative-reset-methods\">Alternative Reset Methods</h3><ol>\n<li><p><strong>Power Button Method</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>1. Unplug system completely\n2. Remove CMOS battery\n3. Hold power button for 30 seconds\n4. Reinstall battery and power up</code></pre></li>\n<li><p><strong>Motherboard Button</strong> (newer systems):</p>\n<ul>\n<li>Some boards have dedicated CMOS reset button</li>\n<li>Press button while system is powered off</li>\n<li>No jumper manipulation required</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"quality-control-checklist\">Quality Control Checklist</h2><h3 id=\"pre-reassembly-verification\">Pre-Reassembly Verification</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> All components thoroughly cleaned</li>\n<li><input disabled=\"\" type=\"checkbox\"> No damaged components identified</li>\n<li><input disabled=\"\" type=\"checkbox\"> Thermal paste replaced (if CPU removed)</li>\n<li><input disabled=\"\" type=\"checkbox\"> All cables and connectors clean</li>\n<li><input disabled=\"\" type=\"checkbox\"> No foreign objects left in case</li>\n<li><input disabled=\"\" type=\"checkbox\"> Anti-static precautions maintained</li>\n</ul>\n<h3 id=\"reassembly-checklist\">Reassembly Checklist</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Components installed in reverse order of removal</li>\n<li><input disabled=\"\" type=\"checkbox\"> All screws properly tightened (not over-tightened)</li>\n<li><input disabled=\"\" type=\"checkbox\"> Cables routed for optimal airflow</li>\n<li><input disabled=\"\" type=\"checkbox\"> No loose cables near fans</li>\n<li><input disabled=\"\" type=\"checkbox\"> All power connections secure</li>\n<li><input disabled=\"\" type=\"checkbox\"> RAM and expansion cards fully seated</li>\n</ul>\n<h3 id=\"initial-power-on-test\">Initial Power-On Test</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> System powers on normally</li>\n<li><input disabled=\"\" type=\"checkbox\"> All fans spinning (CPU, case, GPU)</li>\n<li><input disabled=\"\" type=\"checkbox\"> No unusual noises or smells</li>\n<li><input disabled=\"\" type=\"checkbox\"> POST completes successfully</li>\n<li><input disabled=\"\" type=\"checkbox\"> All components detected in BIOS</li>\n<li><input disabled=\"\" type=\"checkbox\"> Temperature readings normal</li>\n</ul>\n<h2 id=\"common-issues-and-solutions\">Common Issues and Solutions</h2><h3 id=\"system-won39t-power-on\">System Won&#39;t Power On</h3><p><strong>Possible Causes</strong>:</p>\n<ul>\n<li>Loose power connections</li>\n<li>Incorrectly seated RAM</li>\n<li>Disconnected front panel connectors</li>\n<li>Power supply issues</li>\n</ul>\n<p><strong>Solutions</strong>:</p>\n<ol>\n<li>Verify all power connections</li>\n<li>Reseat RAM modules</li>\n<li>Check front panel connector alignment</li>\n<li>Test with minimal configuration</li>\n</ol>\n<h3 id=\"overheating-after-cleaning\">Overheating After Cleaning</h3><p><strong>Possible Causes</strong>:</p>\n<ul>\n<li>Thermal paste not applied properly</li>\n<li>CPU cooler not properly seated</li>\n<li>Fans not connected</li>\n<li>Blocked airflow paths</li>\n</ul>\n<p><strong>Solutions</strong>:</p>\n<ol>\n<li>Verify CPU cooler mounting</li>\n<li>Check thermal paste application</li>\n<li>Confirm all fan connections</li>\n<li>Verify case fan orientation</li>\n</ol>\n<h3 id=\"boot-issues\">Boot Issues</h3><p><strong>Possible Causes</strong>:</p>\n<ul>\n<li>BIOS reset required</li>\n<li>Boot device not detected</li>\n<li>Loose storage connections</li>\n<li>Corrupted boot sector</li>\n</ul>\n<p><strong>Solutions</strong>:</p>\n<ol>\n<li>Reset BIOS to defaults</li>\n<li>Verify storage device connections</li>\n<li>Check boot order in BIOS</li>\n<li>Test with different storage device</li>\n</ol>\n<h2 id=\"documentation-and-labeling\">Documentation and Labeling</h2><h3 id=\"system-documentation\">System Documentation</h3><ol>\n<li><p><strong>Create Service Record</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>System Model: _______________\nSerial Number: ______________\nDate Cleaned: ______________\nTechnician: ________________\nComponents Replaced: ________\nIssues Found: ______________</code></pre></li>\n<li><p><strong>Photo Documentation</strong>:</p>\n<ul>\n<li>Before and after cleaning photos</li>\n<li>Any damage or wear documentation</li>\n<li>Component configuration photos</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"component-tracking\">Component Tracking</h3><ol>\n<li><p><strong>Inventory Management</strong>:</p>\n<ul>\n<li>Label removed components</li>\n<li>Track component serial numbers</li>\n<li>Note compatibility information</li>\n<li>Record performance test results</li>\n</ul>\n</li>\n<li><p><strong>Configuration Notes</strong>:</p>\n<ul>\n<li>BIOS settings modifications</li>\n<li>Hardware configuration changes</li>\n<li>Driver requirements</li>\n<li>Known compatibility issues</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"final-preparation-steps\">Final Preparation Steps</h2><h3 id=\"system-optimization\">System Optimization</h3><ol>\n<li><p><strong>BIOS Configuration</strong>:</p>\n<ul>\n<li>Enable optimal settings for server use</li>\n<li>Configure boot priorities</li>\n<li>Enable virtualization features</li>\n<li>Set power management options</li>\n</ul>\n</li>\n<li><p><strong>Hardware Burn-In</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>CPU Stress Test: 4-8 hours at 100% load\nMemory Test: Full MemTest86+ pass\nStorage Test: Read/write endurance test\nTemperature Monitoring: Throughout all tests</code></pre></li>\n</ol>\n<h3 id=\"deployment-preparation\">Deployment Preparation</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Operating system installation media prepared</li>\n<li><input disabled=\"\" type=\"checkbox\"> Driver packages downloaded</li>\n<li><input disabled=\"\" type=\"checkbox\"> Network configuration planned</li>\n<li><input disabled=\"\" type=\"checkbox\"> Security settings documented</li>\n<li><input disabled=\"\" type=\"checkbox\"> Backup and recovery procedures planned</li>\n</ul>\n<h2 id=\"next-steps\">Next Steps</h2><ol>\n<li><strong>BIOS Setup</strong>: Proceed to <a href=\"bios-access-and-setup.md\" target=\"_blank\">BIOS Access and Setup</a></li>\n<li><strong>Compatibility Verification</strong>: Review <a href=\"hardware-compatibility-check.md\" target=\"_blank\">Hardware Compatibility Check</a></li>\n<li><strong>System Requirements</strong>: Confirm <a href=\"recycled-pc-requirements.md\" target=\"_blank\">Recycled PC Requirements</a></li>\n<li><strong>Model Selection</strong>: Reference <a href=\"recycled-models-comparison.md\" target=\"_blank\">Recycled Models Comparison</a></li>\n</ol>\n<hr>\n<p><em>This preparation guide ensures recycled computers are properly cleaned, tested, and ready for reliable server deployment. Always prioritize safety and take time to do the job correctly.</em></p>\n"}, "hardware/intel_nuc/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "hardware/raspberry_pi_5/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "hardware/zimaboard/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "software/balena/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "software/caprover/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "software/casaos/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "software/yunohost/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "software/zimaos/README.md": {"hash": "d41d8cd98f00b204e9800998ecf8427e", "content": ""}, "guides/router-selection-guide.md": {"hash": "cc693bc6ff48bc1a39428bdc5edde507", "content": "<h1 id=\"router-selection-guide-for-libremesh-community-networks\">Router Selection Guide for LibreMesh Community Networks</h1><p>This guide helps you evaluate and select routers that are compatible and reliable for LibreMesh firmware installation and community networking purposes. Making the right hardware choice is crucial for a successful community network deployment.</p>\n<h2 id=\"quick-decision-matrix\">Quick Decision Matrix</h2><p>Before diving into detailed specifications, use this matrix to quickly determine if a router is suitable for your LibreMesh deployment:</p>\n<table class=\"pdf-table\">\n        <thead><tr>\n<th>Requirement</th>\n<th>Minimum</th>\n<th>Recommended</th>\n<th>Ideal</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Flash Memory</td>\n<td>8 MB</td>\n<td>16 MB</td>\n<td>32+ MB</td>\n</tr>\n<tr>\n<td>RAM</td>\n<td>64 MB</td>\n<td>128 MB</td>\n<td>256+ MB</td>\n</tr>\n<tr>\n<td>Radios</td>\n<td>1 (2.4 GHz)</td>\n<td>2 (2.4 + 5 GHz)</td>\n<td>3+ (Multiple 5 GHz)</td>\n</tr>\n<tr>\n<td>Ethernet Ports</td>\n<td>1 LAN</td>\n<td>4 LAN + 1 WAN</td>\n<td>5+ Gigabit</td>\n</tr>\n<tr>\n<td>Chipset</td>\n<td>Any OpenWrt-supported</td>\n<td>Atheros/MediaTek</td>\n<td>Atheros ath9k/ath10k</td>\n</tr>\n</tbody>\n      </table><p><em><code>[Router Hardware Lineup](router-lineup.png)</code> — Photo showcasing several recommended router models side-by-side, including TP-Link, GL.iNet, and Asus models.</em></p>\n<h2 id=\"hardware-requirements-deep-dive\">Hardware Requirements Deep Dive</h2><h3 id=\"memory-specifications\">Memory Specifications</h3><p><strong>Flash Memory (Storage)</strong></p>\n<ul>\n<li><strong>8 MB minimum</strong>: Allows basic LibreMesh installation with <code>-mini</code> firmware</li>\n<li><strong>16 MB recommended</strong>: Standard LibreMesh with full feature set</li>\n<li><strong>32+ MB ideal</strong>: Enables additional packages, extensive logging, and future upgrades</li>\n</ul>\n<p><strong>RAM (System Memory)</strong></p>\n<ul>\n<li><strong>64 MB minimum</strong>: Basic mesh operation with limited concurrent connections</li>\n<li><strong>128 MB recommended</strong>: Stable performance with 20-50 devices</li>\n<li><strong>256+ MB ideal</strong>: High-capacity nodes supporting 100+ devices and advanced features</li>\n</ul>\n<p><em><code>[Specs Label Closeup](router-specs-label.png)</code> — Close-up of a router&#39;s sticker showing relevant specs/labels including memory specifications and model information.</em></p>\n<blockquote>\n<p><strong>💡 Tip</strong>: Look for the specifications label on the bottom or back of the router. Memory specifications are often listed as &quot;Flash: XXX MB&quot; and &quot;RAM: XXX MB&quot;.</p>\n</blockquote>\n<h3 id=\"wireless-radio-requirements\">Wireless Radio Requirements</h3><p><strong>Essential Radio Configuration</strong></p>\n<ul>\n<li><strong>2.4 GHz radio</strong>: Required for client access and mesh backhaul</li>\n<li><strong>5 GHz radio</strong>: Strongly recommended for high-speed mesh backhaul</li>\n<li><strong>Multiple 5 GHz radios</strong>: Ideal for dedicated backhaul and client access separation</li>\n</ul>\n<p><strong>Supported Chipsets (Priority Order)</strong></p>\n<ol>\n<li><strong>Atheros ath9k</strong> (AR9xxx series): Best LibreMesh compatibility</li>\n<li><strong>Atheros ath10k</strong> (QCA9xxx series): Good performance, wider Wi-Fi 6 support</li>\n<li><strong>MediaTek MT76xx</strong>: Reliable alternative with good price/performance ratio</li>\n<li><strong>Broadcom</strong>: Limited LibreMesh support, avoid if possible</li>\n</ol>\n<blockquote>\n<p><strong>⚠️ Warning</strong>: Avoid routers with Realtek wireless chipsets as they have poor LibreMesh/OpenWrt support.</p>\n</blockquote>\n<h3 id=\"network-interface-requirements\">Network Interface Requirements</h3><p><strong>Ethernet Connectivity</strong></p>\n<ul>\n<li><strong>Minimum</strong>: 1 LAN port for configuration and local connections</li>\n<li><strong>Standard</strong>: 4 LAN ports + 1 WAN port for typical installations</li>\n<li><strong>Advanced</strong>: Gigabit ports with PoE support for professional deployments</li>\n</ul>\n<p><strong>Special Considerations</strong></p>\n<ul>\n<li><strong>WAN port flexibility</strong>: Some single-port devices can be configured with software WAN</li>\n<li><strong>PoE capability</strong>: Essential for outdoor installations and remote powering</li>\n<li><strong>Switch integration</strong>: Built-in switches reduce equipment requirements</li>\n</ul>\n<h2 id=\"recommended-router-models-by-category\">Recommended Router Models by Category</h2><h3 id=\"budget-friendly-options-30-60\">Budget-Friendly Options ($30-60)</h3><p><strong>TP-Link Archer C7 v2/v5</strong></p>\n<ul>\n<li>Flash: 16 MB | RAM: 128 MB</li>\n<li>Dual-band AC1750 (2.4GHz + 5GHz)</li>\n<li>Atheros QCA9558 + QCA9880 chipset</li>\n<li>Pros: Excellent OpenWrt support, proven reliability</li>\n<li>Cons: Older Wi-Fi 5 standard</li>\n</ul>\n<p><strong>GL.iNet GL-AR300M16</strong></p>\n<ul>\n<li>Flash: 16 MB | RAM: 128 MB  </li>\n<li>Single-band N300 (2.4GHz only)</li>\n<li>Atheros AR9331 chipset</li>\n<li>Pros: Compact size, pre-installed OpenWrt</li>\n<li>Cons: Single radio limits mesh performance</li>\n</ul>\n<h3 id=\"mid-range-options-60-120\">Mid-Range Options ($60-120)</h3><p><strong>TP-Link Archer C6 v3.x</strong></p>\n<ul>\n<li>Flash: 16 MB | RAM: 128 MB</li>\n<li>Dual-band AC1200 (2.4GHz + 5GHz)</li>\n<li>MediaTek MT7621A + MT7603E/MT7613BE</li>\n<li>Pros: Good price-performance, stable chipset</li>\n<li>Cons: Requires careful version checking</li>\n</ul>\n<p><strong>GL.iNet Flint (GL-AX1800)</strong></p>\n<ul>\n<li>Flash: 128 MB | RAM: 512 MB</li>\n<li>Dual-band AX1800 Wi-Fi 6</li>\n<li>Qualcomm IPQ6010 chipset</li>\n<li>Pros: Wi-Fi 6 support, large memory</li>\n<li>Cons: Newer chipset with limited community testing</li>\n</ul>\n<h3 id=\"professionalhigh-performance-120\">Professional/High-Performance ($120+)</h3><p><strong>Ubiquiti Rocket M2/M5</strong></p>\n<ul>\n<li>Flash: 8 MB | RAM: 64 MB</li>\n<li>Single-band dedicated mesh radios</li>\n<li>Atheros AR7241 + AR9280 chipset</li>\n<li>Pros: Purpose-built for WISP/mesh, weatherproof</li>\n<li>Cons: Requires separate access point for clients</li>\n</ul>\n<p><strong>ASUS TUF-AX6000</strong></p>\n<ul>\n<li>Flash: 256 MB | RAM: 1 GB</li>\n<li>Tri-band AX6000 Wi-Fi 6</li>\n<li>MediaTek MT7986A Filogic chipset</li>\n<li>Pros: Enterprise-grade performance, extensive memory</li>\n<li>Cons: Higher power consumption, complex configuration</li>\n</ul>\n<h3 id=\"outdoorspecialized-options\">Outdoor/Specialized Options</h3><p><strong>Ubiquiti NanoStation M2/M5</strong></p>\n<ul>\n<li>Flash: 8 MB | RAM: 32 MB</li>\n<li>Point-to-point/point-to-multipoint links</li>\n<li>Atheros AR7240 + AR9280 chipset</li>\n<li>Pros: Weatherproof, integrated antenna, long range</li>\n<li>Cons: Limited for general-purpose routing</li>\n</ul>\n<h2 id=\"brand-specific-considerations\">Brand-Specific Considerations</h2><h3 id=\"tp-link-devices\">TP-Link Devices</h3><p><strong>Advantages:</strong></p>\n<ul>\n<li>Wide OpenWrt/LibreMesh compatibility</li>\n<li>Cost-effective options across all price ranges</li>\n<li>Good availability worldwide</li>\n</ul>\n<p><strong>Considerations:</strong></p>\n<ul>\n<li><strong>FCC Lock bypass</strong>: Newer models may require DD-WRT intermediate step</li>\n<li><strong>Version checking</strong>: Hardware revisions can vary significantly</li>\n<li><strong>Firmware naming</strong>: Factory images must match exact official naming</li>\n</ul>\n<blockquote>\n<p><strong>⚠️ FCC Lock Warning</strong>: TP-Link devices manufactured after 2016 may have firmware restrictions. Look for &quot;18005&quot; or &quot;Invalid Filename&quot; errors during flashing, which indicate FCC lock protection.</p>\n</blockquote>\n<h3 id=\"glinet-devices\">GL.iNet Devices</h3><p><strong>Advantages:</strong></p>\n<ul>\n<li>Pre-installed OpenWrt with easy LibreMesh upgrade path</li>\n<li>Compact travel-friendly form factors</li>\n<li>Good documentation and community support</li>\n</ul>\n<p><strong>Considerations:</strong></p>\n<ul>\n<li><strong>Custom OpenWrt</strong>: May use older or modified OpenWrt versions</li>\n<li><strong>Limited outdoor options</strong>: Most models designed for indoor use</li>\n<li><strong>Price premium</strong>: Often more expensive than equivalent TP-Link options</li>\n</ul>\n<h3 id=\"asus-devices\">ASUS Devices</h3><p><strong>Advantages:</strong></p>\n<ul>\n<li>High-performance hardware with generous memory</li>\n<li>Good build quality and reliability</li>\n<li>Advanced networking features</li>\n</ul>\n<p><strong>Considerations:</strong></p>\n<ul>\n<li><strong>Complex installation</strong>: May require ASUS-specific flashing procedures</li>\n<li><strong>Higher cost</strong>: Premium pricing compared to alternatives</li>\n<li><strong>Power requirements</strong>: Often require more power than budget options</li>\n</ul>\n<h3 id=\"ubiquiti-devices\">Ubiquiti Devices</h3><p><strong>Advantages:</strong></p>\n<ul>\n<li>Purpose-built for wireless networking</li>\n<li>Weatherproof outdoor options</li>\n<li>Excellent long-range performance</li>\n</ul>\n<p><strong>Considerations:</strong></p>\n<ul>\n<li><strong>AirOS compatibility</strong>: Avoid AirOS 5.6.x (requires downgrade)</li>\n<li><strong>Limited routing</strong>: Some models designed as bridges/access points only</li>\n<li><strong>Professional focus</strong>: May be overkill for small deployments</li>\n</ul>\n<h2 id=\"router-selection-decision-tree\">Router Selection Decision Tree</h2><h3 id=\"for-indoor-homeoffice-networks\">For Indoor Home/Office Networks</h3><ol>\n<li><strong>Small (1-10 devices)</strong>: GL.iNet GL-AR300M16 or TP-Link Archer C7</li>\n<li><strong>Medium (10-30 devices)</strong>: TP-Link Archer C6 v3 or GL.iNet Flint</li>\n<li><strong>Large (30+ devices)</strong>: ASUS TUF-AX6000 or multiple coordinated nodes</li>\n</ol>\n<h3 id=\"for-outdoor-community-networks\">For Outdoor Community Networks</h3><ol>\n<li><strong>Point-to-point links</strong>: Ubiquiti NanoStation M2/M5</li>\n<li><strong>Mesh backhaul</strong>: Ubiquiti Rocket M2/M5 with sector antennas</li>\n<li><strong>Client access</strong>: TP-Link outdoor models with weatherproof enclosures</li>\n</ol>\n<h3 id=\"for-mixed-indooroutdoor-deployments\">For Mixed Indoor/Outdoor Deployments</h3><ol>\n<li><strong>Backbone nodes</strong>: High-performance indoor routers with good cooling</li>\n<li><strong>Extension nodes</strong>: Weatherproof outdoor units</li>\n<li><strong>Client access</strong>: Strategically placed indoor access points</li>\n</ol>\n<h2 id=\"pre-purchase-checklist\">Pre-Purchase Checklist</h2><p>Before purchasing any router for LibreMesh use:</p>\n<ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Verify OpenWrt compatibility</strong> on the official Table of Hardware</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Check hardware revision</strong> matches supported versions exactly</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Confirm memory specifications</strong> meet your deployment requirements</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Research installation complexity</strong> for your specific model/revision</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Check antenna connector types</strong> if external antennas are needed</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Verify power requirements</strong> and PoE compatibility if needed</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Read recent community reports</strong> on LibreMesh mailing lists/forums</li>\n</ul>\n<h2 id=\"common-pitfalls-to-avoid\">Common Pitfalls to Avoid</h2><h3 id=\"hardware-selection-mistakes\">Hardware Selection Mistakes</h3><ul>\n<li><strong>Insufficient memory</strong>: 4MB flash or 32MB RAM severely limits functionality</li>\n<li><strong>Wrong hardware revision</strong>: Different revisions may have incompatible installation procedures</li>\n<li><strong>Unsupported chipsets</strong>: Realtek or proprietary wireless chipsets</li>\n<li><strong>Single-band limitation</strong>: 2.4GHz-only routers limit mesh performance</li>\n</ul>\n<h3 id=\"purchasing-considerations\">Purchasing Considerations</h3><ul>\n<li><strong>Version confusion</strong>: Ensure exact model and revision compatibility</li>\n<li><strong>Firmware lock</strong>: Newer devices may have installation restrictions</li>\n<li><strong>Antenna limitations</strong>: Non-removable antennas limit deployment flexibility</li>\n<li><strong>Power supply</strong>: Missing or incompatible power adapters</li>\n</ul>\n<h2 id=\"next-steps\">Next Steps</h2><p>Once you&#39;ve selected appropriate hardware:</p>\n<ol>\n<li><strong>Proceed to <a href=\"libremesh-installation.md\" target=\"_blank\">LibreMesh Installation Guide</a></strong> for firmware flashing procedures</li>\n<li><strong>Review <a href=\"router-troubleshooting.md\" target=\"_blank\">Router Troubleshooting Guide</a></strong> for recovery procedures</li>\n<li><strong>Plan your network topology</strong> considering radio capabilities and coverage requirements</li>\n<li><strong>Prepare installation tools</strong> including appropriate cables and software</li>\n</ol>\n<blockquote>\n<p><strong>📖 Related Reading</strong>: For detailed installation instructions specific to your chosen router model, consult the LibreMesh hardware documentation and OpenWrt device pages before beginning firmware installation.</p>\n</blockquote>\n"}, "guides/router-troubleshooting.md": {"hash": "78fe5b63a373ca95bd9e7152974bc60a", "content": "<h1 id=\"router-troubleshooting-guide\">Router Troubleshooting Guide</h1><p>This guide provides systematic troubleshooting procedures for common router setup and flashing problems including incomplete flash, unresponsive devices, firmware restoration, physical reset practices, and advanced serial recovery methods.</p>\n<h2 id=\"emergency-response-quick-reference\">Emergency Response Quick Reference</h2><h3 id=\"immediate-actions-for-bricked-router\">Immediate Actions for Bricked Router</h3><p><strong>If your router appears &quot;bricked&quot; (unresponsive):</strong></p>\n<ol>\n<li><strong>Stay calm</strong>: Most situations are recoverable</li>\n<li><strong>Do not power cycle repeatedly</strong>: This can worsen the situation</li>\n<li><strong>Check LED patterns</strong>: Note current behavior for diagnosis</li>\n<li><strong>Verify power supply</strong>: Ensure adequate voltage and current</li>\n<li><strong>Try hardware reset</strong>: Follow brand-specific procedures below</li>\n</ol>\n<blockquote>\n<p><strong>⚠️ Important</strong>: Never attempt multiple recovery methods simultaneously. Complete one method fully before trying another.</p>\n</blockquote>\n<h2 id=\"hardware-reset-procedures\">Hardware Reset Procedures</h2><h3 id=\"universal-reset-button-method\">Universal Reset Button Method</h3><p><em><code>[Reset Button Location](router-reset-button.png)</code> — Highlighted photo of a typical hardware reset button on a router, showing button location and surrounding indicators.</em></p>\n<p><strong>Basic Reset Procedure</strong>:</p>\n<ol>\n<li><strong>Locate reset button</strong>: Usually recessed hole labeled &quot;RESET&quot;</li>\n<li><strong>Prepare tool</strong>: Paperclip, pin, or similar pointed object</li>\n<li><strong>Power on router</strong>: Ensure device is fully powered</li>\n<li><strong>Press and hold</strong>: Insert tool and press button firmly</li>\n<li><strong>Hold duration</strong>: 10-30 seconds depending on manufacturer</li>\n<li><strong>Release and wait</strong>: Allow router to complete reboot cycle</li>\n</ol>\n<h3 id=\"brand-specific-reset-procedures\">Brand-Specific Reset Procedures</h3><p><strong>TP-Link Routers</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Power State: ON\nButton: WPS/RESET\nDuration: 10+ seconds\nIndicator: SYS LED changes from slow-flash to quick-flash\nAction: Release button when LED pattern changes</code></pre><p><strong>ASUS Routers</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Power State: ON\nButton: RESET (rear panel)\nDuration: 5-10 seconds\nIndicator: Power LED flashes rapidly\nAction: Release when LED behavior changes</code></pre><p><strong>GL.iNet Routers</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Power State: ON\nButton: RESET\nDuration: 10 seconds\nIndicator: LED turns off briefly then solid\nAction: Release when LED stabilizes</code></pre><p><strong>Ubiquiti Devices</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Power State: Power on while holding\nButton: RESET\nDuration: Hold during power-on for 10 seconds\nIndicator: LED sequence changes\nSpecial: For TFTP recovery, continue holding until specific LED pattern</code></pre><p><strong>Netgear Routers</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Power State: ON\nButton: RESET (rear or side)\nDuration: 7+ seconds\nIndicator: Power LED changes color or pattern\nAction: Release when LED indicates factory reset mode</code></pre><h3 id=\"advanced-reset-modes\">Advanced Reset Modes</h3><p><strong>RouterBOARD/MikroTik Reset Functions</strong>:</p>\n<ul>\n<li><strong>3 seconds</strong>: Load backup bootloader</li>\n<li><strong>Hold until LED flashes</strong>: Reset configuration to default</li>\n<li><strong>+5 seconds (LED solid)</strong>: Enable CAPs mode</li>\n<li><strong>+5 more seconds (LED off)</strong>: Netinstall mode</li>\n</ul>\n<h2 id=\"tftp-recovery-procedures\">TFTP Recovery Procedures</h2><h3 id=\"tftp-recovery-setup\">TFTP Recovery Setup</h3><p><strong>Prerequisites</strong>:</p>\n<ul>\n<li><strong>TFTP server software</strong>: PumpKIN (Windows), tftpd (Linux/macOS)</li>\n<li><strong>Recovery firmware</strong>: Appropriate factory firmware image</li>\n<li><strong>Network configuration</strong>: Static IP in router&#39;s subnet</li>\n<li><strong>Direct connection</strong>: Ethernet cable between computer and router</li>\n</ul>\n<p><strong>Network Configuration</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Computer IP: ************ (or ************)\nSubnet Mask: *************\nGateway: (leave blank)\nRouter IP: *********** (or ***********)</code></pre><h3 id=\"tftp-recovery-execution\">TFTP Recovery Execution</h3><p><strong>Standard TFTP Recovery Process</strong>:</p>\n<ol>\n<li><p><strong>Prepare TFTP server</strong>:</p>\n<ul>\n<li>Install and configure TFTP server software</li>\n<li>Place recovery firmware in TFTP root directory</li>\n<li>Start TFTP service</li>\n</ul>\n</li>\n<li><p><strong>Configure network</strong>:</p>\n<ul>\n<li>Set computer IP to appropriate subnet</li>\n<li>Disable firewall temporarily</li>\n<li>Ensure no other DHCP servers active</li>\n</ul>\n</li>\n<li><p><strong>Initiate recovery mode</strong>:</p>\n<ul>\n<li>Power off router completely</li>\n<li>Hold reset button firmly</li>\n<li>Apply power while continuing to hold reset</li>\n<li>Watch for TFTP request in server logs</li>\n</ul>\n</li>\n<li><p><strong>File transfer</strong>:</p>\n<ul>\n<li>Router requests specific filename (often &quot;recovery.bin&quot;)</li>\n<li>TFTP server transfers firmware automatically</li>\n<li>Wait for transfer completion (usually 30-60 seconds)</li>\n</ul>\n</li>\n<li><p><strong>Installation</strong>:</p>\n<ul>\n<li>Router flashes firmware internally</li>\n<li>Wait 2-5 minutes for installation</li>\n<li>Device reboots to factory firmware</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"brand-specific-tftp-procedures\">Brand-Specific TFTP Procedures</h3><p><strong>TP-Link TFTP Recovery</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Firmware Name: &quot;recovery.bin&quot; (rename your firmware file)\nRouter IP: ***********\nComputer IP: ************\nHold Reset: During power-on until transfer starts\nExpected Duration: 30-45 seconds transfer, 2-3 minutes total</code></pre><p><strong>Ubiquiti TFTP Recovery</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Firmware Name: Original Ubiquiti firmware filename\nRouter IP: ***********0\nComputer IP: 192.168.1.xxx (not .20)\nHold Reset: 20+ seconds during power-on\nLED Pattern: Alternating 2-LED flash pattern indicates ready</code></pre><p><strong>Netgear TFTP Recovery</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Firmware Name: &quot;recovery.img&quot; or device-specific name\nRouter IP: ***********\nComputer IP: ***********\nHold Reset: During power-on until Power LED turns amber\nIndicator: Amber power LED indicates TFTP mode active</code></pre><h2 id=\"serial-console-recovery\">Serial Console Recovery</h2><h3 id=\"when-to-use-serial-console\">When to Use Serial Console</h3><p><strong>Serial console recovery is necessary when</strong>:</p>\n<ul>\n<li>TFTP recovery fails</li>\n<li>Bootloader is corrupted</li>\n<li>No network connectivity available</li>\n<li>Advanced debugging required</li>\n</ul>\n<blockquote>\n<p><strong>⚠️ Advanced Procedure</strong>: Serial console recovery requires hardware modification and advanced technical skills. Proceed only if comfortable with electronics work.</p>\n</blockquote>\n<h3 id=\"serial-console-hardware-setup\">Serial Console Hardware Setup</h3><p><em><code>[Serial Console Hookup](router-serial-console.png)</code> — Photo or diagram of attaching serial cables for advanced recovery, showing connection points and cable routing.</em></p>\n<p><strong>Required Equipment</strong>:</p>\n<ul>\n<li><strong>USB-to-TTL adapter</strong>: 3.3V logic levels (NOT RS232)</li>\n<li><strong>Jumper wires</strong>: Female-to-male or appropriate connectors</li>\n<li><strong>Soldering equipment</strong>: If header installation required</li>\n<li><strong>Multimeter</strong>: For voltage verification</li>\n</ul>\n<p><strong>Connection Mapping</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>Router          USB-TTL Adapter\nGND     &lt;---&gt;   GND\nTX      &lt;---&gt;   RX\nRX      &lt;---&gt;   TX\nVCC     &lt;---&gt;   (usually not connected)</code></pre><blockquote>\n<p><strong>🔌 Voltage Warning</strong>: Ensure USB-TTL adapter operates at 3.3V. 5V levels can damage router circuitry permanently.</p>\n</blockquote>\n<h3 id=\"serial-console-access\">Serial Console Access</h3><p><strong>Software Configuration</strong>:</p>\n<ul>\n<li><strong>Baud Rate</strong>: 115200 (most common)</li>\n<li><strong>Data Bits</strong>: 8</li>\n<li><strong>Parity</strong>: None</li>\n<li><strong>Stop Bits</strong>: 1</li>\n<li><strong>Flow Control</strong>: None</li>\n</ul>\n<p><strong>Terminal Software Options</strong>:</p>\n<ul>\n<li><strong>Windows</strong>: PuTTY, TeraTerm</li>\n<li><strong>macOS</strong>: Screen, Minicom</li>\n<li><strong>Linux</strong>: Minicom, Screen, Picocom</li>\n</ul>\n<p><strong>Connection Commands</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Linux/macOS\nscreen /dev/ttyUSB0 115200\n\n# Alternative Linux\nminicom -D /dev/ttyUSB0 -b 115200\n\n# Windows (use PuTTY GUI or)\nputty -serial COM3 -sercfg 115200,8,n,1,N</code></pre><h3 id=\"bootloader-intervention\">Bootloader Intervention</h3><p><strong>Common Bootloader Prompts</strong>:</p>\n<ul>\n<li><strong>U-Boot</strong>: &quot;Hit any key to stop autoboot&quot;</li>\n<li><strong>CFE</strong>: &quot;CFE&gt;&quot; prompt</li>\n<li><strong>RedBoot</strong>: &quot;RedBoot&gt;&quot; prompt</li>\n</ul>\n<p><strong>Typical Recovery Commands</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># U-Boot TFTP recovery\nsetenv ipaddr ***********\nsetenv serverip ***********00\ntftp 0x80060000 firmware.bin\nerase 0x9f020000 +0x7e0000\ncp.b 0x80060000 0x9f020000 $filesize\nbootm 0x9f020000\n\n# Reset environment variables\nenv default -a\nsaveenv\nreset</code></pre><h2 id=\"firmware-restoration-procedures\">Firmware Restoration Procedures</h2><h3 id=\"restoring-original-firmware\">Restoring Original Firmware</h3><p><em><code>[Restoring Firmware Process](restoring-firmware.png)</code> — Series or diagram showing the steps of recovering from a failed install, including cable connections and interface screenshots.</em></p>\n<p><strong>When to Restore Original Firmware</strong>:</p>\n<ul>\n<li>LibreMesh installation failed</li>\n<li>Device performance issues</li>\n<li>Warranty considerations</li>\n<li>Selling/returning device</li>\n</ul>\n<p><strong>Restoration Methods Priority</strong>:</p>\n<ol>\n<li><strong>Web interface upgrade</strong>: If LibreMesh/OpenWrt still accessible</li>\n<li><strong>TFTP recovery</strong>: Using original factory firmware</li>\n<li><strong>Serial console</strong>: For severe bootloader issues</li>\n<li><strong>Professional service</strong>: If above methods fail</li>\n</ol>\n<h3 id=\"stock-firmware-acquisition\">Stock Firmware Acquisition</h3><p><strong>Official Sources</strong>:</p>\n<ul>\n<li><strong>Manufacturer website</strong>: Downloads/support section</li>\n<li><strong>Device model page</strong>: Exact firmware for your revision</li>\n<li><strong>Version matching</strong>: Ensure firmware matches hardware revision exactly</li>\n</ul>\n<p><strong>Firmware Selection</strong>:</p>\n<ul>\n<li><strong>Factory firmware</strong>: For complete restoration</li>\n<li><strong>Recovery firmware</strong>: Special minimal versions for TFTP</li>\n<li><strong>Regional versions</strong>: Match original firmware region if specified</li>\n</ul>\n<h2 id=\"common-problem-diagnosis\">Common Problem Diagnosis</h2><h3 id=\"led-pattern-analysis\">LED Pattern Analysis</h3><p><strong>Normal Boot Patterns</strong>:</p>\n<ul>\n<li><strong>Power on</strong>: All LEDs illuminate briefly</li>\n<li><strong>Boot process</strong>: Sequential LED activation</li>\n<li><strong>Normal operation</strong>: Steady power, wireless, and ethernet LEDs</li>\n</ul>\n<p><strong>Problem Indicators</strong>:</p>\n<ul>\n<li><strong>All LEDs off</strong>: Power supply failure</li>\n<li><strong>Power LED only</strong>: Boot failure, try reset</li>\n<li><strong>Rapid flashing</strong>: Often indicates recovery mode</li>\n<li><strong>Alternating patterns</strong>: May indicate TFTP mode</li>\n<li><strong>Continuous boot loops</strong>: Corrupted firmware</li>\n</ul>\n<h3 id=\"network-connectivity-tests\">Network Connectivity Tests</h3><p><strong>Basic Connectivity Verification</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Check network interface status\nip link show                    # Linux\nipconfig /all                   # Windows\nifconfig -a                     # macOS\n\n# Test router connectivity\nping ***********\nping ***********\nping thisnode.info\n\n# Check routing table\nip route                        # Linux\nroute print                     # Windows\nnetstat -rn                     # macOS\n\n# Scan for devices\nnmap -sn ***********/24        # Common router subnet\narp -a                          # Show known devices</code></pre><h3 id=\"memory-and-performance-issues\">Memory and Performance Issues</h3><p><strong>Symptoms of Insufficient Memory</strong>:</p>\n<ul>\n<li>Slow web interface response</li>\n<li>Connection timeouts</li>\n<li>Service crashes</li>\n<li>Inability to install packages</li>\n</ul>\n<p><strong>Memory Diagnostics</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Check available memory\nfree -h\ncat /proc/meminfo\n\n# Check flash usage\ndf -h\ndu -sh /overlay/*\n\n# Monitor running processes\ntop\nps aux</code></pre><h2 id=\"prevention-and-maintenance\">Prevention and Maintenance</h2><h3 id=\"pre-flash-precautions\">Pre-Flash Precautions</h3><p><strong>Before Any Firmware Installation</strong>:</p>\n<ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Verify power stability</strong>: Use UPS if available</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Check firmware compatibility</strong>: Exact model/revision match</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Backup current firmware</strong>: If upgrade path available</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Read installation notes</strong>: Device-specific requirements</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Prepare recovery tools</strong>: TFTP server, serial adapter</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Document current settings</strong>: Network configuration backup</li>\n</ul>\n<h3 id=\"regular-maintenance-practices\">Regular Maintenance Practices</h3><p><strong>Monthly Maintenance</strong>:</p>\n<ul>\n<li><strong>Check system logs</strong>: Review for errors or warnings</li>\n<li><strong>Monitor memory usage</strong>: Ensure sufficient free space</li>\n<li><strong>Verify mesh connectivity</strong>: Test network paths</li>\n<li><strong>Update security keys</strong>: Rotate passwords if needed</li>\n</ul>\n<p><strong>Quarterly Maintenance</strong>:</p>\n<ul>\n<li><strong>Firmware updates</strong>: Check for LibreMesh releases</li>\n<li><strong>Configuration backup</strong>: Save current settings</li>\n<li><strong>Performance testing</strong>: Bandwidth and latency checks</li>\n<li><strong>Physical inspection</strong>: Check for overheating or damage</li>\n</ul>\n<h2 id=\"emergency-contact-information\">Emergency Contact Information</h2><h3 id=\"community-support-resources\">Community Support Resources</h3><p><strong>LibreMesh Community</strong>:</p>\n<ul>\n<li><strong>Mailing List</strong>: <a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a></li>\n<li><strong>IRC Channel</strong>: #libremesh on oftc.net</li>\n<li><strong>Forum</strong>: Community discussions and troubleshooting</li>\n</ul>\n<p><strong>OpenWrt Community</strong>:</p>\n<ul>\n<li><strong>Forum</strong>: <a href=\"https://forum.openwrt.org/\" target=\"_blank\">https://forum.openwrt.org/</a></li>\n<li><strong>Wiki</strong>: Device-specific troubleshooting guides</li>\n<li><strong>Developer lists</strong>: Technical implementation issues</li>\n</ul>\n<h3 id=\"professional-recovery-services\">Professional Recovery Services</h3><p><strong>When to Seek Professional Help</strong>:</p>\n<ul>\n<li>Hardware modification required (soldering)</li>\n<li>Bootloader corruption beyond serial recovery</li>\n<li>Critical deployment with time constraints</li>\n<li>Insurance/warranty coverage available</li>\n</ul>\n<h2 id=\"recovery-success-verification\">Recovery Success Verification</h2><h3 id=\"post-recovery-checklist\">Post-Recovery Checklist</h3><p>After any recovery procedure:</p>\n<ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Router responds</strong>: Web interface or SSH accessible</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Network connectivity</strong>: Internet access restored</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>All interfaces functional</strong>: Wireless and ethernet working</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>LED patterns normal</strong>: Standard operational indicators</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Performance acceptable</strong>: No obvious speed/stability issues</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Configuration restored</strong>: Settings match requirements</li>\n</ul>\n<h3 id=\"long-term-monitoring\">Long-term Monitoring</h3><p><strong>Monitor for Recurring Issues</strong>:</p>\n<ul>\n<li><strong>System stability</strong>: Uptime and crash frequency</li>\n<li><strong>Memory usage trends</strong>: Growing memory consumption</li>\n<li><strong>Network performance</strong>: Bandwidth and latency changes</li>\n<li><strong>Log analysis</strong>: Repeated error patterns</li>\n</ul>\n<blockquote>\n<p><strong>📋 Documentation</strong>: Keep detailed records of all troubleshooting procedures and their outcomes. This information is valuable for preventing future issues and helping community members with similar problems.</p>\n</blockquote>\n<h2 id=\"related-guides\">Related Guides</h2><ul>\n<li><strong><a href=\"router-selection-guide.md\" target=\"_blank\">Router Selection Guide</a></strong>: Choose appropriate hardware to minimize problems</li>\n<li><strong><a href=\"libremesh-installation.md\" target=\"_blank\">LibreMesh Installation Guide</a></strong>: Proper installation procedures to avoid issues</li>\n<li><strong>LibreMesh Configuration</strong>: Advanced configuration to optimize performance</li>\n</ul>\n"}, "guides/libremesh-installation.md": {"hash": "6d31db389904d40bb014d588cd5f9f99", "content": "<h1 id=\"libremesh-firmware-installation-guide\">LibreMesh Firmware Installation Guide</h1><p>This comprehensive guide provides step-by-step instructions for downloading, verifying, preparing, and flashing LibreMesh firmware onto your target router. Follow these procedures carefully to ensure a successful installation and avoid device damage.</p>\n<h2 id=\"pre-installation-requirements\">Pre-Installation Requirements</h2><h3 id=\"hardware-preparation\">Hardware Preparation</h3><ul>\n<li><strong>Router</strong>: Compatible device from <a href=\"router-selection-guide.md\" target=\"_blank\">Router Selection Guide</a></li>\n<li><strong>Computer</strong>: Windows, macOS, or Linux machine with Ethernet port</li>\n<li><strong>Ethernet cable</strong>: Cat5e or Cat6 cable for wired connection</li>\n<li><strong>Power supply</strong>: Original router power adapter</li>\n<li><strong>Tools</strong>: Paperclip or similar for reset button (if needed)</li>\n</ul>\n<h3 id=\"software-requirements\">Software Requirements</h3><ul>\n<li><strong>Web browser</strong>: Chrome, Firefox, Safari, or Edge</li>\n<li><strong>TFTP client</strong>: For recovery situations (PumpKIN, Tftpd64, or built-in)</li>\n<li><strong>Terminal/Command prompt</strong>: For network diagnostics</li>\n</ul>\n<blockquote>\n<p><strong>⚠️ Safety Notice</strong>: Firmware installation carries risk of device damage (&quot;bricking&quot;). Ensure stable power supply and follow instructions precisely. Never interrupt the flashing process.</p>\n</blockquote>\n<h2 id=\"step-1-download-libremesh-firmware\">Step 1: Download LibreMesh Firmware</h2><h3 id=\"finding-your-router39s-firmware\">Finding Your Router&#39;s Firmware</h3><p><em><code>[Firmware Download Page Screenshot](libremesh-download-page.png)</code> — LibreMesh site at the firmware download section showing the downloads page interface.</em></p>\n<ol>\n<li><strong>Visit the LibreMesh downloads site</strong>: <a href=\"https://downloads.libremesh.org/\" target=\"_blank\">https://downloads.libremesh.org/</a></li>\n<li><strong>Navigate to your router category</strong>: Browse by manufacturer (TP-Link, GL.iNet, Asus, etc.)</li>\n<li><strong>Locate exact model and revision</strong>: Match your device precisely</li>\n<li><strong>Choose firmware type</strong>:<ul>\n<li><strong><code>-factory.bin</code></strong>: For routers with stock/original firmware</li>\n<li><strong><code>-sysupgrade.bin</code></strong>: For routers already running OpenWrt/LibreMesh</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"firmware-verification\">Firmware Verification</h3><blockquote>\n<p><strong>🔒 Security Best Practice</strong>: Always verify firmware integrity before installation to prevent corrupted or malicious firmware from damaging your device.</p>\n</blockquote>\n<p><strong>Download verification files</strong> alongside your firmware:</p>\n<ul>\n<li><strong>SHA256 checksum file</strong>: Usually named <code>sha256sums</code></li>\n<li><strong>GPG signature</strong>: Often <code>sha256sums.gpg</code> for authenticity verification</li>\n</ul>\n<p><strong>Verify checksum (Linux/macOS)</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code>sha256sum -c sha256sums</code></pre><p><strong>Verify checksum (Windows)</strong>:</p>\n<pre class=\"code-block\" data-language=\"powershell\"><code>Get-FileHash -Algorithm SHA256 filename.bin</code></pre><p><strong>Compare output</strong> with the value in the checksum file. They must match exactly.</p>\n<blockquote>\n<p><strong>💡 Tip</strong>: If checksums don&#39;t match, re-download the firmware. Never install firmware with mismatched checksums.</p>\n</blockquote>\n<h2 id=\"step-2-prepare-your-network-connection\">Step 2: Prepare Your Network Connection</h2><h3 id=\"computer-network-configuration\">Computer Network Configuration</h3><ol>\n<li><strong>Disable wireless connections</strong>: Turn off Wi-Fi to avoid routing conflicts</li>\n<li><strong>Configure static IP</strong>: Set your computer&#39;s Ethernet interface to match router subnet<ul>\n<li><strong>IP Address</strong>: ***********00 (or ***********00)</li>\n<li><strong>Subnet Mask</strong>: *************</li>\n<li><strong>Gateway</strong>: Leave blank for now</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"physical-connection-setup\">Physical Connection Setup</h3><p><em><code>[Ethernet Connection Preparation](ethernet-cable-setup.png)</code> — User connecting LAN cable between computer and router for flashing, showing proper cable routing and connection points.</em></p>\n<ol>\n<li><strong>Power off router</strong>: Unplug power adapter completely</li>\n<li><strong>Connect Ethernet cable</strong>: Computer to router LAN port (not WAN)</li>\n<li><strong>Verify cable quality</strong>: Ensure cable is not damaged and connections are secure</li>\n<li><strong>Power on router</strong>: Plug in power adapter and wait for boot completion</li>\n</ol>\n<h3 id=\"router-access-methods\">Router Access Methods</h3><p><strong>For Stock Firmware Routers</strong>:</p>\n<ul>\n<li><strong>Common addresses</strong>: *********** or ***********</li>\n<li><strong>Check router label</strong>: Default IP often printed on device sticker</li>\n<li><strong>Login credentials</strong>: Usually admin/admin or admin/(blank)</li>\n</ul>\n<p><strong>For OpenWrt Routers</strong>:</p>\n<ul>\n<li><strong>Standard address</strong>: ***********</li>\n<li><strong>Default login</strong>: root with empty password (if unconfigured)</li>\n</ul>\n<p><strong>For LibreMesh Routers</strong>:</p>\n<ul>\n<li><strong>Magic address</strong>: <a href=\"http://thisnode.info\" target=\"_blank\">http://thisnode.info</a></li>\n<li><strong>Alternative</strong>: ********* or configured gateway IP</li>\n</ul>\n<h2 id=\"step-3-access-router-web-interface\">Step 3: Access Router Web Interface</h2><h3 id=\"troubleshooting-connection-issues\">Troubleshooting Connection Issues</h3><p>If you cannot reach the router web interface:</p>\n<p><strong>Check network connectivity</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Test basic connectivity\nping ***********\nping ***********\n\n# Check routing table\nip route show default    # Linux\nroute print             # Windows\nnetstat -rn             # macOS</code></pre><p><strong>Find router IP automatically</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Linux/macOS - scan common subnets\nnmap -sn ***********/24\nnmap -sn ***********/24\n\n# Windows - check ARP table\narp -a</code></pre><p><strong>Common solutions</strong>:</p>\n<ul>\n<li><strong>Verify cable connection</strong>: Try different Ethernet ports</li>\n<li><strong>Check power supply</strong>: Ensure router is fully powered and booted</li>\n<li><strong>Reset router</strong>: Use hardware reset if configuration is corrupted</li>\n<li><strong>Try different browser</strong>: Some routers have browser compatibility issues</li>\n</ul>\n<h2 id=\"step-4-flash-libremesh-firmware\">Step 4: Flash LibreMesh Firmware</h2><h3 id=\"standard-web-based-flashing\">Standard Web-Based Flashing</h3><p><em><code>[Router in Flash Mode](router-flash-mode.png)</code> — Router with LEDs or physical indicators signaling firmware flashing mode, showing typical LED patterns during firmware upload.</em></p>\n<ol>\n<li><p><strong>Navigate to firmware upgrade section</strong>:</p>\n<ul>\n<li><strong>TP-Link</strong>: Advanced → System Tools → Firmware Upgrade</li>\n<li><strong>Asus</strong>: Administration → Firmware Upgrade</li>\n<li><strong>GL.iNet</strong>: System → Upgrade</li>\n<li><strong>Generic OpenWrt</strong>: System → Backup/Flash Firmware</li>\n</ul>\n</li>\n<li><p><strong>Upload firmware file</strong>:</p>\n<ul>\n<li><strong>Click &quot;Choose File&quot;</strong> or &quot;Browse&quot;</li>\n<li><strong>Select your <code>-factory.bin</code> file</strong></li>\n<li><strong>Verify filename</strong> matches your router exactly</li>\n</ul>\n</li>\n<li><p><strong>Important pre-flash checks</strong>:</p>\n<ul>\n<li><input disabled=\"\" type=\"checkbox\"> Correct firmware file selected</li>\n<li><input disabled=\"\" type=\"checkbox\"> Stable power supply confirmed</li>\n<li><input disabled=\"\" type=\"checkbox\"> No other network activity on router</li>\n<li><input disabled=\"\" type=\"checkbox\"> Backup of current config (if desired)</li>\n</ul>\n</li>\n<li><p><strong>Initiate flash process</strong>:</p>\n<ul>\n<li><strong>Click &quot;Flash&quot; or &quot;Upgrade&quot;</strong></li>\n<li><strong>Do NOT power off</strong> or disconnect during process</li>\n<li><strong>Wait for completion</strong> (typically 3-5 minutes)</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"brand-specific-procedures\">Brand-Specific Procedures</h3><p><strong>TP-Link FCC Lock Bypass</strong> (for newer models):</p>\n<ol>\n<li><strong>Install DD-WRT first</strong>: Use appropriate DD-WRT US version</li>\n<li><strong>Revert to factory</strong>: Download official revert firmware</li>\n<li><strong>Rename LibreMesh firmware</strong>: Match exact TP-Link naming convention</li>\n<li><strong>Flash via stock interface</strong>: Upload renamed LibreMesh firmware</li>\n</ol>\n<p><strong>Ubiquiti AirOS Considerations</strong>:</p>\n<blockquote>\n<p><strong>⚠️ Critical Warning</strong>: AirOS 5.6.x will brick your device. Downgrade to AirOS 5.5.x first using official firmware from Ubiquiti downloads.</p>\n</blockquote>\n<p><strong>GL.iNet Devices</strong>:</p>\n<ul>\n<li><strong>Use U-Boot recovery</strong>: If web interface fails</li>\n<li><strong>Check for hardware switches</strong>: Some models have firmware selection switches</li>\n</ul>\n<h2 id=\"step-5-post-installation-verification\">Step 5: Post-Installation Verification</h2><h3 id=\"initial-connection-test\">Initial Connection Test</h3><p>After firmware installation completes:</p>\n<ol>\n<li><strong>Wait for router reboot</strong>: Allow 2-3 minutes for full startup</li>\n<li><strong>Reconfigure computer network</strong>: Set to DHCP or appropriate static IP</li>\n<li><strong>Access LibreMesh interface</strong>: Browse to <a href=\"http://thisnode.info\" target=\"_blank\">http://thisnode.info</a></li>\n</ol>\n<h3 id=\"first-time-configuration\">First-Time Configuration</h3><p><strong>Set Administrator Password</strong>:</p>\n<blockquote>\n<p><strong>🔒 Security Critical</strong>: Always set a strong admin password immediately after installation.</p>\n</blockquote>\n<ol>\n<li><strong>Navigate to password settings</strong>: Usually prompted on first access</li>\n<li><strong>Choose strong password</strong>: Minimum 12 characters with mixed case, numbers, symbols</li>\n<li><strong>Save configuration</strong>: Apply settings and wait for reload</li>\n</ol>\n<p><strong>Basic Network Verification</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Test connectivity to LibreMesh node\nping thisnode.info\n\n# Check mesh network advertisement\niwinfo wlan0 scan | grep LibreMesh\n\n# Verify network interfaces\nip addr show</code></pre><h3 id=\"essential-post-installation-steps\">Essential Post-Installation Steps</h3><ol>\n<li><p><strong>Update system packages</strong>:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code>opkg update\nopkg list-upgradable</code></pre></li>\n<li><p><strong>Configure time synchronization</strong>:</p>\n<ul>\n<li><strong>Set timezone</strong>: System → System → General Settings</li>\n<li><strong>Enable NTP</strong>: Network → Time Synchronization</li>\n</ul>\n</li>\n<li><p><strong>Configure mesh network</strong>:</p>\n<ul>\n<li><strong>Set network name</strong>: Customize from default &quot;LibreMesh.org&quot;</li>\n<li><strong>Configure channels</strong>: Optimize for local RF environment</li>\n<li><strong>Set mesh key</strong>: Enable encryption for mesh backhaul</li>\n</ul>\n</li>\n</ol>\n<h2 id=\"step-6-network-integration\">Step 6: Network Integration</h2><h3 id=\"internet-gateway-configuration\">Internet Gateway Configuration</h3><p><strong>Connect WAN interface</strong>:</p>\n<ol>\n<li><strong>Physical connection</strong>: Internet source to router WAN port</li>\n<li><strong>Configure protocol</strong>: DHCP, PPPoE, or static as required</li>\n<li><strong>Test connectivity</strong>: Verify internet access through mesh</li>\n</ol>\n<p><strong>Configure port roles</strong> (for single-port devices):</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Set specific port as WAN\nuci set network.wan.ifname=&#039;eth0.2&#039;\nuci commit network\nservice network restart</code></pre><h3 id=\"mesh-network-joining\">Mesh Network Joining</h3><p><strong>Automatic mesh discovery</strong>:</p>\n<ul>\n<li>LibreMesh nodes automatically discover neighbors</li>\n<li><strong>Verify mesh links</strong>: Network → Mesh Status</li>\n<li><strong>Check routing tables</strong>: Advanced → Status → Routes</li>\n</ul>\n<p><strong>Manual mesh configuration</strong> (if needed):</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Set mesh ID and key\nuci set lime.wifi.ap_ssid=&#039;YourNetworkName&#039;\nuci set lime.wifi.adhoc_ssid=&#039;YourMeshName_mesh&#039;\nuci commit lime\nlime-config\nreboot</code></pre><h2 id=\"troubleshooting-installation-issues\">Troubleshooting Installation Issues</h2><h3 id=\"failed-flash-recovery\">Failed Flash Recovery</h3><p>If firmware installation fails:</p>\n<ol>\n<li><strong>Do not panic</strong>: Most routers have recovery mechanisms</li>\n<li><strong>Try TFTP recovery</strong>: See <a href=\"router-troubleshooting.md\" target=\"_blank\">Router Troubleshooting Guide</a></li>\n<li><strong>Use hardware reset</strong>: Hold reset button during boot</li>\n<li><strong>Serial console recovery</strong>: Last resort for advanced users</li>\n</ol>\n<h3 id=\"common-installation-problems\">Common Installation Problems</h3><p><strong>&quot;Invalid Firmware&quot; Errors</strong>:</p>\n<ul>\n<li><strong>Check firmware filename</strong>: Must match expected format</li>\n<li><strong>Verify model/revision</strong>: Ensure exact hardware compatibility</li>\n<li><strong>Try different upload method</strong>: Web interface vs TFTP</li>\n</ul>\n<p><strong>Flash Process Hangs</strong>:</p>\n<ul>\n<li><strong>Power cycle router</strong>: Attempt recovery mode</li>\n<li><strong>Check network connectivity</strong>: Ensure stable connection</li>\n<li><strong>Reduce network traffic</strong>: Close other applications</li>\n</ul>\n<p><strong>Router Unresponsive After Flash</strong>:</p>\n<ul>\n<li><strong>Wait longer</strong>: Some routers need 5+ minutes for first boot</li>\n<li><strong>Check LED patterns</strong>: Compare with normal boot sequence</li>\n<li><strong>Try factory reset</strong>: Hardware reset button procedure</li>\n</ul>\n<h2 id=\"verification-checklist\">Verification Checklist</h2><p>After successful LibreMesh installation:</p>\n<ul>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Admin password set</strong>: Strong password configured</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Network connectivity</strong>: Internet access through mesh</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Web interface accessible</strong>: <a href=\"http://thisnode.info\" target=\"_blank\">http://thisnode.info</a> loads</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Mesh network active</strong>: Other nodes visible in mesh status</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Wireless networks broadcasting</strong>: Both client and mesh SSIDs</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>System time synchronized</strong>: Correct time zone and NTP</li>\n<li><input disabled=\"\" type=\"checkbox\"> <strong>Basic services running</strong>: SSH, web interface, mesh protocols</li>\n</ul>\n<h2 id=\"next-steps\">Next Steps</h2><p>With LibreMesh successfully installed:</p>\n<ol>\n<li><strong>Review <a href=\"router-troubleshooting.md\" target=\"_blank\">Router Troubleshooting Guide</a></strong> for maintenance procedures</li>\n<li><strong>Plan additional nodes</strong>: Expand mesh network coverage</li>\n<li><strong>Configure advanced features</strong>: QoS, captive portal, monitoring</li>\n<li><strong>Join community support</strong>: Mailing lists and forums for ongoing help</li>\n</ol>\n<blockquote>\n<p><strong>📚 Additional Resources</strong>: </p>\n<ul>\n<li>LibreMesh Configuration Documentation: <a href=\"https://libremesh.org/docs/\" target=\"_blank\">https://libremesh.org/docs/</a></li>\n<li>OpenWrt Package Management: <a href=\"https://openwrt.org/docs/guide-user/additional-software/opkg\" target=\"_blank\">https://openwrt.org/docs/guide-user/additional-software/opkg</a></li>\n<li>Community Support Forums: <a href=\"https://libremesh.org/communication.html\" target=\"_blank\">https://libremesh.org/communication.html</a></li>\n</ul>\n</blockquote>\n"}, "guides/libremesh-overview.md": {"hash": "2200c7b7ad6f79050375383988851c35", "content": "<h1 id=\"libremesh-overview\">LibreMesh Overview</h1><h2 id=\"introduction-to-libremesh\">Introduction to LibreMesh</h2><p>LibreMesh is a modular framework for creating OpenWrt-based firmwares for wireless mesh nodes, designed specifically for community networks[1][10]. Several communities around the world use LibreMesh as the foundation of their local mesh firmwares, enabling decentralized, community-managed wireless networks.</p>\n<p><code>[Mesh Network Topology Illustration](mesh-network-topology.png) — Diagram of mesh-connected routers in a typical deployment.</code></p>\n<p>LibreMesh represents a fundamental shift from traditional centralized wireless networks to distributed, community-controlled infrastructure. Unlike conventional wireless setups that rely on a single access point or router, LibreMesh creates self-configuring mesh networks where each node can route traffic for other participants, eliminating single points of failure[10].</p>\n<p><code>[LibreMesh Logo or Screenshot](libremesh-logo.png) — Identifying the project visually.</code></p>\n<h2 id=\"core-features-and-benefits\">Core Features and Benefits</h2><h3 id=\"automatic-network-configuration\">Automatic Network Configuration</h3><p>LibreMesh&#39;s primary strength lies in its auto-configurable nature[1]. The firmware allows simple deployment of versatile, multi-radio mesh networks without requiring extensive technical expertise from community members. Once installed, nodes automatically discover each other and establish mesh connections using sophisticated routing protocols.</p>\n<h3 id=\"dual-layer-network-architecture\">Dual-Layer Network Architecture</h3><p>LibreMesh implements a unique two-layer network architecture that provides both scalability and segmentation[8][20]:</p>\n<p><strong>Layer 2 (Cloud Layer)</strong>: Uses BATMAN-adv (Better Approach To Mobile Ad hoc Networking) routing protocol, which creates a virtual layer 2 collision domain across multiple hops. This enables seamless roaming for devices within the same &quot;cloud&quot;[17][20].</p>\n<p><strong>Layer 3 (Network Layer)</strong>: Employs Babel routing protocol (previously BMX6/BMX7) for IPv6-native dynamic routing with advanced features and minimal network overhead[17][20].</p>\n<h3 id=\"smart-gateway-selection\">Smart Gateway Selection</h3><p>The system provides intelligent gateway selection with redundancy and user choice capabilities[8]. Multiple internet connections can be shared across the mesh, with automatic failover when primary connections become unavailable.</p>\n<h3 id=\"multi-radio-support\">Multi-Radio Support</h3><p>LibreMesh supports multi-radio configurations, allowing routers to use different frequency bands simultaneously for both mesh backbone connections and client access[5]. This separation improves network performance by reducing interference between backhaul and access traffic.</p>\n<h2 id=\"network-topologies\">Network Topologies</h2><h3 id=\"community-mesh-networks\">Community Mesh Networks</h3><p>LibreMesh excels in community-scale deployments where neighborhoods, rural areas, or urban districts create shared wireless infrastructure. Common topologies include:</p>\n<ul>\n<li><strong>Neighborhood Networks</strong>: Connecting homes and small businesses within a geographic area</li>\n<li><strong>Rural Connectivity</strong>: Bridging remote locations with limited internet infrastructure  </li>\n<li><strong>Urban Mesh</strong>: Providing redundant connectivity in dense urban environments</li>\n<li><strong>Campus Networks</strong>: Supporting educational institutions or community centers</li>\n</ul>\n<h3 id=\"hybrid-infrastructure\">Hybrid Infrastructure</h3><p>The framework supports hybrid deployments combining:</p>\n<ul>\n<li>Mesh backbone connections between strategic points</li>\n<li>Traditional access point services for end users</li>\n<li>Wired backbone links where available</li>\n<li>Multiple internet gateway connections</li>\n</ul>\n<h2 id=\"common-use-cases-in-community-networks\">Common Use Cases in Community Networks</h2><h3 id=\"internet-sharing\">Internet Sharing</h3><p>Communities can pool resources to share internet connections, reducing individual costs while improving reliability through redundancy[22]. The mesh automatically balances traffic across available gateways.</p>\n<h3 id=\"local-services\">Local Services</h3><p>LibreMesh networks excel at hosting local services accessible without internet connectivity:</p>\n<ul>\n<li>Local web servers and content repositories</li>\n<li>Community communication platforms</li>\n<li>Local file sharing and collaboration tools</li>\n<li>Emergency communication systems</li>\n</ul>\n<h3 id=\"educational-and-social-applications\">Educational and Social Applications</h3><p>Many deployments focus on educational and social benefits:</p>\n<ul>\n<li>Providing internet access to underserved communities</li>\n<li>Supporting digital literacy programs</li>\n<li>Enabling local content creation and sharing</li>\n<li>Facilitating community organization and communication</li>\n</ul>\n<h3 id=\"emergency-and-disaster-response\">Emergency and Disaster Response</h3><p>The resilient, distributed nature of mesh networks makes them valuable for emergency scenarios:</p>\n<ul>\n<li>Maintaining communication when traditional infrastructure fails</li>\n<li>Providing internet access during disasters</li>\n<li>Supporting coordination efforts for emergency response</li>\n</ul>\n<h2 id=\"organizations-supporting-libremesh\">Organizations Supporting LibreMesh</h2><p>LibreMesh enjoys support from numerous organizations worldwide[10]:</p>\n<table class=\"pdf-table\">\n        <thead><tr>\n<th>Organization</th>\n<th>Region</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>AlterMundi</td>\n<td>Argentina</td>\n</tr>\n<tr>\n<td>FreiFunk</td>\n<td>Germany</td>\n</tr>\n<tr>\n<td>FunkFeuer</td>\n<td>Austria</td>\n</tr>\n<tr>\n<td>Guifi</td>\n<td>Iberian peninsula</td>\n</tr>\n<tr>\n<td>Ibebrasil</td>\n<td>Brasil</td>\n</tr>\n<tr>\n<td>LibreRouter</td>\n<td>Global</td>\n</tr>\n<tr>\n<td>Ninux.org</td>\n<td>Italy</td>\n</tr>\n<tr>\n<td>Wakoma</td>\n<td>Global</td>\n</tr>\n</tbody>\n      </table><h2 id=\"community-networks-using-libremesh\">Community Networks Using LibreMesh</h2><p>Active deployments demonstrate real-world success[10]:</p>\n<table class=\"pdf-table\">\n        <thead><tr>\n<th>Network</th>\n<th>Location</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Antennine</td>\n<td>Appennino Bolognese</td>\n</tr>\n<tr>\n<td>Calafou</td>\n<td>Catalunia</td>\n</tr>\n<tr>\n<td>Coolab</td>\n<td>Brasil</td>\n</tr>\n<tr>\n<td>Janastu CowMesh</td>\n<td>Rural Karnataka</td>\n</tr>\n</tbody>\n      </table><h2 id=\"technical-philosophy\">Technical Philosophy</h2><h3 id=\"open-source-hardware-and-software\">Open Source Hardware and Software</h3><p>LibreMesh embodies the principles of open technology, with all software and many hardware designs available under open licenses[4]. This transparency enables communities to understand, modify, and maintain their networks independently.</p>\n<h3 id=\"community-ownership\">Community Ownership</h3><p>The project emphasizes community ownership and control over networking infrastructure, moving away from dependence on commercial providers or centralized authorities[10]. This approach aligns with broader movements toward digital sovereignty and community resilience.</p>\n<h3 id=\"scalability-and-modularity\">Scalability and Modularity</h3><p>The modular design allows communities to start small and grow organically, adding nodes and services as needs evolve[5]. The system scales from simple two-node links to complex multi-hundred node networks.</p>\n<h2 id=\"getting-started\">Getting Started</h2><p>For communities interested in LibreMesh, the typical progression involves:</p>\n<ol>\n<li><strong>Planning Phase</strong>: Assessing community needs and technical requirements</li>\n<li><strong>Hardware Selection</strong>: Choosing appropriate routers and equipment (see <a href=\"#libremesh-installationmd\" >libremesh-installation.md</a>)</li>\n<li><strong>Network Design</strong>: Planning node placement and topology (see <a href=\"#mesh-network-basicsmd\" >mesh-network-basics.md</a>)</li>\n<li><strong>Configuration</strong>: Setting up basic network parameters and services</li>\n<li><strong>Deployment</strong>: Installing and configuring nodes</li>\n<li><strong>Community Integration</strong>: Training users and establishing governance</li>\n</ol>\n<h2 id=\"next-steps\">Next Steps</h2><p>This overview provides the foundation for understanding LibreMesh&#39;s capabilities and applications. For detailed implementation guidance:</p>\n<ul>\n<li>Review <a href=\"#mesh-network-basicsmd\" >mesh-network-basics.md</a> for networking concepts</li>\n<li>Consult <a href=\"#libremesh-installationmd\" >libremesh-installation.md</a> for hardware and installation procedures</li>\n<li>Explore <a href=\"#captive-portal-guidemd\" >captive-portal-guide.md</a> for user management</li>\n<li>Reference <a href=\"#dns-configurationmd\" >dns-configuration.md</a> for local service setup</li>\n</ul>\n<p>LibreMesh represents more than just networking technology—it&#39;s a tool for community empowerment and digital self-determination. By providing the technical foundation for community-controlled networks, it enables groups to take ownership of their digital infrastructure and create more resilient, equitable connectivity solutions.</p>\n"}, "guides/dns-configuration.md": {"hash": "a4d5980e8fcbbe733e236d0fb50d218f", "content": "<h1 id=\"dns-configuration-for-libremesh\">DNS Configuration for LibreMesh</h1><p>This guide explains how to configure DNS services on LibreMesh networks to enable reliable access to local services, implement caching, and establish domain naming inside the mesh network.</p>\n<h2 id=\"introduction-to-dns-in-mesh-networks\">Introduction to DNS in Mesh Networks</h2><p>Domain Name System (DNS) is a critical component of any network, translating human-readable domain names into IP addresses. In mesh networks, DNS configuration takes on additional importance due to:</p>\n<ul>\n<li><strong>Local Services</strong>: Need to provide access to services hosted within the mesh</li>\n<li><strong>Distributed Architecture</strong>: Multiple nodes may provide services across the network</li>\n<li><strong>Offline Operation</strong>: Networks must function even without internet connectivity</li>\n<li><strong>Resource Constraints</strong>: Limited hardware requires efficient DNS solutions</li>\n</ul>\n<p>LibreMesh uses dnsmasq as its primary DNS service, providing a lightweight yet powerful solution for mesh networks[36][37].</p>\n<p><code>[Network Diagram with Local DNS](local-dns-diagram.png) — Schematic showing flow of local DNS queries.]</code></p>\n<h2 id=\"dns-architecture-in-libremesh\">DNS Architecture in LibreMesh</h2><h3 id=\"components-overview\">Components Overview</h3><p>The DNS system in LibreMesh consists of several integrated components[37][38][40]:</p>\n<ol>\n<li><strong>dnsmasq</strong>: The primary DNS server and DHCP service</li>\n<li><strong>Shared State</strong>: Mechanism for distributing DNS information between nodes</li>\n<li><strong>Host Configuration</strong>: Local and network-wide hostname definitions</li>\n<li><strong>Domain Configuration</strong>: Settings for local domains within the mesh</li>\n</ol>\n<h3 id=\"resolution-flow\">Resolution Flow</h3><p>When a client on the mesh network makes a DNS query[41]:</p>\n<ol>\n<li>The request first goes to the local dnsmasq instance on the connected node</li>\n<li>If the domain is local to the mesh, it&#39;s resolved using local records</li>\n<li>If it&#39;s an internet domain and cached, the cached result is returned</li>\n<li>Otherwise, the query is forwarded to upstream DNS servers</li>\n<li>If internet connectivity is unavailable, only local domains resolve</li>\n</ol>\n<h2 id=\"basic-dns-configuration\">Basic DNS Configuration</h2><h3 id=\"accessing-dns-settings\">Accessing DNS Settings</h3><p>DNS configuration in LibreMesh can be modified through several methods[55]:</p>\n<ol>\n<li><strong>Web Interface</strong>: Limited DNS settings are available through the LibreMesh web interface</li>\n<li><strong>UCI Configuration</strong>: Edit settings using UCI (Unified Configuration Interface)</li>\n<li><strong>Direct File Editing</strong>: Modify configuration files in <code>/etc/config/</code></li>\n</ol>\n<h3 id=\"configuration-files\">Configuration Files</h3><p>The main configuration files for DNS in LibreMesh are[37][38]:</p>\n<ul>\n<li><strong>/etc/config/dhcp</strong>: Contains dnsmasq configuration</li>\n<li><strong>/etc/config/lime</strong>: Main LibreMesh configuration affecting DNS behavior</li>\n<li><strong>/etc/hosts</strong>: Local hostname definitions</li>\n<li><strong>/etc/dnsmasq.d/</strong>: Additional configuration fragments</li>\n</ul>\n<p><code>[LibreMesh DNS Config Screenshot](libremesh-dns-config.png) — Web UI or config file with DNS settings highlighted.]</code></p>\n<h2 id=\"setting-up-local-dns-resolution\">Setting Up Local DNS Resolution</h2><h3 id=\"configuring-static-hosts\">Configuring Static Hosts</h3><p>To add static host entries for local services[36][38]:</p>\n<ol>\n<li><p><strong>Via SSH</strong>: Connect to the LibreMesh node via SSH:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>ssh <EMAIL></code></pre></li>\n<li><p><strong>Edit Hosts File</strong>: Add entries to <code>/etc/hosts</code>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>vi /etc/hosts</code></pre><p>Add lines in the format:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>***********0   mediaserver.mesh\n***********0   wiki.mesh</code></pre></li>\n<li><p><strong>Restart DNS Service</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>/etc/init.d/dnsmasq restart</code></pre></li>\n</ol>\n<h3 id=\"using-dnsmasq-distributed-hosts\">Using dnsmasq-distributed-hosts</h3><p>LibreMesh includes a package called dnsmasq-distributed-hosts that shares hostname information across the mesh[25]:</p>\n<ol>\n<li><p><strong>Verify Package Installation</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>opkg list-installed | grep dnsmasq-distributed</code></pre></li>\n<li><p><strong>Configure Host Distribution</strong>:<br>Edit <code>/etc/config/lime-community</code> to enable host distribution:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config lime network\n    option dns_distributed_hosts &#039;1&#039;</code></pre></li>\n<li><p><strong>Add Local Services</strong>: Register local services on their respective nodes using the hosts file</p>\n</li>\n</ol>\n<h2 id=\"implementing-domain-aliases\">Implementing Domain Aliases</h2><h3 id=\"custom-domain-names-for-local-services\">Custom Domain Names for Local Services</h3><p>To create user-friendly domain names for local services[25]:</p>\n<ol>\n<li><p><strong>Create Domain Aliases Configuration</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>vi /etc/config/domain-aliases</code></pre></li>\n<li><p><strong>Add Domain Aliases</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config alias\n    option domain &#039;community.portal&#039;\n    option target &#039;***********00&#039;\n\nconfig alias\n    option domain &#039;local.library&#039;\n    option target &#039;***********01&#039;</code></pre></li>\n<li><p><strong>Apply Configuration</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>/etc/init.d/domain-aliases restart</code></pre></li>\n</ol>\n<h3 id=\"domain-extensions\">Domain Extensions</h3><p>LibreMesh typically uses these domain extensions for local services[41]:</p>\n<ul>\n<li><strong>.mesh</strong>: For mesh-wide services</li>\n<li><strong>.local</strong>: For node-specific services</li>\n<li><strong>.lan</strong>: For local network services</li>\n</ul>\n<h2 id=\"dns-caching-configuration\">DNS Caching Configuration</h2><h3 id=\"optimizing-cache-settings\">Optimizing Cache Settings</h3><p>DNS caching improves performance by storing recent query results[38][40]:</p>\n<ol>\n<li><p><strong>Edit dnsmasq Configuration</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>vi /etc/config/dhcp</code></pre></li>\n<li><p><strong>Configure Cache Size</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option cachesize &#039;1000&#039;\n    option min_cache_ttl &#039;3600&#039;</code></pre></li>\n<li><p><strong>Apply Changes</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>/etc/init.d/dnsmasq restart</code></pre></li>\n</ol>\n<h3 id=\"nodelocal-dns-cache\">NodeLocal DNS Cache</h3><p>For larger deployments, implement a node-local DNS cache to reduce query latency[40]:</p>\n<ol>\n<li><p><strong>Install Required Packages</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>opkg update\nopkg install dns-cache-local</code></pre></li>\n<li><p><strong>Configure Forwarding</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>vi /etc/config/dns-cache-local</code></pre><p>Add:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dns-cache-local\n    option enabled &#039;1&#039;\n    option cache_size &#039;10000&#039;</code></pre></li>\n<li><p><strong>Restart Services</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>/etc/init.d/dns-cache-local restart</code></pre></li>\n</ol>\n<h2 id=\"offline-dns-operation\">Offline DNS Operation</h2><h3 id=\"ensuring-local-resilience\">Ensuring Local Resilience</h3><p>Configure DNS to operate reliably without internet connectivity[41]:</p>\n<ol>\n<li><p><strong>Configure Local Domain</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>vi /etc/config/dhcp</code></pre><p>Add:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option local &#039;/mesh/&#039;\n    option domain &#039;mesh&#039;\n    option expandhosts &#039;1&#039;</code></pre></li>\n<li><p><strong>Set Authoritative Mode</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option authoritative &#039;1&#039;</code></pre></li>\n<li><p><strong>Disable DNS Forwarding When Offline</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option nonwildcard &#039;1&#039;\n    option filterwin2k &#039;1&#039;</code></pre></li>\n</ol>\n<h2 id=\"advanced-dns-configurations\">Advanced DNS Configurations</h2><h3 id=\"multiple-domain-support\">Multiple Domain Support</h3><p>To support multiple local domains[36]:</p>\n<ol>\n<li><p><strong>Configure Domain Definitions</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    list local &#039;/mesh/&#039;\n    list local &#039;/community/&#039;\n    list local &#039;/services/&#039;</code></pre></li>\n<li><p><strong>Configure Domain Behavior</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option domain &#039;mesh&#039;\n    option expand_hosts &#039;1&#039;\n    option local_service &#039;1&#039;</code></pre></li>\n</ol>\n<h3 id=\"integration-with-external-dns\">Integration with External DNS</h3><p>For networks with internet connectivity[38]:</p>\n<ol>\n<li><p><strong>Configure Upstream DNS Servers</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    list server &#039;*******&#039;\n    list server &#039;*******&#039;</code></pre></li>\n<li><p><strong>Configure Smart Forwarding</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option all_servers &#039;1&#039;\n    option sequential_ip &#039;1&#039;</code></pre></li>\n</ol>\n<h2 id=\"special-libremesh-dns-features\">Special LibreMesh DNS Features</h2><h3 id=\"thisnodeinfo-domain\">thisnode.info Domain</h3><p>LibreMesh implements a special domain called &quot;thisnode.info&quot; that always resolves to the current node[41]:</p>\n<ol>\n<li>This domain works across all nodes in the network</li>\n<li>Accessing <a href=\"http://thisnode.info\" target=\"_blank\">http://thisnode.info</a> always brings up the local node&#39;s web interface</li>\n<li>This feature simplifies node administration</li>\n</ol>\n<h3 id=\"shared-state-for-dns\">Shared State for DNS</h3><p>LibreMesh uses a shared state system to distribute DNS information[37]:</p>\n<ol>\n<li><p><strong>Enable Shared State</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config lime network\n    option use_shared_state &#039;1&#039;</code></pre></li>\n<li><p><strong>Configure State Sharing</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config lime shared_state\n    option sharing_strategy &#039;all&#039;\n    option shared_data &#039;hosts dnsmasq-lease&#039;</code></pre></li>\n</ol>\n<h2 id=\"troubleshooting-dns-issues\">Troubleshooting DNS Issues</h2><h3 id=\"common-problems-and-solutions\">Common Problems and Solutions</h3><h4 id=\"domain-names-not-resolving\">Domain Names Not Resolving</h4><ol>\n<li><p><strong>Check dnsmasq Service</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>/etc/init.d/dnsmasq status</code></pre></li>\n<li><p><strong>Verify Configuration</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>cat /etc/config/dhcp</code></pre></li>\n<li><p><strong>Test Resolution</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>nslookup local.service 127.0.0.1</code></pre></li>\n<li><p><strong>Check Logs</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>logread | grep dnsmasq</code></pre></li>\n</ol>\n<h4 id=\"dns-propagation-issues\">DNS Propagation Issues</h4><p>If DNS entries aren&#39;t propagating across the mesh:</p>\n<ol>\n<li><p><strong>Verify Shared State</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>cat /tmp/shared-state/dnsmasq-hosts</code></pre></li>\n<li><p><strong>Check Mesh Connectivity</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>ping othernode.mesh</code></pre></li>\n<li><p><strong>Restart Shared State Service</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>/etc/init.d/shared-state restart</code></pre></li>\n</ol>\n<h4 id=\"slow-resolution\">Slow Resolution</h4><p>For slow DNS resolution:</p>\n<ol>\n<li><p><strong>Optimize Cache Settings</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option cachesize &#039;2000&#039;\n    option min_cache_ttl &#039;7200&#039;</code></pre></li>\n<li><p><strong>Check for DNS Leaks</strong>:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>tcpdump -i any port 53</code></pre></li>\n</ol>\n<h2 id=\"replacing-dnsmasq\">Replacing dnsmasq</h2><p>LibreMesh currently uses dnsmasq for both DNS and DHCP, but there are plans to separate these functions[37]:</p>\n<ol>\n<li><strong>Current Status</strong>: dnsmasq handles both DNS and DHCP in current LibreMesh versions</li>\n<li><strong>Future Plans</strong>: odhcpd may replace the DHCP functionality while keeping dnsmasq for DNS</li>\n<li><strong>Compatibility</strong>: Any changes will maintain backward compatibility with existing configurations</li>\n</ol>\n<h2 id=\"best-practices-for-libremesh-dns\">Best Practices for LibreMesh DNS</h2><h3 id=\"performance-optimization\">Performance Optimization</h3><ol>\n<li><strong>Right-Size Cache</strong>: Adjust cache size based on node memory (larger for gateway nodes)</li>\n<li><strong>Minimize TTL Overrides</strong>: Only override TTL for frequently accessed resources</li>\n<li><strong>Use Local Domains</strong>: Keep mesh-specific services under consistent domain names</li>\n</ol>\n<h3 id=\"security-considerations\">Security Considerations</h3><ol>\n<li><strong>Restrict Zone Transfers</strong>: Limit zone transfers to mesh nodes only</li>\n<li><strong>Implement DNS Filtering</strong>: Consider DNS-based content filtering for community policies</li>\n<li><strong>Secure Configuration Files</strong>: Protect DNS configuration from unauthorized changes</li>\n</ol>\n<h3 id=\"scalability-planning\">Scalability Planning</h3><ol>\n<li><strong>Hierarchical Structure</strong>: For large networks, implement a hierarchical DNS structure</li>\n<li><strong>Load Distribution</strong>: Distribute DNS serving across multiple powerful nodes</li>\n<li><strong>Monitoring</strong>: Implement DNS query monitoring to identify performance issues</li>\n</ol>\n<h2 id=\"implementation-examples\">Implementation Examples</h2><h3 id=\"small-community-network\">Small Community Network</h3><p>For a network of 5-10 nodes:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option domain &#039;ourcomm.mesh&#039;\n    option expandhosts &#039;1&#039;\n    option local &#039;/ourcomm.mesh/&#039;\n    option cachesize &#039;500&#039;</code></pre><h3 id=\"medium-community-network\">Medium Community Network</h3><p>For a network of 20-50 nodes:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>config dnsmasq\n    option domain &#039;communitynet.mesh&#039;\n    option expandhosts &#039;1&#039;\n    option local &#039;/communitynet.mesh/&#039;\n    option local &#039;/services.communitynet.mesh/&#039;\n    option cachesize &#039;1000&#039;\n    option all_servers &#039;1&#039;</code></pre><h3 id=\"large-community-network\">Large Community Network</h3><p>For a network of 100+ nodes, implement hierarchical DNS with primary and secondary DNS servers on more powerful nodes.</p>\n<h2 id=\"next-steps\">Next Steps</h2><p>For further development of your LibreMesh network:</p>\n<ul>\n<li>Review <a href=\"#libremesh-overviewmd\" >libremesh-overview.md</a> for broader context</li>\n<li>Consult <a href=\"#mesh-network-basicsmd\" >mesh-network-basics.md</a> for networking fundamentals</li>\n<li>Follow <a href=\"#libremesh-installationmd\" >libremesh-installation.md</a> for hardware and setup guidance</li>\n<li>Explore <a href=\"#captive-portal-guidemd\" >captive-portal-guide.md</a> for user management options</li>\n</ul>\n<p>By implementing a well-configured DNS system, you&#39;ll enhance the usability, performance, and resilience of your LibreMesh network, enabling seamless access to both local and internet resources.</p>\n"}, "guides/disk-partitioning.md": {"hash": "97f2641a879b5ad42bd106e06d58f8bf", "content": "<h1 id=\"beginner39s-guide-to-disk-partitioning-for-community-servers\">Beginner&#39;s Guide to Disk Partitioning for Community Servers</h1><p>This comprehensive guide covers disk partitioning fundamentals using both graphical and command-line tools, with specific recommendations for community server deployments.</p>\n<h2 id=\"understanding-disk-partitioning\">Understanding Disk Partitioning</h2><p>Disk partitioning divides a storage device into separate, independent sections called partitions[16]. Each partition can have its own file system and serves specific purposes in server environments. Proper partitioning improves system performance, simplifies backups, and enhances security[17].</p>\n<h3 id=\"why-partition-your-server-drives\">Why Partition Your Server Drives?</h3><ol>\n<li><strong>Separation of Concerns</strong>: Keep system files separate from user data and logs[17]</li>\n<li><strong>Performance Optimization</strong>: Reduce disk fragmentation and I/O contention[17]</li>\n<li><strong>Security Enhancement</strong>: Isolate sensitive data and system components</li>\n<li><strong>Backup Efficiency</strong>: Enable targeted backups of specific data types</li>\n<li><strong>Fault Tolerance</strong>: Prevent one full partition from affecting the entire system[17]</li>\n</ol>\n<h2 id=\"recommended-partition-layout-for-community-servers\">Recommended Partition Layout for Community Servers</h2><p><code>[Partition Layout Example](partition-layout.png)</code></p>\n<h3 id=\"standard-partition-scheme\">Standard Partition Scheme</h3><p>For most community server deployments, consider this layout:</p>\n<table class=\"pdf-table\">\n        <thead><tr>\n<th>Partition</th>\n<th>Mount Point</th>\n<th>Size</th>\n<th>File System</th>\n<th>Purpose</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>Boot</td>\n<td><code>/boot</code></td>\n<td>1GB</td>\n<td>ext4</td>\n<td>Boot loader files and kernels</td>\n</tr>\n<tr>\n<td>Root</td>\n<td><code>/</code></td>\n<td>20-50GB</td>\n<td>ext4</td>\n<td>System files and applications</td>\n</tr>\n<tr>\n<td>Var</td>\n<td><code>/var</code></td>\n<td>10-20GB</td>\n<td>ext4</td>\n<td>Variable data, temporary files</td>\n</tr>\n<tr>\n<td>Var Log</td>\n<td><code>/var/log</code></td>\n<td>5-10GB</td>\n<td>ext4</td>\n<td>System and application logs[17]</td>\n</tr>\n<tr>\n<td>Home</td>\n<td><code>/home</code></td>\n<td>10-50GB</td>\n<td>ext4</td>\n<td>User directories</td>\n</tr>\n<tr>\n<td>Tmp</td>\n<td><code>/tmp</code></td>\n<td>2-5GB</td>\n<td>ext4</td>\n<td>Temporary files[17]</td>\n</tr>\n<tr>\n<td>Data</td>\n<td><code>/srv</code> or <code>/data</code></td>\n<td>Remaining space</td>\n<td>ext4</td>\n<td>Application data and services</td>\n</tr>\n<tr>\n<td>Swap</td>\n<td>N/A</td>\n<td>1-2x RAM</td>\n<td>swap</td>\n<td>Virtual memory</td>\n</tr>\n</tbody>\n      </table><blockquote>\n<p><strong>💡 Best Practice</strong></p>\n<p>Separating <code>/var/log</code> from <code>/var</code> prevents log files from filling up the entire <code>/var</code> partition and affecting system stability[17].</p>\n</blockquote>\n<h2 id=\"method-1-graphical-partitioning-with-gparted\">Method 1: Graphical Partitioning with GParted</h2><p>GParted (GNOME Partition Editor) provides an intuitive interface for managing disk partitions[9].</p>\n<h3 id=\"installing-gparted\">Installing GParted</h3><p><strong>Ubuntu/Debian:</strong></p>\n<pre class=\"code-block\" data-language=\"bash\"><code>sudo apt update\nsudo apt install gparted</code></pre><p><strong>CentOS/RHEL/Fedora:</strong></p>\n<pre class=\"code-block\" data-language=\"bash\"><code>sudo dnf install gparted  # Fedora\nsudo yum install gparted  # CentOS/RHEL</code></pre><h3 id=\"using-gparted\">Using GParted</h3><h4 id=\"step-1-launch-gparted\">Step 1: Launch GParted</h4><ol>\n<li>Open GParted from the Applications menu or run:<pre class=\"code-block\" data-language=\"bash\"><code>sudo gparted</code></pre></li>\n<li>Enter your admin password when prompted</li>\n<li>GParted will scan all available storage devices</li>\n</ol>\n<p><code>[GParted In Use](gparted-screenshot.png)</code></p>\n<h4 id=\"step-2-select-target-drive\">Step 2: Select Target Drive</h4><ol>\n<li>Use the dropdown menu in the top-right to select your target drive</li>\n<li><strong>Verify the drive size and model</strong> to ensure you&#39;re working with the correct device</li>\n<li>Review existing partitions displayed in the graphical layout</li>\n</ol>\n<blockquote>\n<p><strong>⚠️ Critical Warning</strong></p>\n<p>Always verify you&#39;ve selected the correct drive. Partitioning operations will destroy existing data.</p>\n</blockquote>\n<h4 id=\"step-3-create-partition-table-if-needed\">Step 3: Create Partition Table (if needed)</h4><p>For new drives:</p>\n<ol>\n<li>Go to <strong>Device</strong> → <strong>Create Partition Table</strong></li>\n<li>Select <strong>GPT</strong> for drives larger than 2TB or modern systems</li>\n<li>Select <strong>MBR (msdos)</strong> for compatibility with older systems[13]</li>\n<li>Click <strong>Apply</strong></li>\n</ol>\n<h4 id=\"step-4-create-new-partitions\">Step 4: Create New Partitions</h4><ol>\n<li>Right-click on unallocated space</li>\n<li>Select <strong>New</strong></li>\n<li>Configure partition settings:<ul>\n<li><strong>Size</strong>: Specify partition size in MB or GB</li>\n<li><strong>Create as</strong>: Choose Primary, Extended, or Logical</li>\n<li><strong>File system</strong>: Select ext4 for Linux partitions</li>\n<li><strong>Label</strong>: Add descriptive labels (e.g., &quot;server-root&quot;, &quot;data-storage&quot;)</li>\n</ul>\n</li>\n<li>Click <strong>Add</strong> to queue the operation</li>\n</ol>\n<h4 id=\"step-5-apply-changes\">Step 5: Apply Changes</h4><ol>\n<li>Review all queued operations in the pending operations list</li>\n<li>Click <strong>Apply</strong> to execute all changes</li>\n<li>Confirm the operation when prompted</li>\n<li>Wait for completion (this may take several minutes for large partitions)</li>\n</ol>\n<h3 id=\"advanced-gparted-operations\">Advanced GParted Operations</h3><h4 id=\"resizing-existing-partitions\">Resizing Existing Partitions</h4><ol>\n<li>Right-click the partition to resize</li>\n<li>Select <strong>Resize/Move</strong></li>\n<li>Drag the partition boundaries or enter specific sizes</li>\n<li>Apply changes</li>\n</ol>\n<h4 id=\"moving-partitions\">Moving Partitions</h4><p>Use the <strong>Resize/Move</strong> dialog to shift partition locations on the disk[11]. This is useful for consolidating free space.</p>\n<h2 id=\"method-2-command-line-partitioning\">Method 2: Command-Line Partitioning</h2><p>Command-line tools offer more control and automation capabilities for server deployments.</p>\n<h3 id=\"using-fdisk\">Using fdisk</h3><p><code>fdisk</code> is a powerful command-line partitioning tool available on all Linux systems[14][16].</p>\n<h4 id=\"step-1-identify-target-drive\">Step 1: Identify Target Drive</h4><pre class=\"code-block\" data-language=\"bash\"><code>sudo fdisk -l</code></pre><p>This command lists all drives and their current partitions[14]. Identify your target drive (e.g., <code>/dev/sdb</code>).</p>\n<h4 id=\"step-2-start-fdisk\">Step 2: Start fdisk</h4><pre class=\"code-block\" data-language=\"bash\"><code>sudo fdisk /dev/sdb</code></pre><p>Replace <code>/dev/sdb</code> with your target drive[14].</p>\n<h4 id=\"step-3-create-partitions\">Step 3: Create Partitions</h4><p>Within the fdisk prompt:</p>\n<ol>\n<li><strong>Create new partition</strong>: Type <code>n</code></li>\n<li><strong>Select partition type</strong>: <ul>\n<li><code>p</code> for primary partition</li>\n<li><code>e</code> for extended partition[14]</li>\n</ul>\n</li>\n<li><strong>Choose partition number</strong>: Accept default or specify (1-4 for primary)</li>\n<li><strong>Set first sector</strong>: Accept default for optimal alignment</li>\n<li><strong>Set last sector</strong>: Specify size using <code>+20G</code> format for 20GB partition[14]</li>\n</ol>\n<h4 id=\"step-4-set-partition-type\">Step 4: Set Partition Type</h4><ol>\n<li>Type <code>t</code> to change partition type</li>\n<li>Select partition number</li>\n<li>Enter type code:<ul>\n<li><code>83</code> for Linux (ext4)</li>\n<li><code>82</code> for Linux swap</li>\n<li><code>8e</code> for Linux LVM[26]</li>\n</ul>\n</li>\n</ol>\n<h4 id=\"step-5-write-changes\">Step 5: Write Changes</h4><ol>\n<li>Type <code>p</code> to preview partition table</li>\n<li>Type <code>w</code> to write changes and exit[14]</li>\n</ol>\n<h3 id=\"using-parted\">Using parted</h3><p><code>parted</code> offers more advanced features and supports GPT partition tables[16].</p>\n<h4 id=\"basic-parted-usage\">Basic parted Usage</h4><pre class=\"code-block\" data-language=\"bash\"><code># Start parted on target drive\nsudo parted /dev/sdb\n\n# Create GPT partition table\n(parted) mklabel gpt\n\n# Create partitions\n(parted) mkpart primary ext4 0% 20GB\n(parted) mkpart primary ext4 20GB 40GB\n(parted) mkpart primary linux-swap 40GB 42GB\n\n# Exit parted\n(parted) quit</code></pre><h2 id=\"formatting-new-partitions\">Formatting New Partitions</h2><p>After creating partitions, format them with appropriate file systems[23].</p>\n<h3 id=\"format-ext4-partitions\">Format ext4 Partitions</h3><pre class=\"code-block\" data-language=\"bash\"><code># Format with ext4\nsudo mkfs.ext4 /dev/sdb1\n\n# Format with label\nsudo mkfs.ext4 -L &quot;server-root&quot; /dev/sdb1</code></pre><h3 id=\"create-swap-partition\">Create Swap Partition</h3><pre class=\"code-block\" data-language=\"bash\"><code># Format swap partition\nsudo mkswap /dev/sdb3\n\n# Enable swap\nsudo swapon /dev/sdb3</code></pre><h3 id=\"verify-formatting\">Verify Formatting</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check file system information\nsudo blkid /dev/sdb1\n\n# List all block devices\nlsblk -f</code></pre><h2 id=\"mounting-partitions\">Mounting Partitions</h2><h3 id=\"temporary-mounting\">Temporary Mounting</h3><pre class=\"code-block\" data-language=\"bash\"><code># Create mount point\nsudo mkdir /mnt/server-data\n\n# Mount partition\nsudo mount /dev/sdb2 /mnt/server-data</code></pre><h3 id=\"permanent-mounting\">Permanent Mounting</h3><p>Edit <code>/etc/fstab</code> for automatic mounting at boot:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code>sudo nano /etc/fstab</code></pre><p>Add entries using UUID for reliability:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>UUID=12345678-1234-1234-1234-123456789012 /srv ext4 defaults 0 2\nUUID=*************-4321-4321-************ none swap sw 0 0</code></pre><p>Get UUIDs with:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code>sudo blkid</code></pre><h2 id=\"platform-specific-recommendations\">Platform-Specific Recommendations</h2><h3 id=\"raspberry-pi-servers\">Raspberry Pi Servers</h3><ul>\n<li><strong>Boot Partition</strong>: 256MB FAT32 for firmware</li>\n<li><strong>Root Partition</strong>: 8-16GB ext4 minimum</li>\n<li><strong>Use SD card for boot, USB SSD for data</strong> when possible</li>\n</ul>\n<h3 id=\"x86-community-servers\">x86 Community Servers</h3><ul>\n<li><strong>EFI System Partition</strong>: 512MB FAT32 for UEFI systems</li>\n<li><strong>Boot Partition</strong>: 1GB ext4</li>\n<li><strong>Consider LVM</strong> for flexible storage management[17]</li>\n</ul>\n<h3 id=\"arm-based-servers\">ARM-based Servers</h3><ul>\n<li>Check manufacturer documentation for specific partition requirements</li>\n<li>May require special boot partitions or firmware sections</li>\n</ul>\n<h2 id=\"logical-volume-management-lvm\">Logical Volume Management (LVM)</h2><p>LVM provides advanced storage management capabilities[17][24].</p>\n<h3 id=\"lvm-benefits\">LVM Benefits</h3><ol>\n<li><strong>Dynamic Resizing</strong>: Grow or shrink logical volumes as needed[24]</li>\n<li><strong>Storage Pooling</strong>: Combine multiple physical drives[17]</li>\n<li><strong>Snapshots</strong>: Create point-in-time backups</li>\n<li><strong>Striping</strong>: Improve performance across multiple drives</li>\n</ol>\n<h3 id=\"basic-lvm-setup\">Basic LVM Setup</h3><pre class=\"code-block\" data-language=\"bash\"><code># Install LVM tools\nsudo apt install lvm2\n\n# Create physical volume\nsudo pvcreate /dev/sdb1\n\n# Create volume group\nsudo vgcreate server-vg /dev/sdb1\n\n# Create logical volumes\nsudo lvcreate -L 20G -n root server-vg\nsudo lvcreate -L 10G -n var server-vg\nsudo lvcreate -l 100%FREE -n data server-vg\n\n# Format logical volumes\nsudo mkfs.ext4 /dev/server-vg/root\nsudo mkfs.ext4 /dev/server-vg/var\nsudo mkfs.ext4 /dev/server-vg/data</code></pre><h2 id=\"common-partitioning-mistakes-to-avoid\">Common Partitioning Mistakes to Avoid</h2><h3 id=\"size-related-issues\">Size-Related Issues</h3><ul>\n<li><strong>Root partition too small</strong>: Minimum 20GB, preferably 30-50GB</li>\n<li><strong>No swap space</strong>: Always include swap, even with abundant RAM</li>\n<li><strong>Oversized boot partition</strong>: 1GB is typically sufficient</li>\n</ul>\n<h3 id=\"file-system-choices\">File System Choices</h3><ul>\n<li><strong>Using FAT32 for Linux systems</strong>: Use ext4 for better performance and features</li>\n<li><strong>Wrong partition types</strong>: Ensure proper partition type codes in fdisk</li>\n</ul>\n<h3 id=\"planning-failures\">Planning Failures</h3><ul>\n<li><strong>No room for growth</strong>: Leave 10-20% unallocated space for future expansion</li>\n<li><strong>Poor separation</strong>: Keep logs separate from system files[17]</li>\n</ul>\n<h2 id=\"troubleshooting-common-issues\">Troubleshooting Common Issues</h2><h3 id=\"partition-recognition-problems\">Partition Recognition Problems</h3><pre class=\"code-block\" data-language=\"bash\"><code># Force kernel to re-read partition table\nsudo partprobe /dev/sdb\n\n# Or reboot if partprobe fails\nsudo reboot</code></pre><h3 id=\"alignment-issues\">Alignment Issues</h3><ul>\n<li>Always accept default start sectors for optimal SSD alignment</li>\n<li>Use <code>parted</code> with <code>align-check</code> command to verify alignment</li>\n</ul>\n<h3 id=\"mount-failures\">Mount Failures</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check file system errors\nsudo fsck /dev/sdb1\n\n# Check /etc/fstab syntax\nsudo mount -a</code></pre><h2 id=\"backup-and-recovery\">Backup and Recovery</h2><h3 id=\"before-partitioning\">Before Partitioning</h3><ol>\n<li><strong>Backup important data</strong> to external storage</li>\n<li><strong>Document current partition layout</strong>:<pre class=\"code-block\" data-language=\"bash\"><code>sudo fdisk -l &gt; partition-backup.txt\nsudo blkid &gt; uuid-backup.txt</code></pre></li>\n</ol>\n<h3 id=\"partition-table-backup\">Partition Table Backup</h3><pre class=\"code-block\" data-language=\"bash\"><code># Backup partition table\nsudo dd if=/dev/sdb of=partition-table.backup bs=512 count=1\n\n# Restore partition table (emergency use only)\nsudo dd if=partition-table.backup of=/dev/sdb bs=512 count=1</code></pre><h2 id=\"integration-with-other-server-setup-tasks\">Integration with Other Server Setup Tasks</h2><p>After partitioning:</p>\n<ul>\n<li><strong>Next</strong>: Set up mount points and configure <code>/etc/fstab</code></li>\n<li><strong>Storage Expansion</strong>: See <a href=\"external-storage.md\" target=\"_blank\">external-storage.md</a> for adding more drives</li>\n<li><strong>Initial Setup</strong>: Complete OS installation using your new partition layout</li>\n<li><strong>Monitoring</strong>: Set up disk usage monitoring for each partition</li>\n</ul>\n<h2 id=\"security-considerations\">Security Considerations</h2><ol>\n<li><strong>Encrypt sensitive partitions</strong> using LUKS</li>\n<li><strong>Set appropriate mount options</strong> (<code>noexec</code>, <code>nosuid</code> for <code>/tmp</code>)</li>\n<li><strong>Regular monitoring</strong> of partition usage</li>\n<li><strong>Access controls</strong> on mount points</li>\n</ol>\n<h2 id=\"conclusion\">Conclusion</h2><p>Proper disk partitioning is fundamental to stable, maintainable community servers. Use graphical tools like GParted for simplicity or command-line tools for automation and advanced features. Always plan partition layouts based on your specific use case, maintain backups, and monitor disk usage regularly.</p>\n<p>The investment in proper partitioning pays dividends in system stability, performance, and maintenance efficiency throughout your server&#39;s lifecycle.</p>\n"}, "guides/external-storage.md": {"hash": "12a2fae8669b16d0a649861ca6c50661", "content": "<h1 id=\"external-storage-guide-for-community-server-expansion\">External Storage Guide for Community Server Expansion</h1><p>This guide covers connecting, formatting, and mounting additional storage devices (USB, SATA, SSD) to expand capacity and improve data resilience in community server deployments.</p>\n<h2 id=\"understanding-external-storage-options\">Understanding External Storage Options</h2><p>External storage provides crucial capabilities for community servers:</p>\n<ul>\n<li><strong>Capacity Expansion</strong>: Add storage without replacing existing drives</li>\n<li><strong>Data Redundancy</strong>: Create backup copies on separate physical devices</li>\n<li><strong>Performance Enhancement</strong>: Use SSDs for high-performance workloads</li>\n<li><strong>Modular Architecture</strong>: Easy addition and removal of storage as needs change</li>\n</ul>\n<h3 id=\"storage-connection-types\">Storage Connection Types</h3><table class=\"pdf-table\">\n        <thead><tr>\n<th>Connection Type</th>\n<th>Speed</th>\n<th>Best Use Case</th>\n<th>Compatibility</th>\n</tr>\n</thead>\n        <tbody><tr>\n<td>USB 3.0/3.1</td>\n<td>5-10 Gbps</td>\n<td>Backup drives, portable storage</td>\n<td>Universal</td>\n</tr>\n<tr>\n<td>USB-C</td>\n<td>Up to 40 Gbps</td>\n<td>High-speed external SSDs</td>\n<td>Modern systems</td>\n</tr>\n<tr>\n<td>eSATA</td>\n<td>6 Gbps</td>\n<td>Permanent external storage</td>\n<td>Desktop systems</td>\n</tr>\n<tr>\n<td>Thunderbolt</td>\n<td>Up to 40 Gbps</td>\n<td>Professional workloads</td>\n<td>Mac/high-end PC</td>\n</tr>\n<tr>\n<td>Internal SATA</td>\n<td>6 Gbps</td>\n<td>Primary storage expansion</td>\n<td>Desktop/server</td>\n</tr>\n</tbody>\n      </table><h2 id=\"pre-connection-preparation\">Pre-Connection Preparation</h2><h3 id=\"inventory-current-storage\">Inventory Current Storage</h3><p>Before adding external storage, document your current setup:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># List all block devices\nlsblk\n\n# Show disk usage\ndf -h\n\n# Display detailed disk information\nsudo fdisk -l</code></pre><h3 id=\"check-available-ports\">Check Available Ports</h3><p>Identify available connection ports:</p>\n<ul>\n<li>USB 3.0+ ports (blue connectors typically)</li>\n<li>eSATA ports</li>\n<li>Internal SATA connections</li>\n<li>Power requirements for 3.5&quot; drives</li>\n</ul>\n<blockquote>\n<p><strong>⚠️ Power Requirements</strong></p>\n<p>3.5&quot; SATA drives require separate power connections. 2.5&quot; drives can typically be powered via USB. Plan your power infrastructure accordingly.</p>\n</blockquote>\n<h2 id=\"step-1-physical-connection\">Step 1: Physical Connection</h2><h3 id=\"usb-drive-connection\">USB Drive Connection</h3><p><code>[Mounting External Drive](mounting-external-drive.png)</code></p>\n<ol>\n<li><strong>Locate appropriate USB port</strong> (USB 3.0+ recommended for better performance)</li>\n<li><strong>Connect the drive</strong> ensuring secure connection</li>\n<li><strong>For USB-SATA adapters</strong>: Connect both USB and power cables if required</li>\n<li><strong>Wait for recognition</strong> (typically 5-10 seconds)</li>\n</ol>\n<h3 id=\"sata-drive-connection\">SATA Drive Connection</h3><p>For internal SATA connections:</p>\n<ol>\n<li><strong>Power down the system completely</strong></li>\n<li><strong>Connect SATA data cable</strong> to motherboard SATA port</li>\n<li><strong>Connect SATA power cable</strong> from power supply</li>\n<li><strong>Secure drive in mounting bracket</strong> if permanent installation</li>\n<li><strong>Power on and check BIOS/UEFI</strong> for drive recognition</li>\n</ol>\n<h3 id=\"verification-of-physical-connection\">Verification of Physical Connection</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check system messages for new device\ndmesg | tail -20\n\n# List all SCSI/SATA devices\nlsscsi\n\n# Monitor real-time device detection\nsudo tail -f /var/log/syslog</code></pre><h2 id=\"step-2-drive-detection-and-identification\">Step 2: Drive Detection and Identification</h2><h3 id=\"automatic-detection\">Automatic Detection</h3><p>Most Linux systems automatically detect new storage devices:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># List all block devices with file system info\nlsblk -f\n\n# Show all storage devices\nsudo fdisk -l\n\n# Display device information by ID\nls -la /dev/disk/by-id/</code></pre><h3 id=\"manual-device-scanning\">Manual Device Scanning</h3><p>If automatic detection fails:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Rescan SCSI devices\necho &quot;- - -&quot; | sudo tee /sys/class/scsi_host/host*/scan\n\n# Rescan specific SCSI host (replace X with host number)\necho &quot;1&quot; | sudo tee /sys/class/block/sdX/device/rescan</code></pre><h3 id=\"identifying-your-new-drive\">Identifying Your New Drive</h3><p>Look for the new device in the output:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Example output showing new USB drive\n$ lsblk\nNAME   MAJ:MIN RM  SIZE RO TYPE MOUNTPOINT\nsda      8:0    0   10G  0 disk \n├─sda1   8:1    0    1G  0 part /boot\n└─sda2   8:2    0    9G  0 part /\nsdb      8:16   1  7.4G  0 disk  ← New USB drive</code></pre><h2 id=\"step-3-drive-preparation-and-formatting\">Step 3: Drive Preparation and Formatting</h2><h3 id=\"check-existing-file-system\">Check Existing File System</h3><p>Before formatting, check if the drive already contains data:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Check for existing file systems\nsudo blkid /dev/sdb\n\n# Attempt to identify file system type\nsudo file -s /dev/sdb1</code></pre><h3 id=\"create-partition-table-if-needed\">Create Partition Table (if needed)</h3><p>For completely new drives:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Create GPT partition table (recommended for drives &gt;2TB)\nsudo parted /dev/sdb mklabel gpt\n\n# Or create MBR partition table (for compatibility)\nsudo parted /dev/sdb mklabel msdos</code></pre><h3 id=\"create-partitions\">Create Partitions</h3><h4 id=\"using-parted-recommended\">Using parted (recommended)</h4><pre class=\"code-block\" data-language=\"bash\"><code># Start parted on the new drive\nsudo parted /dev/sdb\n\n# Create a single partition using all available space\n(parted) mkpart primary ext4 0% 100%\n\n# Exit parted\n(parted) quit</code></pre><h4 id=\"using-fdisk\">Using fdisk</h4><pre class=\"code-block\" data-language=\"bash\"><code># Start fdisk\nsudo fdisk /dev/sdb\n\n# Create new partition (n), primary (p), partition 1, accept defaults\n# Write changes (w)</code></pre><h3 id=\"format-the-partition\">Format the Partition</h3><p><code>[Filesystem Format Screenshot](format-drive-ui.png)</code></p>\n<p>Choose the appropriate file system based on your needs:</p>\n<h4 id=\"ext4-recommended-for-linux\">ext4 (Recommended for Linux)</h4><pre class=\"code-block\" data-language=\"bash\"><code># Format with ext4 file system\nsudo mkfs.ext4 /dev/sdb1\n\n# Format with label for easy identification\nsudo mkfs.ext4 -L &quot;backup-storage&quot; /dev/sdb1</code></pre><h4 id=\"ntfs-for-windows-compatibility\">NTFS (for Windows compatibility)</h4><pre class=\"code-block\" data-language=\"bash\"><code># Install NTFS tools if not available\nsudo apt install ntfs-3g\n\n# Format with NTFS\nsudo mkfs.ntfs -f -L &quot;shared-storage&quot; /dev/sdb1</code></pre><h4 id=\"exfat-cross-platform-compatibility\">exFAT (cross-platform compatibility)</h4><pre class=\"code-block\" data-language=\"bash\"><code># Install exFAT tools\nsudo apt install exfat-fuse exfat-utils\n\n# Format with exFAT\nsudo mkfs.exfat -n &quot;portable-storage&quot; /dev/sdb1</code></pre><h3 id=\"verify-formatting\">Verify Formatting</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check the new file system\nsudo blkid /dev/sdb1\n\n# Verify file system integrity\nsudo fsck /dev/sdb1</code></pre><h2 id=\"step-4-mounting-external-storage\">Step 4: Mounting External Storage</h2><h3 id=\"create-mount-point\">Create Mount Point</h3><pre class=\"code-block\" data-language=\"bash\"><code># Create directory for mount point\nsudo mkdir -p /mnt/external-storage\n\n# Or use a more descriptive path\nsudo mkdir -p /srv/backup-storage</code></pre><h3 id=\"temporary-mounting\">Temporary Mounting</h3><p>For testing or temporary access:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Mount the drive\nsudo mount /dev/sdb1 /mnt/external-storage\n\n# Verify mount\ndf -h | grep external-storage\n\n# Check mount details\nmount | grep sdb1</code></pre><h3 id=\"permanent-mounting\">Permanent Mounting</h3><p>For automatic mounting at boot, edit <code>/etc/fstab</code>:</p>\n<h4 id=\"get-drive-uuid\">Get Drive UUID</h4><pre class=\"code-block\" data-language=\"bash\"><code># Find the UUID of your partition\nsudo blkid /dev/sdb1\n# Output: /dev/sdb1: UUID=&quot;12345678-1234-1234-1234-123456789012&quot; TYPE=&quot;ext4&quot; LABEL=&quot;backup-storage&quot;</code></pre><h4 id=\"edit-fstab\">Edit fstab</h4><pre class=\"code-block\" data-language=\"bash\"><code># Backup current fstab\nsudo cp /etc/fstab /etc/fstab.backup\n\n# Edit fstab\nsudo nano /etc/fstab</code></pre><p>Add a line for your new drive:</p>\n<pre class=\"code-block\" data-language=\"text\"><code># External backup storage\nUUID=12345678-1234-1234-1234-123456789012 /srv/backup-storage ext4 defaults,nofail 0 2</code></pre><h4 id=\"fstab-options-explained\">fstab Options Explained</h4><ul>\n<li><code>defaults</code>: Use default mount options</li>\n<li><code>nofail</code>: Don&#39;t fail boot if drive is missing[20]</li>\n<li><code>0</code>: Don&#39;t dump this file system</li>\n<li><code>2</code>: Check file system after root partition</li>\n</ul>\n<h4 id=\"test-fstab-entry\">Test fstab Entry</h4><pre class=\"code-block\" data-language=\"bash\"><code># Test mount all entries in fstab\nsudo mount -a\n\n# Verify successful mount\ndf -h</code></pre><h2 id=\"step-5-setting-permissions-and-ownership\">Step 5: Setting Permissions and Ownership</h2><h3 id=\"basic-permissions\">Basic Permissions</h3><pre class=\"code-block\" data-language=\"bash\"><code># Set appropriate ownership\nsudo chown -R $USER:$USER /srv/backup-storage\n\n# Set permissions for user access\nsudo chmod 755 /srv/backup-storage</code></pre><h3 id=\"group-based-access\">Group-Based Access</h3><p>For multi-user server environments:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Create a group for storage access\nsudo groupadd storage-users\n\n# Add users to the group\nsudo usermod -a -G storage-users username1\nsudo usermod -a -G storage-users username2\n\n# Set group ownership\nsudo chgrp -R storage-users /srv/backup-storage\n\n# Set group write permissions\nsudo chmod -R 775 /srv/backup-storage</code></pre><h2 id=\"advanced-configuration-options\">Advanced Configuration Options</h2><h3 id=\"performance-optimization\">Performance Optimization</h3><h4 id=\"mount-options-for-ssds\">Mount Options for SSDs</h4><p>For SSD external drives, use optimized mount options:</p>\n<pre class=\"code-block\" data-language=\"text\"><code>UUID=12345678-1234-1234-1234-123456789012 /srv/ssd-storage ext4 defaults,noatime,discard,nofail 0 2</code></pre><ul>\n<li><code>noatime</code>: Don&#39;t update access times (improves performance)</li>\n<li><code>discard</code>: Enable TRIM commands for SSD health</li>\n</ul>\n<h4 id=\"io-scheduling\">I/O Scheduling</h4><p>For external drives, consider deadline or noop schedulers:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Check current scheduler\ncat /sys/block/sdb/queue/scheduler\n\n# Set deadline scheduler for external drive\necho deadline | sudo tee /sys/block/sdb/queue/scheduler</code></pre><h3 id=\"encryption-setup\">Encryption Setup</h3><p>For sensitive data, use LUKS encryption:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Install encryption tools\nsudo apt install cryptsetup\n\n# Create encrypted container\nsudo cryptsetup luksFormat /dev/sdb1\n\n# Open encrypted device\nsudo cryptsetup luksOpen /dev/sdb1 encrypted-storage\n\n# Format the encrypted device\nsudo mkfs.ext4 /dev/mapper/encrypted-storage\n\n# Mount encrypted device\nsudo mkdir /mnt/encrypted-storage\nsudo mount /dev/mapper/encrypted-storage /mnt/encrypted-storage</code></pre><h2 id=\"drive-health-monitoring\">Drive Health Monitoring</h2><h3 id=\"smart-monitoring\">S.M.A.R.T. Monitoring</h3><pre class=\"code-block\" data-language=\"bash\"><code># Install smartmontools\nsudo apt install smartmontools\n\n# Check drive health\nsudo smartctl -H /dev/sdb\n\n# Full S.M.A.R.T. information\nsudo smartctl -a /dev/sdb\n\n# Run short self-test\nsudo smartctl -t short /dev/sdb</code></pre><h3 id=\"regular-health-checks\">Regular Health Checks</h3><p>Create a monitoring script:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code>#!/bin/bash\n# External drive health check script\n\nDRIVE=&quot;/dev/sdb&quot;\nLOG_FILE=&quot;/var/log/external-drive-health.log&quot;\n\necho &quot;$(date): Checking drive health for $DRIVE&quot; &gt;&gt; $LOG_FILE\nsudo smartctl -H $DRIVE &gt;&gt; $LOG_FILE 2&gt;&amp;1\n\n# Check mount status\nif mountpoint -q /srv/backup-storage; then\n    echo &quot;$(date): Drive mounted successfully&quot; &gt;&gt; $LOG_FILE\nelse\n    echo &quot;$(date): WARNING - Drive not mounted&quot; &gt;&gt; $LOG_FILE\nfi</code></pre><h2 id=\"backup-and-redundancy-strategies\">Backup and Redundancy Strategies</h2><h3 id=\"automated-backup-scripts\">Automated Backup Scripts</h3><pre class=\"code-block\" data-language=\"bash\"><code>#!/bin/bash\n# Simple backup script for external storage\n\nSOURCE_DIR=&quot;/srv/data&quot;\nBACKUP_DIR=&quot;/srv/backup-storage/daily-backup&quot;\nDATE=$(date +%Y%m%d)\n\n# Create dated backup directory\nmkdir -p &quot;$BACKUP_DIR/$DATE&quot;\n\n# Perform incremental backup using rsync\nrsync -av --link-dest=&quot;$BACKUP_DIR/latest&quot; \\\n      &quot;$SOURCE_DIR/&quot; &quot;$BACKUP_DIR/$DATE/&quot;\n\n# Update latest symlink\nln -nfs &quot;$DATE&quot; &quot;$BACKUP_DIR/latest&quot;\n\necho &quot;Backup completed: $(date)&quot; &gt;&gt; /var/log/backup.log</code></pre><h3 id=\"raid-configuration\">RAID Configuration</h3><p>For multiple external drives, consider software RAID:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Install mdadm\nsudo apt install mdadm\n\n# Create RAID 1 mirror with two drives\nsudo mdadm --create /dev/md0 --level=1 --raid-devices=2 /dev/sdb1 /dev/sdc1\n\n# Format and mount RAID array\nsudo mkfs.ext4 /dev/md0\nsudo mkdir /srv/raid-storage\nsudo mount /dev/md0 /srv/raid-storage</code></pre><h2 id=\"troubleshooting-common-issues\">Troubleshooting Common Issues</h2><h3 id=\"drive-not-detected\">Drive Not Detected</h3><ol>\n<li><strong>Check physical connections</strong></li>\n<li><strong>Verify power supply</strong> (especially for 3.5&quot; drives)</li>\n<li><strong>Try different USB port</strong> or cable</li>\n<li><strong>Check system logs</strong>:<pre class=\"code-block\" data-language=\"bash\"><code>dmesg | grep -i error\nsudo journalctl -f</code></pre></li>\n</ol>\n<h3 id=\"mount-failures\">Mount Failures</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check file system errors\nsudo fsck /dev/sdb1\n\n# Force mount with specific type\nsudo mount -t ext4 /dev/sdb1 /mnt/external-storage\n\n# Check for conflicting mounts\nmount | grep sdb1</code></pre><h3 id=\"permission-issues\">Permission Issues</h3><pre class=\"code-block\" data-language=\"bash\"><code># Fix ownership issues\nsudo chown -R $USER:$USER /mnt/external-storage\n\n# Check SELinux contexts (if applicable)\nls -Z /mnt/external-storage</code></pre><h3 id=\"performance-problems\">Performance Problems</h3><pre class=\"code-block\" data-language=\"bash\"><code># Check I/O statistics\niostat 5\n\n# Monitor disk usage\niotop\n\n# Test read/write performance\nsudo hdparm -tT /dev/sdb</code></pre><h2 id=\"security-considerations\">Security Considerations</h2><h3 id=\"access-control\">Access Control</h3><ol>\n<li><strong>Limit mount points</strong> to necessary directories only</li>\n<li><strong>Use noexec option</strong> for data-only partitions</li>\n<li><strong>Regular permission audits</strong>:<pre class=\"code-block\" data-language=\"bash\"><code>find /srv/backup-storage -type f -perm 777</code></pre></li>\n</ol>\n<h3 id=\"data-protection\">Data Protection</h3><ol>\n<li><strong>Encrypt sensitive external storage</strong></li>\n<li><strong>Regular backup verification</strong></li>\n<li><strong>Secure disposal</strong> of old drives</li>\n<li><strong>Access logging</strong>:<pre class=\"code-block\" data-language=\"bash\"><code># Monitor file access\ntail -f /var/log/audit/audit.log | grep external-storage</code></pre></li>\n</ol>\n<h2 id=\"integration-with-server-services\">Integration with Server Services</h2><h3 id=\"web-server-integration\">Web Server Integration</h3><pre class=\"code-block\" data-language=\"bash\"><code># Create web-accessible storage directory\nsudo mkdir -p /srv/backup-storage/web-data\nsudo chown -R www-data:www-data /srv/backup-storage/web-data\n\n# Add to web server configuration\n# Apache example:\n# Alias /backup-data /srv/backup-storage/web-data</code></pre><h3 id=\"database-storage\">Database Storage</h3><pre class=\"code-block\" data-language=\"bash\"><code># Stop database service\nsudo systemctl stop mysql\n\n# Move data directory to external storage\nsudo mv /var/lib/mysql /srv/backup-storage/mysql-data\n\n# Create symlink\nsudo ln -s /srv/backup-storage/mysql-data /var/lib/mysql\n\n# Restart database\nsudo systemctl start mysql</code></pre><h2 id=\"maintenance-schedule\">Maintenance Schedule</h2><h3 id=\"daily-tasks\">Daily Tasks</h3><ul>\n<li>Monitor mount status</li>\n<li>Check available space</li>\n<li>Review system logs for errors</li>\n</ul>\n<h3 id=\"weekly-tasks\">Weekly Tasks</h3><ul>\n<li>Run file system checks</li>\n<li>Verify backup integrity</li>\n<li>Update usage documentation</li>\n</ul>\n<h3 id=\"monthly-tasks\">Monthly Tasks</h3><ul>\n<li>Run S.M.A.R.T. tests</li>\n<li>Review performance metrics</li>\n<li>Plan capacity expansion</li>\n</ul>\n<h2 id=\"integration-with-other-guides\">Integration with Other Guides</h2><p>This external storage setup complements other server management tasks:</p>\n<ul>\n<li><strong>After OS Installation</strong>: Follow <a href=\"etcher-guide.md\" target=\"_blank\">etcher-guide.md</a> for initial system setup</li>\n<li><strong>Before Storage Setup</strong>: Complete <a href=\"disk-partitioning.md\" target=\"_blank\">disk-partitioning.md</a> for main system layout</li>\n<li><strong>Security Hardening</strong>: Implement access controls and encryption</li>\n<li><strong>Monitoring Integration</strong>: Add storage metrics to server monitoring</li>\n</ul>\n<h2 id=\"conclusion\">Conclusion</h2><p>External storage expansion is crucial for growing community server deployments. Proper planning, configuration, and maintenance ensure reliable operation and data protection. Always prioritize data safety through redundancy, monitoring, and regular maintenance procedures.</p>\n<p>The modular approach to external storage allows community servers to scale efficiently while maintaining budget consciousness and operational simplicity.</p>\n"}, "guides/etcher-guide.md": {"hash": "a19a53f685bf36aab1abd6582bcde8a9", "content": "<h1 id=\"complete-guide-to-using-etcher-for-os-image-flashing\">Complete Guide to Using Etcher for OS Image Flashing</h1><p>This guide provides step-by-step instructions for using balenaEtcher to safely write operating system images to SD cards and USB drives for community server deployments.</p>\n<h2 id=\"what-is-etcher\">What is <PERSON><PERSON><PERSON>?</h2><p>balenaEtcher is a free, open-source, cross-platform tool designed to flash OS images onto SD cards and USB drives safely and easily[1]. It&#39;s maintained by balena and works on Windows, macOS, and Linux systems[1]. Etcher provides built-in validation to ensure error-free flashing and has an intuitive three-step interface[1].</p>\n<h2 id=\"prerequisites\">Prerequisites</h2><ul>\n<li>A computer running Windows 10+, macOS 10.10+, or most Linux distributions[1]</li>\n<li>An SD card or USB drive (minimum 8GB capacity recommended for most OS images)[2]</li>\n<li>Downloaded OS image file (.iso, .img, or compressed formats)</li>\n<li>Administrative privileges on your computer</li>\n</ul>\n<blockquote>\n<p><strong>⚠️ Important Safety Warning</strong></p>\n<p><PERSON><PERSON><PERSON> will completely erase all data on the target drive. Always verify you&#39;ve selected the correct drive before flashing. Keep your original media safe and create backups of important data[1].</p>\n</blockquote>\n<h2 id=\"step-1-download-and-install-etcher\">Step 1: Download and Install Etcher</h2><h3 id=\"for-windows-users\">For Windows Users</h3><ol>\n<li>Visit the official balenaEtcher website at <a href=\"https://etcher.balena.io/\" target=\"_blank\">https://etcher.balena.io/</a></li>\n<li>Click the green &quot;Download Etcher&quot; button</li>\n<li>Select the Windows version (64-bit recommended for modern systems)[6]</li>\n<li>Once downloaded, locate the installer in your Downloads folder</li>\n<li>Double-click the installer file and follow the setup wizard</li>\n<li>Accept the license agreement and complete the installation[6]</li>\n</ol>\n<h3 id=\"for-linux-users\">For Linux Users</h3><h4 id=\"appimage-method-recommended\">AppImage Method (Recommended)</h4><ol>\n<li>Download the Linux AppImage from the official website[3]</li>\n<li>Navigate to your Downloads folder in the terminal</li>\n<li>Make the file executable:<pre class=\"code-block\" data-language=\"bash\"><code>chmod +x balena-etcher-*.AppImage</code></pre></li>\n<li>Run Etcher by double-clicking the file or using:<pre class=\"code-block\" data-language=\"bash\"><code>./balena-etcher-*.AppImage</code></pre></li>\n</ol>\n<h4 id=\"package-installation-method\">Package Installation Method</h4><p>For Ubuntu/Debian systems:</p>\n<ol>\n<li>Download the .deb package from the releases page[3]</li>\n<li>Install using:<pre class=\"code-block\" data-language=\"bash\"><code>sudo dpkg -i balena-etcher_*.deb\nsudo apt-get install -f  # Fix any dependency issues</code></pre></li>\n</ol>\n<h3 id=\"for-macos-users\">For macOS Users</h3><ol>\n<li>Download the macOS version from the official website</li>\n<li>Open the downloaded .dmg file</li>\n<li>Drag Etcher to your Applications folder</li>\n<li>Launch from Applications or Spotlight</li>\n</ol>\n<h2 id=\"step-2-prepare-your-image-file\">Step 2: Prepare Your Image File</h2><p>Before flashing, ensure your OS image is ready:</p>\n<ol>\n<li><strong>Verify the Image</strong>: Always verify the checksum of downloaded images to ensure integrity</li>\n<li><strong>Extract if Necessary</strong>: If your image is compressed (.zip, .gz, .xz), you may need to extract it first (though Etcher can handle many compressed formats directly)</li>\n<li><strong>Check Image Size</strong>: Ensure your target drive has sufficient capacity for the image</li>\n</ol>\n<blockquote>\n<p><strong>💡 Pro Tip</strong></p>\n<p>If you&#39;re downloading from a URL and flashing fails, try downloading the file locally first and flash from the file instead[1].</p>\n</blockquote>\n<h2 id=\"step-3-flash-your-image\">Step 3: Flash Your Image</h2><h3 id=\"main-interface-overview\">Main Interface Overview</h3><p>When you open Etcher, you&#39;ll see a clean, three-step interface:</p>\n<p><code>[Etcher Main Screen](etcher-main-screen.png)</code></p>\n<p>The interface consists of three main sections:</p>\n<ol>\n<li><strong>Flash from file</strong> - Select your OS image</li>\n<li><strong>Select target</strong> - Choose your SD card or USB drive  </li>\n<li><strong>Flash!</strong> - Start the flashing process</li>\n</ol>\n<h3 id=\"detailed-flashing-process\">Detailed Flashing Process</h3><h4 id=\"step-31-select-your-image\">Step 3.1: Select Your Image</h4><ol>\n<li>Click &quot;Flash from file&quot; (the first button)[6]</li>\n<li>Navigate to your downloaded OS image file</li>\n<li>Select the image file (.iso, .img, etc.)</li>\n<li>Etcher will display the selected file name and size</li>\n</ol>\n<p><code>[Selecting Image and Target](etcher-select-image.png)</code></p>\n<h4 id=\"step-32-select-target-drive\">Step 3.2: Select Target Drive</h4><ol>\n<li>Insert your SD card or USB drive</li>\n<li>Etcher will automatically detect available drives</li>\n<li>Click &quot;Select target&quot; if not automatically selected</li>\n<li><strong>CAREFULLY</strong> verify you&#39;ve selected the correct drive</li>\n<li>Check the drive size and name to confirm it&#39;s the right device</li>\n</ol>\n<blockquote>\n<p><strong>⚠️ Critical Safety Check</strong></p>\n<p>Double-check the target drive selection. Writing to the wrong drive will destroy your data permanently. Look for drive size, manufacturer, and model to confirm the correct device.</p>\n</blockquote>\n<h4 id=\"step-33-start-flashing\">Step 3.3: Start Flashing</h4><ol>\n<li>Click the &quot;Flash!&quot; button to begin the process[6]</li>\n<li>Etcher will prompt for administrator/sudo password if required[6]</li>\n<li>The flashing process includes:<ul>\n<li>Writing the image to the drive</li>\n<li>Automatic verification of the written data</li>\n<li>Progress indication with time estimates</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"understanding-the-flashing-process\">Understanding the Flashing Process</h3><p>During flashing, Etcher performs several operations:</p>\n<ol>\n<li><strong>Preparation</strong>: Unmounts the target drive and prepares for writing</li>\n<li><strong>Flashing</strong>: Writes the image data sector by sector</li>\n<li><strong>Verification</strong>: Reads back the written data to ensure accuracy[1]</li>\n<li><strong>Completion</strong>: Safely ejects the drive when finished</li>\n</ol>\n<h2 id=\"step-4-post-flash-verification\">Step 4: Post-Flash Verification</h2><p>After successful flashing:</p>\n<ol>\n<li><strong>Check Completion Message</strong>: Look for &quot;Flash Complete!&quot; message</li>\n<li><strong>Safely Remove</strong>: Use your operating system&#39;s &quot;Safely Remove&quot; function[2]</li>\n<li><strong>Test the Drive</strong>: Boot from the drive on target hardware to verify functionality</li>\n</ol>\n<h2 id=\"common-issues-and-troubleshooting\">Common Issues and Troubleshooting</h2><h3 id=\"flash-failed-errors\">Flash Failed Errors</h3><p>If you encounter flash failures[7]:</p>\n<ol>\n<li><strong>Try Different Hardware</strong>: Test with another SD card, USB drive, or USB port</li>\n<li><strong>Check Drive Quality</strong>: Faulty drives or adapters are common causes</li>\n<li><strong>Disable Windows Features</strong> (Windows users):<ul>\n<li>Disable Windows Search indexing for removable drives</li>\n<li>Add <code>DisableRemovableDriveIndexing</code> registry key to prevent system interference[5]</li>\n</ul>\n</li>\n</ol>\n<h3 id=\"verification-failures\">Verification Failures</h3><p>If verification consistently fails[5]:</p>\n<ol>\n<li><strong>Use Different Adapter</strong>: Try a different USB adapter or card reader</li>\n<li><strong>Check System Resources</strong>: Ensure sufficient RAM and CPU availability</li>\n<li><strong>Download Fresh Image</strong>: Re-download the OS image in case of corruption</li>\n</ol>\n<h3 id=\"performance-issues\">Performance Issues</h3><p>For better performance:</p>\n<ol>\n<li><strong>Use USB 3.0 Ports</strong>: Faster transfer speeds reduce flashing time</li>\n<li><strong>Close Other Applications</strong>: Free up system resources during flashing</li>\n<li><strong>Use High-Quality Drives</strong>: Invest in reliable, fast SD cards and USB drives</li>\n</ol>\n<blockquote>\n<p><strong>💡 Troubleshooting Tip</strong></p>\n<p>Open Etcher&#39;s console with Ctrl+Shift+I (Windows/Linux) or Cmd+Option+I (macOS) to view detailed error messages[7].</p>\n</blockquote>\n<h2 id=\"advanced-options\">Advanced Options</h2><h3 id=\"command-line-usage\">Command Line Usage</h3><p>For advanced users, Etcher offers command-line functionality:</p>\n<pre class=\"code-block\" data-language=\"bash\"><code># Example command-line usage\netcher --image path/to/image.img --drive /dev/sdX --yes</code></pre><h3 id=\"batch-flashing\">Batch Flashing</h3><p>Etcher supports flashing to multiple drives simultaneously when multiple compatible drives are connected.</p>\n<h2 id=\"security-considerations\">Security Considerations</h2><ol>\n<li><strong>Verify Sources</strong>: Only download OS images from official sources</li>\n<li><strong>Check Checksums</strong>: Always verify image integrity before flashing</li>\n<li><strong>Secure Disposal</strong>: Properly wipe drives containing sensitive data before reuse</li>\n<li><strong>Access Control</strong>: Limit access to flashing tools in shared environments</li>\n</ol>\n<h2 id=\"integration-with-other-guides\">Integration with Other Guides</h2><p>After successfully flashing your OS image:</p>\n<ul>\n<li><strong>Next Step</strong>: See our <a href=\"disk-partitioning.md\" target=\"_blank\">disk-partitioning.md</a> guide for customizing disk layouts</li>\n<li><strong>Storage Expansion</strong>: Refer to <a href=\"external-storage.md\" target=\"_blank\">external-storage.md</a> for adding additional storage</li>\n<li><strong>Backup Strategy</strong>: Always maintain original image files and create regular backups</li>\n</ul>\n<h2 id=\"best-practices-for-community-deployments\">Best Practices for Community Deployments</h2><ol>\n<li><strong>Standardize Images</strong>: Use consistent, tested OS images across deployments</li>\n<li><strong>Label Drives</strong>: Clearly label flashed drives with OS version and date</li>\n<li><strong>Test Before Deployment</strong>: Boot test every flashed drive before field deployment</li>\n<li><strong>Maintain Image Library</strong>: Keep a repository of verified, up-to-date OS images</li>\n<li><strong>Document Configurations</strong>: Record image versions and customizations for each deployment</li>\n</ol>\n<h2 id=\"conclusion\">Conclusion</h2><p>balenaEtcher provides a reliable, user-friendly solution for flashing OS images in community server deployments. Its built-in verification and cross-platform compatibility make it an ideal choice for volunteer technicians and community organizations. Always prioritize data safety by verifying target drives and maintaining proper backups.</p>\n<p>For additional support, visit the balenaEtcher community forums or check the official documentation at <a href=\"https://etcher.balena.io/\" target=\"_blank\">https://etcher.balena.io/</a>.</p>\n"}, "guides/deployment-best-practices.md": {"hash": "b45a5127ac0c547563754ee4fda3e328", "content": "<h1 id=\"mini-pc-ai-deployment-best-practices-guide\">Mini PC AI Deployment Best Practices Guide</h1><h2 id=\"pre-deployment-checklist\">Pre-Deployment Checklist</h2><h3 id=\"infrastructure-assessment\">Infrastructure Assessment</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Available electrical outlets and power capacity</li>\n<li><input disabled=\"\" type=\"checkbox\"> Network bandwidth and latency requirements</li>\n<li><input disabled=\"\" type=\"checkbox\"> Physical space dimensions and ventilation</li>\n<li><input disabled=\"\" type=\"checkbox\"> Ambient temperature and humidity levels</li>\n<li><input disabled=\"\" type=\"checkbox\"> Security requirements and physical access control</li>\n</ul>\n<h3 id=\"hardware-preparation\">Hardware Preparation</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Verify all component compatibility before purchase</li>\n<li><input disabled=\"\" type=\"checkbox\"> Test mini PC and eGPU combination if applicable</li>\n<li><input disabled=\"\" type=\"checkbox\"> Ensure adequate PSU wattage for full system load</li>\n<li><input disabled=\"\" type=\"checkbox\"> Check thermal throttling under sustained load</li>\n<li><input disabled=\"\" type=\"checkbox\"> Validate all connectivity options (USB, video, network)</li>\n</ul>\n<h3 id=\"software-planning\">Software Planning</h3><ul>\n<li><input disabled=\"\" type=\"checkbox\"> Choose appropriate operating system (Linux vs Windows)</li>\n<li><input disabled=\"\" type=\"checkbox\"> Plan AI framework installation (PyTorch, TensorFlow, etc.)</li>\n<li><input disabled=\"\" type=\"checkbox\"> Prepare user management and authentication system</li>\n<li><input disabled=\"\" type=\"checkbox\"> Design backup and disaster recovery procedures</li>\n<li><input disabled=\"\" type=\"checkbox\"> Create monitoring and alerting framework</li>\n</ul>\n<h2 id=\"optimal-configuration-guidelines\">Optimal Configuration Guidelines</h2><h3 id=\"apple-silicon-mini-pcs-mac-mini-m4m4-pro\">Apple Silicon Mini PCs (Mac Mini M4/M4 Pro)</h3><p><strong>Best Use Cases:</strong></p>\n<ul>\n<li>Small teams (2-8 users)</li>\n<li>Energy-efficient deployments</li>\n<li>Stable, long-term installations</li>\n<li>Mixed AI and general computing workloads</li>\n</ul>\n<p><strong>Configuration Tips:</strong></p>\n<ul>\n<li>Order maximum RAM configuration upfront (not upgradeable)</li>\n<li>Use unified memory advantage for large model loading</li>\n<li>Configure macOS for multiple user sessions</li>\n<li>Set up remote access via Screen Sharing or VNC</li>\n<li>Enable automatic software updates during off hours</li>\n</ul>\n<p><strong>Performance Optimization:</strong></p>\n<ul>\n<li>Use Metal Performance Shaders for GPU acceleration</li>\n<li>Leverage Core ML for optimized inference</li>\n<li>Monitor memory pressure and swap usage</li>\n<li>Configure thermal throttling alerts</li>\n</ul>\n<h3 id=\"x86-mini-pcs-with-external-gpu\">x86 Mini PCs with External GPU</h3><p><strong>Best Use Cases:</strong></p>\n<ul>\n<li>Medium to large communities (5-50 users)</li>\n<li>High-performance AI workloads</li>\n<li>Flexible, upgradeable systems</li>\n<li>Development and research environments</li>\n</ul>\n<p><strong>Configuration Guidelines:</strong></p>\n<ul>\n<li>Prefer OCuLink over Thunderbolt for maximum bandwidth</li>\n<li>Ensure mini PC CPU won&#39;t bottleneck GPU performance</li>\n<li>Use high-speed NVMe storage for model caching</li>\n<li>Install adequate system RAM (32GB+ recommended)</li>\n<li>Consider dual-channel memory configuration</li>\n</ul>\n<p><strong>eGPU Selection Criteria:</strong></p>\n<ul>\n<li>Prioritize VRAM capacity over raw performance</li>\n<li>Consider power efficiency for 24/7 operation</li>\n<li>Verify driver support for target operating system</li>\n<li>Plan for future model size growth (24GB+ VRAM ideal)</li>\n</ul>\n<h2 id=\"cooling-and-thermal-management\">Cooling and Thermal Management</h2><h3 id=\"passive-cooling-solutions\">Passive Cooling Solutions</h3><ul>\n<li>Position mini PCs for optimal airflow</li>\n<li>Use laptop cooling pads for additional heat dissipation</li>\n<li>Maintain ambient temperature below 25°C (77°F)</li>\n<li>Ensure 6+ inches clearance around all vents</li>\n<li>Consider undervolting CPU for reduced heat generation</li>\n</ul>\n<h3 id=\"active-cooling-strategies\">Active Cooling Strategies</h3><ul>\n<li>Install high-quality case fans in eGPU enclosures</li>\n<li>Use temperature monitoring software with alerts</li>\n<li>Create automated fan curve profiles</li>\n<li>Consider liquid cooling for high-end GPU setups</li>\n<li>Implement thermal throttling safeguards</li>\n</ul>\n<h3 id=\"environmental-controls\">Environmental Controls</h3><ul>\n<li>Monitor room temperature and humidity</li>\n<li>Use temperature/humidity sensors with logging</li>\n<li>Consider dedicated air conditioning for server closets</li>\n<li>Ensure good room ventilation and air circulation</li>\n<li>Plan for seasonal temperature variations</li>\n</ul>\n<h2 id=\"power-management-and-efficiency\">Power Management and Efficiency</h2><h3 id=\"power-supply-planning\">Power Supply Planning</h3><ul>\n<li>Calculate total system power draw including 20% headroom</li>\n<li>Use 80 PLUS Gold or higher rated PSUs</li>\n<li>Install UPS systems for critical deployments</li>\n<li>Monitor power consumption trends over time</li>\n<li>Plan for peak vs average usage patterns</li>\n</ul>\n<h3 id=\"energy-optimization\">Energy Optimization</h3><ul>\n<li>Enable CPU power management features</li>\n<li>Use GPU power limiting for sustained workloads</li>\n<li>Configure automatic sleep/wake schedules</li>\n<li>Implement dynamic voltage and frequency scaling</li>\n<li>Monitor and optimize idle power consumption</li>\n</ul>\n<h3 id=\"power-quality-considerations\">Power Quality Considerations</h3><ul>\n<li>Use surge protectors on all equipment</li>\n<li>Install line conditioning for sensitive areas</li>\n<li>Monitor for power quality issues (voltage sags, spikes)</li>\n<li>Consider generator backup for mission-critical deployments</li>\n<li>Document power requirements for facility planning</li>\n</ul>\n<h2 id=\"security-and-access-control\">Security and Access Control</h2><h3 id=\"physical-security\">Physical Security</h3><ul>\n<li>Secure mini PCs in locked enclosures or rooms</li>\n<li>Use cable locks for portable installations</li>\n<li>Implement video surveillance for equipment areas</li>\n<li>Control physical access with key cards or codes</li>\n<li>Mark equipment with asset tags and contact information</li>\n</ul>\n<h3 id=\"network-security\">Network Security</h3><ul>\n<li>Use VPNs for all remote access</li>\n<li>Implement strong authentication (2FA/MFA)</li>\n<li>Segregate AI workloads on dedicated network segments</li>\n<li>Regular security updates and patch management</li>\n<li>Monitor network traffic for anomalies</li>\n</ul>\n<h3 id=\"data-protection\">Data Protection</h3><ul>\n<li>Encrypt all storage devices</li>\n<li>Implement regular automated backups</li>\n<li>Use secure protocols for all communications</li>\n<li>Control data access with proper user permissions</li>\n<li>Plan for secure data disposal when upgrading</li>\n</ul>\n<h2 id=\"multi-user-management\">Multi-User Management</h2><h3 id=\"user-account-setup\">User Account Setup</h3><ul>\n<li>Create standardized user profiles</li>\n<li>Implement resource quotas per user</li>\n<li>Use centralized authentication (LDAP/Active Directory)</li>\n<li>Set up automated user provisioning/deprovisioning</li>\n<li>Create shared spaces for collaboration</li>\n</ul>\n<h3 id=\"resource-allocation\">Resource Allocation</h3><ul>\n<li>Implement GPU scheduling for multiple users</li>\n<li>Set memory limits per user session</li>\n<li>Monitor CPU and storage usage by user</li>\n<li>Create fair queuing systems for batch jobs</li>\n<li>Implement priority levels for different user types</li>\n</ul>\n<h3 id=\"session-management\">Session Management</h3><ul>\n<li>Configure automatic session timeouts</li>\n<li>Implement session sharing for collaborative work</li>\n<li>Monitor active sessions and resource usage</li>\n<li>Provide user visibility into queue status</li>\n<li>Create session recording for troubleshooting</li>\n</ul>\n<h2 id=\"monitoring-and-maintenance\">Monitoring and Maintenance</h2><h3 id=\"performance-monitoring\">Performance Monitoring</h3><ul>\n<li>Track CPU, GPU, memory, and storage utilization</li>\n<li>Monitor network bandwidth and latency</li>\n<li>Set up automated alerts for performance thresholds</li>\n<li>Create performance dashboards for users</li>\n<li>Log performance trends for capacity planning</li>\n</ul>\n<h3 id=\"health-monitoring\">Health Monitoring</h3><ul>\n<li>Monitor component temperatures continuously</li>\n<li>Track fan speeds and power consumption</li>\n<li>Monitor system logs for errors and warnings</li>\n<li>Implement predictive failure detection</li>\n<li>Create maintenance schedules based on usage</li>\n</ul>\n<h3 id=\"software-maintenance\">Software Maintenance</h3><ul>\n<li>Schedule regular security updates</li>\n<li>Update AI frameworks and models regularly</li>\n<li>Monitor for driver updates and compatibility</li>\n<li>Maintain inventory of installed software</li>\n<li>Test updates in staging environment first</li>\n</ul>\n<h2 id=\"troubleshooting-common-issues\">Troubleshooting Common Issues</h2><h3 id=\"performance-problems\">Performance Problems</h3><ul>\n<li><strong>Symptoms</strong>: Slow inference, high latency</li>\n<li><strong>Causes</strong>: Thermal throttling, insufficient VRAM, network bottlenecks</li>\n<li><strong>Solutions</strong>: Improve cooling, upgrade GPU, optimize model size</li>\n</ul>\n<h3 id=\"connectivity-issues\">Connectivity Issues</h3><ul>\n<li><strong>Symptoms</strong>: eGPU not detected, unstable connections</li>\n<li><strong>Causes</strong>: Cable problems, driver issues, power supply problems</li>\n<li><strong>Solutions</strong>: Replace cables, update drivers, check PSU capacity</li>\n</ul>\n<h3 id=\"memory-problems\">Memory Problems</h3><ul>\n<li><strong>Symptoms</strong>: Out of memory errors, system crashes</li>\n<li><strong>Causes</strong>: Insufficient RAM, memory leaks, large models</li>\n<li><strong>Solutions</strong>: Add RAM, restart services, optimize model loading</li>\n</ul>\n<h3 id=\"thermal-issues\">Thermal Issues</h3><ul>\n<li><strong>Symptoms</strong>: System shutdowns, performance degradation</li>\n<li><strong>Causes</strong>: Poor ventilation, dust buildup, failed fans</li>\n<li><strong>Solutions</strong>: Clean systems, improve airflow, replace fans</li>\n</ul>\n<h2 id=\"upgrade-planning-and-future-proofing\">Upgrade Planning and Future-Proofing</h2><h3 id=\"technology-roadmap\">Technology Roadmap</h3><ul>\n<li>Monitor AI hardware development trends</li>\n<li>Plan upgrade cycles (typically 3-4 years)</li>\n<li>Budget for periodic hardware refreshes</li>\n<li>Evaluate new connectivity standards (USB4, Thunderbolt 5)</li>\n<li>Track software ecosystem evolution</li>\n</ul>\n<h3 id=\"scalability-planning\">Scalability Planning</h3><ul>\n<li>Design for horizontal scaling (multiple mini PCs)</li>\n<li>Plan network infrastructure for growth</li>\n<li>Consider cloud hybrid deployments</li>\n<li>Evaluate container orchestration platforms</li>\n<li>Design modular upgrade paths</li>\n</ul>\n<h3 id=\"budget-planning\">Budget Planning</h3><ul>\n<li>Allocate 15-20% annual budget for maintenance</li>\n<li>Plan for major refresh every 3-4 years</li>\n<li>Budget for unexpected hardware failures</li>\n<li>Consider leasing vs buying for rapid depreciation</li>\n<li>Track total cost of ownership trends</li>\n</ul>\n<h2 id=\"vendor-relationships-and-support\">Vendor Relationships and Support</h2><h3 id=\"supplier-management\">Supplier Management</h3><ul>\n<li>Establish relationships with multiple vendors</li>\n<li>Negotiate volume discounts for bulk purchases</li>\n<li>Understand warranty terms and conditions</li>\n<li>Plan for end-of-life component availability</li>\n<li>Document vendor contact information and procedures</li>\n</ul>\n<h3 id=\"support-planning\">Support Planning</h3><ul>\n<li>Identify local technical support resources</li>\n<li>Create escalation procedures for critical issues</li>\n<li>Maintain spare parts inventory for common failures</li>\n<li>Document all support interactions and resolutions</li>\n<li>Establish SLAs for different types of issues</li>\n</ul>\n<h3 id=\"community-resources\">Community Resources</h3><ul>\n<li>Join user forums and communities</li>\n<li>Share experiences and best practices</li>\n<li>Contribute to open source projects</li>\n<li>Attend relevant conferences and meetups</li>\n<li>Build network of technical contacts</li>\n</ul>\n"}, "results/global-community-networks-directory.md": {"hash": "039ec4780866b5df76ebbacfd56fdd96", "content": "<h1 id=\"global-community-networks-directory\">Global Community Networks Directory</h1><p>A comprehensive list of community networks and community network organizations worldwide. Community networks are locally-owned and operated digital infrastructure projects that provide connectivity, services, and digital autonomy to their communities.</p>\n<h2 id=\"europe\">Europe</h2><h3 id=\"germany\">Germany</h3><p><strong>Freifunk</strong></p>\n<ul>\n<li>Location: Nationwide (400+ local communities)</li>\n<li>Description: Non-commercial open grassroots initiative for free computer networks. Part of the international movement for wireless community networks.</li>\n<li>Size: 41,000+ access points across Germany</li>\n<li>Major communities: Münster, Aachen, Munich, Hanover, Stuttgart, Uelzen (1,000+ access points each)</li>\n<li>Website: <a href=\"https://freifunk.net/en/\" target=\"_blank\">https://freifunk.net/en/</a></li>\n<li>Technology: Mesh networks using special Linux firmware based on OpenWrt</li>\n<li>Founded: 2002</li>\n</ul>\n<h3 id=\"spain\">Spain</h3><p><strong>Guifi.net</strong></p>\n<ul>\n<li>Location: Catalonia and Valencian Community (expanding globally)</li>\n<li>Description: Free, open and neutral, mostly wireless community network</li>\n<li>Size: 37,000+ active nodes, 71,000+ km of wireless links</li>\n<li>Website: <a href=\"https://guifi.net\" target=\"_blank\">https://guifi.net</a></li>\n<li>Social Media: Active on various platforms</li>\n<li>Technology: Wireless and optical fiber links</li>\n<li>Founded: 2004</li>\n<li>Governance: Guifi.net Foundation, registered telecommunications operator</li>\n<li>Notable: Connected to Catalonia Neutral Internet Exchange Point (CATNIX)</li>\n</ul>\n<h3 id=\"greece\">Greece</h3><p><strong>Athens Wireless Metropolitan Network (AWMN)</strong></p>\n<ul>\n<li>Location: Athens and surrounding areas (110km N-S, 85km W-E)</li>\n<li>Description: Grassroots wireless community network</li>\n<li>Size: 1,120 backbone nodes, 2,900+ client computers</li>\n<li>Website: <a href=\"http://www.awmn.net\" target=\"_blank\">http://www.awmn.net</a></li>\n<li>Founded: 2002</li>\n<li>Notable: One of the most successful community networks in Europe</li>\n</ul>\n<p><strong>Sarantaporo.gr</strong></p>\n<ul>\n<li>Location: 15 villages around Mount Olympus, Greece</li>\n<li>Description: Non-profit organization providing Internet connectivity and technology education in rural Greece</li>\n<li>Website: <a href=\"https://sarantaporo.gr\" target=\"_blank\">https://sarantaporo.gr</a></li>\n<li>Founded: 2013</li>\n<li>Focus: Rural community revitalization through connectivity</li>\n<li>Partnership: University of Applied Sciences of Thessaly</li>\n</ul>\n<h3 id=\"italy\">Italy</h3><p><strong>Ninux</strong></p>\n<ul>\n<li>Location: Nationwide Italy (started in Rome)</li>\n<li>Description: Wireless community network, free, open and experimental</li>\n<li>Size: 352 active nodes, 2,216 planned nodes</li>\n<li>Website: <a href=\"http://ninux.org\" target=\"_blank\">http://ninux.org</a></li>\n<li>Social Media: Active on Telegram, IRC #ninux</li>\n<li>Founded: 2002</li>\n<li>Technology: Mesh networks, member of NaMeX Internet Exchange Point in Rome</li>\n</ul>\n<h3 id=\"slovenia\">Slovenia</h3><p><strong>wlan slovenija</strong></p>\n<ul>\n<li>Location: Slovenia (extending to Croatia and Austria)</li>\n<li>Description: Open wireless network initiative for free community wireless network</li>\n<li>Size: 400+ wireless nodes, 2+ million non-unique usages</li>\n<li>Website: <a href=\"https://wlan.si\" target=\"_blank\">https://wlan.si</a></li>\n<li>Founded: 2006</li>\n<li>Technology: IEEE 802.11 standards, open source software and hardware</li>\n</ul>\n<h3 id=\"netherlands\">Netherlands</h3><p><strong>Wireless Leiden</strong></p>\n<ul>\n<li>Location: Leiden and surrounding areas (Amsterdam to Delft)</li>\n<li>Description: Nonprofit organization providing open-source wireless networking</li>\n<li>Size: 110+ wireless servers</li>\n<li>Website: <a href=\"https://wirelessleiden.nl\" target=\"_blank\">https://wirelessleiden.nl</a></li>\n<li>Founded: 2002</li>\n<li>Technology: True wireless network using open source software</li>\n<li>Notable: One of the most advanced community networks globally</li>\n</ul>\n<h3 id=\"ireland\">Ireland</h3><p><strong>CRCWN (Cavan Rural Community Wireless Network)</strong></p>\n<ul>\n<li>Location: Cavan, Ireland</li>\n<li>Description: Free Wi-Fi and Fixed Wireless Internet</li>\n<li>Website: <a href=\"http://www.crcwn.online\" target=\"_blank\">www.crcwn.online</a></li>\n</ul>\n<h2 id=\"north-america\">North America</h2><h3 id=\"united-states\">United States</h3><p><strong>NYC Mesh</strong></p>\n<ul>\n<li>Location: New York City (all five boroughs)</li>\n<li>Description: Physical network of interconnected routers and community of enthusiasts</li>\n<li>Size: 2,000+ active member nodes</li>\n<li>Website: <a href=\"https://nycmesh.net\" target=\"_blank\">https://nycmesh.net</a></li>\n<li>Technology: Mesh technology with OSPF routing</li>\n<li>Governance: Network Commons License</li>\n<li>Founded: Based on similar projects globally</li>\n</ul>\n<p><strong>People&#39;s Open Network</strong></p>\n<ul>\n<li>Location: Oakland, California</li>\n<li>Description: Local, community-owned Internet embodying freedom and trust</li>\n<li>Website: <a href=\"https://peoplesopen.net\" target=\"_blank\">https://peoplesopen.net</a></li>\n<li>Technology: Mesh networking</li>\n<li>Focus: Community-owned infrastructure in Oakland</li>\n</ul>\n<p><strong>Personal Telco Project (PTP)</strong></p>\n<ul>\n<li>Location: Portland, Oregon</li>\n<li>Description: Wireless community network project</li>\n<li>Website: <a href=\"https://personaltelco.net\" target=\"_blank\">https://personaltelco.net</a></li>\n<li>Founded: November 2000 by Adam Shand</li>\n<li>Status: 501(c)(3) nonprofit organization</li>\n<li>Services: Free Internet access at hotspots including Pioneer Courthouse Square, public parks, restaurants and coffee shops</li>\n</ul>\n<h3 id=\"canada\">Canada</h3><p><strong>Toronto Mesh</strong></p>\n<ul>\n<li>Location: Toronto, Ontario</li>\n<li>Description: Grassroots and decentralized group building community-owned infrastructure</li>\n<li>Website: <a href=\"https://tomesh.net\" target=\"_blank\">https://tomesh.net</a></li>\n<li>Founded: 2016 at CivicTechTO</li>\n<li>Technology: Off-the-shelf hardware and open-source technology</li>\n<li>Focus: Addressing barriers to internet access, digital literacy and privacy</li>\n</ul>\n<h2 id=\"latin-america\">Latin America</h2><h3 id=\"brazil\">Brazil</h3><p><strong>Portal Sem Porteiras (PSP)</strong></p>\n<ul>\n<li>Location: Bairro dos Souzas, Monteiro Lobato, São Paulo</li>\n<li>Description: Community internet network focused on democratization of media and alternative information access</li>\n<li>Website: <a href=\"https://portalsemporteiras.github.io\" target=\"_blank\">https://portalsemporteiras.github.io</a></li>\n<li>Social Media: Various platforms for local content</li>\n<li>Founded: 2018 (meetings started 2016)</li>\n<li>Organization: Portal sem Porteiras Association</li>\n<li>Support: Coolab partnership</li>\n<li>Special Projects: Nodecast podcast, feminist cartography, local applications</li>\n</ul>\n<h3 id=\"colombia\">Colombia</h3><p><strong>Colnodo &amp; RedINC Network</strong></p>\n<ul>\n<li>Location: Bogotá (Colnodo HQ), Buenos Aires, Cauca (RedINC deployment)</li>\n<li>Description: Organization leading social initiatives around strategic use of Internet; RedINC connects Indigenous, Black, and peasant communities</li>\n<li>Website: <a href=\"https://colnodo.apc.org\" target=\"_blank\">https://colnodo.apc.org</a></li>\n<li>Technology: GSM cellular community network (2G), Wi-Fi</li>\n<li>Target: Ex-combatants, ethnic groups, rural communities</li>\n<li>Partners: Internet Society, Association for Progressive Communications (APC), Rhizomatica</li>\n</ul>\n<h3 id=\"argentina\">Argentina</h3><p><strong>Altermundi</strong></p>\n<ul>\n<li>Location: Nationwide Argentina</li>\n<li>Description: NGO working to bring affordable connectivity to all parts of Argentina</li>\n<li>Current Project: &quot;Seedbed&quot; project with 16 communities nationwide</li>\n<li>Technology: LibreRouter nodes</li>\n<li>Focus: Training communities, government funding assistance (Roberto Arias Program)</li>\n<li>Partners: APC, Nodo TAU</li>\n</ul>\n<h3 id=\"mexico\">Mexico</h3><p><strong>Rhizomatica</strong></p>\n<ul>\n<li>Location: Rural Oaxaca</li>\n<li>Description: Not-for-profit organization operating community-owned GSM networks</li>\n<li>Size: 16 connected communities (approximately 5,000 people each as of 2017)</li>\n<li>Website: <a href=\"https://rhizomatica.org\" target=\"_blank\">https://rhizomatica.org</a></li>\n<li>Technology: Independent GSM network, 2G cellular</li>\n<li>Founded: 2014 (first deployment)</li>\n<li>Focus: Rural, economically disadvantaged communities</li>\n</ul>\n<h2 id=\"africa\">Africa</h2><h3 id=\"south-africa\">South Africa</h3><p><strong>Zenzeleni Community Networks</strong></p>\n<ul>\n<li>Location: Eastern Cape (Mankosi and expanding)</li>\n<li>Description: Community-owned wireless internet service provider</li>\n<li>Size: 13,000+ people connected, 10+ institutions</li>\n<li>Website: <a href=\"https://zenzeleni.net\" target=\"_blank\">https://zenzeleni.net</a></li>\n<li>Contact: +27 (0) 85 301 2222, <a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a></li>\n<li>Organization: Cooperative model (Zenzeleni Networks Mankosi Co-op Ltd)</li>\n<li>Technology: Solar-powered Wi-Fi network</li>\n<li>Founded: 2012</li>\n<li>Notable: South Africa&#39;s first cooperative-owned ISP, 100% Black, 40% women-owned</li>\n<li>Pricing: Up to 20 times lower than existing operators</li>\n</ul>\n<h3 id=\"kenya\">Kenya</h3><p><strong>TunapandaNET</strong></p>\n<ul>\n<li>Location: Kibera slum, Nairobi</li>\n<li>Description: Urban community network improving education, health, and business</li>\n<li>Size: 1,800+ youth reached initially, expanded to 14 nodes</li>\n<li>Organization: Tunapanda Institute</li>\n<li>Technology: 2.4 GHz and 5.8GHz unlicensed spectrum</li>\n<li>Partners: Internet Society, International Centre of Theoretical Physics, Rhinotivity</li>\n<li>Founded: 2016</li>\n<li>Services: Connects schools, youth centers, women&#39;s centers</li>\n</ul>\n<h2 id=\"asia\">Asia</h2><h3 id=\"nepal\">Nepal</h3><p><strong>Nepal Wireless Networking Project</strong></p>\n<ul>\n<li>Location: Nationwide Nepal (rural areas)</li>\n<li>Description: Social enterprise providing connectivity to villages through community Wi-Fi projects</li>\n<li>Size: 200+ rural hamlets connected</li>\n<li>Founder: Mahabir Pun (Internet Hall of Fame recipient, Ramon Magsaysay Award winner)</li>\n<li>Website: <a href=\"http://nepwireless.net\" target=\"_blank\">http://nepwireless.net</a></li>\n<li>Technology: 2.4 GHz and 5.8 GHz frequencies, Motorola Canopy Radios</li>\n<li>Founded: 1996 (first connection 2002)</li>\n<li>Services: Telemedicine, education, smart village initiatives</li>\n<li>Projects: W4C Nepal (post-earthquake), RUCCESS</li>\n</ul>\n<h2 id=\"oceania\">Oceania</h2><h3 id=\"australia\">Australia</h3><p><strong>Melbourne Wireless</strong></p>\n<ul>\n<li>Location: Melbourne, Victoria</li>\n<li>Description: Community wireless network</li>\n<li>Website: <a href=\"https://melbournewireless.org.au\" target=\"_blank\">https://melbournewireless.org.au</a></li>\n</ul>\n<p><strong>Air Stream Wireless</strong></p>\n<ul>\n<li>Location: Australia</li>\n<li>Description: Community wireless network</li>\n</ul>\n<p><strong>TasWireless</strong></p>\n<ul>\n<li>Location: Tasmania, Australia</li>\n<li>Description: Community wireless network</li>\n</ul>\n<h2 id=\"community-network-organizations-and-support\">Community Network Organizations and Support</h2><h3 id=\"global-organizations\">Global Organizations</h3><p><strong>Association for Progressive Communications (APC)</strong></p>\n<ul>\n<li>Website: <a href=\"https://www.apc.org\" target=\"_blank\">https://www.apc.org</a></li>\n<li>Description: Global network supporting community networks worldwide</li>\n<li>Services: Funding, technical support, policy advocacy</li>\n</ul>\n<p><strong>Internet Society</strong></p>\n<ul>\n<li>Website: <a href=\"https://www.internetsociety.org\" target=\"_blank\">https://www.internetsociety.org</a></li>\n<li>Description: Global organization supporting community network development</li>\n<li>Programs: Beyond the Net grants, Community Networks program</li>\n</ul>\n<p><strong>Community Networks Organization</strong></p>\n<ul>\n<li>Website: <a href=\"https://communitynetworks.org\" target=\"_blank\">https://communitynetworks.org</a></li>\n<li>Description: Organization focused on community broadband and digital equity</li>\n</ul>\n<h3 id=\"regional-networks-and-federations\">Regional Networks and Federations</h3><p><strong>FFDN (Fédération FDN)</strong></p>\n<ul>\n<li>Location: France and francophone countries</li>\n<li>Website: <a href=\"https://db.ffdn.org\" target=\"_blank\">https://db.ffdn.org</a></li>\n<li>Description: Federation of francophone non-profit internet access providers</li>\n<li>Database: <a href=\"https://db.ffdn.org/isp/\" target=\"_blank\">https://db.ffdn.org/isp/</a> (API available)</li>\n</ul>\n<h2 id=\"research-and-academic-resources\">Research and Academic Resources</h2><h3 id=\"academic-papers-and-studies\">Academic Papers and Studies</h3><ul>\n<li>&quot;Toward Smart Community Networks&quot; (IEEE, 2023)[2]</li>\n<li>Various academic studies on community network governance, sustainability, and impact</li>\n</ul>\n<h3 id=\"databases-and-directories\">Databases and Directories</h3><ul>\n<li>Wikipedia List of Wireless Community Networks by Region[9]</li>\n<li>Community Networks Group Directory[7]</li>\n<li>FFDN Database (francophone networks)[7]</li>\n</ul>\n<h2 id=\"technology-and-standards\">Technology and Standards</h2><h3 id=\"common-technologies-used\">Common Technologies Used</h3><ul>\n<li>IEEE 802.11 (Wi-Fi) standards</li>\n<li>Mesh networking protocols (OLSR, B.A.T.M.A.N.)</li>\n<li>OpenWrt firmware</li>\n<li>Unlicensed spectrum bands (2.4 GHz, 5.8 GHz)</li>\n<li>GSM/cellular for rural areas</li>\n<li>Hybrid solutions combining wireless and fiber</li>\n</ul>\n<h3 id=\"governance-models\">Governance Models</h3><ul>\n<li>Wireless Commons License</li>\n<li>Network Commons License (NYC Mesh)</li>\n<li>Picopeering Agreement</li>\n<li>Cooperative ownership models</li>\n<li>Non-profit organizations</li>\n<li>Informal community governance</li>\n</ul>\n<h2 id=\"notes\">Notes</h2><ul>\n<li>This directory includes both wireless community networks and community network organizations</li>\n<li>Sizes and statistics are approximate and based on available data from various time periods</li>\n<li>Many networks are constantly evolving and expanding</li>\n<li>Some historical networks may no longer be active</li>\n<li>Contact information should be verified before use</li>\n<li>This list represents major and documented networks but is not exhaustive</li>\n</ul>\n<h2 id=\"sources\">Sources</h2><p>Information compiled from academic papers, organizational websites, Internet Society reports, Association for Progressive Communications documentation, and community network databases. Data verified through multiple sources where possible.</p>\n<p>Last updated: May 2025</p>\n"}, "datasheets/hardware.csv": {"hash": "2a5f58c47a12b6e0e79fd39d5fa65aa7", "content": "<p>Attribute / Platform,Zimaboard (832/v2),Raspberry Pi 5,Intel NUC (N100/N5105)<br>CPU/Arch,&quot;Intel Celeron N3450 (832) / N150 (v2), x86&quot;,&quot;Broadcom BCM2712, ARM Cortex-A76&quot;,&quot;Intel N100 (Alder Lake-N) / N5105, x86&quot;<br>RAM,&quot;8GB LPDDR4/5 (soldered; non-upgradable)&quot;,&quot;2/4/8/16GB LPDDR4X (soldered; non-upgradable)&quot;,&quot;8/16GB+ DDR4/5 (SODIMM; upgradeable)&quot;<br>Storage Expansion,&quot;2x SATA, 1x PCIe x4, 2x USB3&quot;,&quot;PCIe x1 via FPC (NVMe HAT), 2x USB3, microSD&quot;,&quot;1x M.2 NVMe, 1x 2.5&quot;&quot; SATA, several USB3&quot;<br>Network,&quot;Dual GbE (832), Dual 2.5GbE (v2), no wifi onboard&quot;,&quot;GbE, onboard WiFi 5, Bluetooth 5&quot;,&quot;1/2x 2.5GbE, often WiFi 6/6E, BT 5.x&quot;<br>Wireless Options,&quot;PCIe or USB WiFi card required&quot;,&quot;Built-in WiFi 5 (dual band); stable but not WiFi 6&quot;,&quot;M.2 WiFi/BT (upgradeable), often WiFi 6/6E&quot;<br>Purchase Cost (board/typical kit, USD),&quot;$90–$200 / $120–$230&quot;,&quot;$50–$120 board / $100–$200+ kit&quot;,&quot;$200–$300+ typical kit&quot;<br>Availability, Global South,&quot;Direct ship, limited local distros&quot;,&quot;Many local resellers, easy import&quot;,&quot;Mostly direct ship, some global distributors (Intel/ASUS)&quot;<br>Power Idle / Max (W),&quot;<del>3–4 / 14 (832), ~10 / 40 (v2)&quot;,&quot;</del>2.4–3.3 / ~12&quot;,&quot;6–12 / 20–30 (varies by vendor)&quot;<br>Power Input,&quot;12V DC barrel&quot;,&quot;USB-C PD (5V/5A, needs strong PSU)&quot;,&quot;12V or 19V DC, PSU included&quot;<br>Off-grid Suitability,&quot;Excellent (832); needs stable DC&quot;,&quot;Good (esp. 2GB model); PSU less standard&quot;,&quot;Needs tuning/choice to reach low idle&quot;<br>NAS/File Serve/Portal Performance,&quot;Good, native SATA, up to 900MB/s (SATA SSD)&quot;,&quot;Good, NVMe via HAT, up to 900MB/s&quot;,&quot;Excellent (NVMe or SATA native)&quot;<br>Media Transcoding (Jellyfin/Plex),&quot;QSV (H.264/HEVC), up to 2–4 (4K) streams with VAAPI&quot;,&quot;No HW encoding, direct play only&quot;,&quot;QSV (H.264/HEVC/AV1) 3–5+ 4K streams&quot;<br>Max Users Supported,&quot;10–20 concurrent typical loads&quot;,&quot;10–20 portal, 1 direct video&quot;,&quot;30+ portal/NAS, 4–6 video streams&quot;<br>Thermal Management,&quot;Passive, can throttle on heavy load/hot room&quot;,&quot;Active cooling required under load&quot;,&quot;Fanless or fan, may throttle fanless&quot;<br>Physical Robustness,&quot;Solid alum. case, small, needs DIY IP protec&quot;,&quot;Bare PCB, needs external case (many available)&quot;,&quot;Small sturdy case, options vary by brand&quot;<br>Repairability,&quot;Peripherals/cables replaceable, RAM/cpu not&quot;,&quot;Peripherals only, board not&quot;,&quot;RAM/SSD easy, rest proprietary&quot;<br>Expandability,&quot;PCIe slot enables (multi)-GbE, NVMe, WiFi&quot;,&quot;PCIe x1: NVMe, some USB4, basic&quot;,&quot;RAM/storage, WiFi, often 2.5GbE native&quot;<br>Hardware Openness,&quot;Software &amp; interface open, not schematics&quot;,&quot;Software GPIO docs open, not HW&quot;,&quot;Fully proprietary, closed HW&quot;<br>Community Support,&quot;Med+ (forums, Discord, some in Spanish/Chinese)&quot;,&quot;Large, global, many languages&quot;,&quot;Large, but less non-expert content&quot;<br>Docs/Manuals,&quot;Official, multi-lang, focused on CasaOS&quot;,&quot;Excellent, many community guides&quot;,&quot;Good (EN), varies by brand/model&quot;<br>Adoption/Field Use (rural),&quot;Mainly home-lab, few rural/digital inclusion docs&quot;,&quot;Many CCCI/community/low-resource&quot;,&quot;Office/home, little rural CCCI&quot;<br>Security/Intel ME,&quot;Standard x86, AMI BIOS, QSV, no ME (N3450)&quot;,&quot;No equivalent intel ME&quot;,&quot;Intel ME/CSME (security/privacy issues)&quot;<br>Best Use-case,&quot;CCCI w/ media streams, expand storage/NIC; low idle&quot;,&quot;Affordable CCCI, community, docs, portal (not transcoding)&quot;,&quot;Highest perf., advanced NAS, transcoding, virtual.&quot;</p>\n"}, "datasheets/software.csv": {"hash": "33a6c25e8601d7327dbcb770253276f7", "content": "<p>Section,Feature/Dimension,CasaOS,CapRover,Yunohost,ZimaOS,<PERSON><PERSON> (openBalena)</p>\n<p>Overall Suitability Scores,Feature,CasaOS,CapRover,Yunohost,ZimaOS,<PERSON>lena (openBalena)<br>, Ease of Setup,5,3,4,5,2<br>, Ease of Maintenance,4,3,4,4,3<br>, Low-Resource HW Suitability,5,3,4,5,4<br>, Resilience (Updates/Recovery),2,3,3,4,5<br>, Scalability (Stateless HA),1,4,1,1,1<br>, Scalability (Stateful HA),1,1,1,1,1<br>, Service Variety (CCCI),4,4,5,4,3<br>, Community/Support Access,4,3,5,3,4<br>, Overall CCCI Suitability,4,2.5,4,4,2.5</p>\n<p>Suitability Matrix,Dimension,CasaOS,CapRover,Yunohost,ZimaOS,Balena (openBalena)<br>, Ease of Setup,High,Medium,High,High,Low<br>, Ease of Maintenance,Medium,Medium,High,Medium,Medium<br>, Low-Resource HW Suitability,High,Medium,High,High,Medium<br>, Resilience (Updates/Recovery),Low,Medium,Medium,High,High<br>, Scalability (Stateless HA),Low,High,Low,Low,Low<br>, Scalability (Stateful HA),Low,Low,Low,Low,Low<br>, Relevant App Access (CCCI),Medium,Medium,High,Medium,Medium<br>, Multi-User Support,Low,Low,High,Low,Low<br>, Community/Docs Access,High,Medium,High,Medium,High</p>\n<p>Installation and Setup,Platform,Min HW,CPU Arch,Install Method,Required Skill,Setup Time<br>,,400MB+ RAM,amd64/arm64/armv7,Script (curl),Beginner,Fast (&lt;30min)<br>,,1GB+ RAM,amd64/arm64/armv7,Docker/UI,Familiar,Moderate<br>,,512MB+ RAM,x86_64/ARM,Image/Script,Familiar,Moderate<br>,,400MB+ RAM (est),x86_64,Pre-installed/Image,Beginner,Fast (&lt;30min)<br>,,512MB+ RAM,x86_64/ARM,Image,Familiar,Moderate</p>\n<p>Service/App Availability,Platform,Official Store,Community Stores,Key Apps,Custom App Method,Isolation Tech<br>,,Yes,Yes (Multiple),&quot;JF, FB, PiH, SpT (Comm.); WP, Kol, AzC (?)&quot;,UI (Compose/Run),Docker<br>,,Yes (Official+Comm),Yes (CaproverHub),&quot;WP, JF, PiH, AzC (Comm.); FB, Kol, SpT (Img)&quot;,UI (Image), CLI,Docker Swarm<br>,,Yes (Official+Comm),No,&quot;WP, JF, FB, Kol, AzC, PiH, LS; SpT (No)&quot;,Packaging, Manual,Systemd/User<br>,,Yes (via CasaOS),Yes (via CasaOS),Mirrors CasaOS,UI (Compose/Run),Docker<br>,,No (balenaHub),No,&quot;PiH (Comm.); WP, JF, FB, Kol, AzC, SpT (Img)&quot;,CLI/Git (Compose),Docker</p>\n<p>Usability/Docs,Platform,UI Style/Focus,Admin UI Features,User Mgmt,Doc Quality/Access,UI Languages<br>,,Simple, Home Cloud,App Install, Widgets, File Mgmt,No (Community Workarounds),Wiki (EN, Comm.),Multi (EN, ZH, Comm.)<br>,,PaaS, Developer Focus,App Deploy, Domain/SSL, Basic Docker,No,Website (EN),EN only<br>,,Integrated Admin,Full Server Mgmt, Apps, Users, Backup,Yes (LDAP/SSO, Groups, Perms),Wiki (Multi-lang),Multi (EN, FR, ES+)<br>,,Simple, Home Cloud,App Install, Widgets, File/Share Mgmt,Limited (Samba Members),Website (EN, Comm.),Multi (EN, ZH, JP+)<br>,,Fleet Mgmt (IoT/Edge),Device Mgmt, Deploy, Monitor (Cloud),Cloud: Yes; openBalena: No,Website (EN),EN only</p>\n<p>Perf./Resource Use,Platform,Baseline RAM (Est.),Baseline CPU (Est.),Min RAM (Practical),SBC Perf. (RPi),Built-in Monitoring<br>,,Low (&lt;100MB?),Low,512MB - 1GB,Good,Basic Widgets<br>,,Moderate (100MB+?),Low-Moderate,1GB+,Okay (Needs Swap),Netdata (Detailed)<br>,,Low-Moderate,Low,512MB - 1GB,Okay,Basic OS Tools<br>,,Low (&lt;100MB?),Low,512MB - 1GB,Good (Optimized),btop Widget<br>,,Moderate (OS+Agent),Low-Moderate,1GB+,Good (Optimized),Cloud Dash / Custom</p>\n<p>Networking,Platform,Local DNS/mDNS,Custom Domain,Integrated SSL (LE),App Network Isolation,Built-in Firewall<br>,,IP /.local,Via Proxy,Via Proxy,Docker Network,No (Host OS)<br>,,Service Name,Yes (Built-in),Yes (Built-in),Docker Swarm Network,No (Host OS)<br>,,.local (mDNS),Yes (Built-in),Yes (Built-in),Host Network + FW,Yes (yunohost firewall)<br>,,IP /.local,Via Proxy,Via Proxy,Docker Network,No (Host OS)<br>,,Via Container,Via Proxy,Via Proxy,Docker Network,No (Host OS)</p>\n<p>Update/Backup/Recovery,Platform,System Update,App Update,Backup Method,Backup Scope,Restore Process,Resilience Features<br>,,UI/CLI Script,App Store / Manual,Manual,Config?, Data (Manual),Manual,Host OS Dependent<br>,,CLI (serverupdate),Redeploy / UI,Built-in (Partial),Config, SSL (No Data),Manual Steps,Docker Swarm (Apps)<br>,,UI/CLI (upgrade),UI/CLI (upgrade),Built-in,Config, Data, Apps,UI/CLI,Backup System<br>,,OTA / Offline,App Store / Manual,Manual,Config?, Data (Manual),Manual,Dual-Partition Rollback<br>,,Remote OTA (Delta),Remote Push (Delta),Manual (App Data),OS/App Code,Redeploy,Atomic Updates, Rollback</p>\n<p>Security/Permissions,Platform,Default Firewall,Access Control (User Model),Permissions Granularity,Patching Method,Key Security Features<br>,,Host OS (Manual),Single Admin (Default),Low (App/Share Level),Host OS + CasaOS Updates,Docker Isolation<br>,,Host OS (Manual),Single Admin,Low (Admin Only),CapRover Updates,Integrated HTTPS, Swarm Net<br>,,Integrated Mgmt,Multi-User (LDAP/Groups),High (Apps, SSH, SFTP),System + App Updates,Fail2ban, Firewall Mgmt, SSO<br>,,Host OS (Manual),Single Admin (Likely),Low (Samba Level),ZimaOS OTA Updates,Docker Isolation, Dual-Boot<br>,,Host OS / Docker,Multi-User (Cloud); Single (Open),High (Cloud Roles),Balena OTA Updates,Minimal OS, Container Isolation</p>\n<p>Clustering/Scaling,Platform,Native Clustering,Clustering Tech,HA (Stateless),HA (Stateful),HA Manager,Load Balancing,Setup Ease<br>,,No,Manual Swarm?,No (Manual),No (Manual),No,No (Manual),Very Hard<br>,,Yes,Docker Swarm,Yes (Built-in),No (Manual),No (Manual),Yes (Built-in),Moderate<br>,,No,N/A,No,No,No,No,N/A<br>,,No,N/A,No,No,No,No,N/A<br>,,No (Fleet Mgmt),N/A,No (Manual/App),No (Manual/App),N/A,No (Manual/App),N/A</p>\n<p>Community/Sustainability,Platform,Primary Maintainer(s),GitHub Activity (Core),Forum/Chat Activity,Doc Quality/Access,CCCI/GS Reports<br>,,Community / IceWhale,High / Active,High (Discord/GH),Good Wiki (EN+),No<br>,,Individual / Small Team,Moderate / Less Recent,Moderate (GH Only),Good Website (EN),No<br>,,Community (Volunteer),High / Active,High (Forum/Chat),Excellent (Multi),No<br>,,IceWhale Technology,Moderate / Active,Moderate (Forum/Discord),Good Website (EN+),No<br>,,Balena (Company),High / Active,High (Forum),Excellent (EN),No</p>\n<p>Licensing,Platform,Core License,FOSS Compliance (OSI),Copyleft,Proprietary Modules/Concerns,Vendor Lock-in Risk<br>,,Apache 2.0,Yes,No,None identified,Low<br>,,Apache 2.0 + Appendix,No (Modified),Partial,License appendix restrictions,Low-Moderate<br>,,AGPL v3,Yes,Yes (Strong),None identified,Low<br>,,Apache 2.0? +?,Unclear (Potential Mix),Unclear,Potential (Hardware/Services),Moderate-High<br>,,AGPLv3 (openBalena) / Apache 2.0 (OS) / Proprietary (Cloud),Yes (Open Components),Yes (AGPL),BalenaCloud is proprietary,High (Cloud) / Low (Open)</p>\n"}}